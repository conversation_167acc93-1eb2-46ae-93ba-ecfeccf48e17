/**
 * Per-Shift Attendance Marking Test
 * 
 * This test verifies that attendance records are properly marked as paid
 * when per-shift payments are processed.
 */

// Test configuration
const TEST_CONFIG = {
  staffId: 'test-staff-123',
  testAttendanceIds: ['att-1', 'att-2', 'att-3'],
  testShiftData: {
    attendanceIds: ['att-1', 'att-2', 'att-3'],
    shiftBreakdown: [
      { shiftId: 'morning', shiftName: 'Morning Shift', count: 1, rate: 2500, amount: 2500 },
      { shiftId: 'afternoon', shiftName: 'Afternoon Shift', count: 1, rate: 2000, amount: 2000 },
      { shiftId: 'evening', shiftName: 'Evening Shift', count: 1, rate: 3000, amount: 3000 }
    ]
  }
};

/**
 * Test 1: Check Attendance Records Before Payment
 */
async function testAttendanceBeforePayment(staffId) {
  console.log('🔍 Test 1: Check Attendance Records Before Payment');
  
  try {
    // Import database operations
    const { getStaffAttendanceRecords } = await import('./lib/db/v4/operations/per-staff-ops.js');
    
    // Get attendance records
    const records = await getStaffAttendanceRecords(staffId, '2024-01-01', '2024-12-31');
    
    console.log(`📊 Found ${records.length} attendance records for staff ${staffId}`);
    
    // Check if any are already paid
    const paidRecords = records.filter(r => r.isPaid);
    const unpaidRecords = records.filter(r => !r.isPaid);
    
    console.log(`💰 Paid records: ${paidRecords.length}`);
    console.log(`⏳ Unpaid records: ${unpaidRecords.length}`);
    
    if (unpaidRecords.length > 0) {
      console.log('✅ Found unpaid records to test with');
      return unpaidRecords.slice(0, 3).map(r => r.id); // Return first 3 unpaid record IDs
    } else {
      console.log('⚠️ No unpaid records found - test may not be meaningful');
      return [];
    }
    
  } catch (error) {
    console.error('❌ Error checking attendance records:', error);
    return [];
  }
}

/**
 * Test 2: Create Per-Shift Payment and Check Attendance Marking
 */
async function testPerShiftPaymentWithAttendance(staffId, attendanceIds) {
  console.log('🔍 Test 2: Create Per-Shift Payment and Check Attendance Marking');
  
  if (attendanceIds.length === 0) {
    console.log('⚠️ No attendance IDs provided - skipping test');
    return null;
  }
  
  try {
    // Import the new balance service
    const { createPerShiftPaymentSnapshot } = await import('./lib/services/new-staff-balance-service.js');
    
    // Create test shift data
    const shiftData = {
      attendanceIds: attendanceIds,
      shiftBreakdown: attendanceIds.map((id, index) => ({
        shiftId: `shift-${index + 1}`,
        shiftName: `Test Shift ${index + 1}`,
        count: 1,
        rate: 2500,
        amount: 2500
      }))
    };
    
    console.log('💳 Creating per-shift payment snapshot...');
    
    // Create payment snapshot
    const snapshot = await createPerShiftPaymentSnapshot({
      staffId: staffId,
      shiftData: shiftData,
      useAllBonuses: false,
      useAllDeductions: false,
      useAllAdvances: false,
      notes: 'Test per-shift payment for attendance marking'
    });
    
    console.log('✅ Payment snapshot created:', {
      id: snapshot._id,
      netAmount: snapshot.netAmount,
      attendanceIds: snapshot.shiftData?.attendanceIds
    });
    
    return snapshot;
    
  } catch (error) {
    console.error('❌ Error creating per-shift payment:', error);
    return null;
  }
}

/**
 * Test 3: Verify Attendance Records Are Marked as Paid
 */
async function testAttendanceAfterPayment(staffId, attendanceIds) {
  console.log('🔍 Test 3: Verify Attendance Records Are Marked as Paid');
  
  if (attendanceIds.length === 0) {
    console.log('⚠️ No attendance IDs provided - skipping test');
    return false;
  }
  
  try {
    // Import database operations
    const { getStaffAttendanceRecords } = await import('./lib/db/v4/operations/per-staff-ops.js');
    
    // Get attendance records
    const records = await getStaffAttendanceRecords(staffId, '2024-01-01', '2024-12-31');
    
    // Check if the specific records are marked as paid
    let allMarkedAsPaid = true;
    let markedCount = 0;
    
    for (const attendanceId of attendanceIds) {
      const record = records.find(r => r.id === attendanceId);
      if (record) {
        if (record.isPaid) {
          markedCount++;
          console.log(`✅ Record ${attendanceId} is marked as paid`);
        } else {
          allMarkedAsPaid = false;
          console.log(`❌ Record ${attendanceId} is NOT marked as paid`);
        }
      } else {
        allMarkedAsPaid = false;
        console.log(`❌ Record ${attendanceId} not found`);
      }
    }
    
    console.log(`📊 Summary: ${markedCount}/${attendanceIds.length} records marked as paid`);
    
    if (allMarkedAsPaid) {
      console.log('✅ All attendance records properly marked as paid');
      return true;
    } else {
      console.log('❌ Some attendance records were not marked as paid');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Error verifying attendance records:', error);
    return false;
  }
}

/**
 * Test 4: Test Payment Deletion and Attendance Unmarking
 */
async function testPaymentDeletionAndUnmarking(snapshot, attendanceIds) {
  console.log('🔍 Test 4: Test Payment Deletion and Attendance Unmarking');
  
  if (!snapshot || attendanceIds.length === 0) {
    console.log('⚠️ No snapshot or attendance IDs provided - skipping test');
    return false;
  }
  
  try {
    // Import the delete function
    const { deletePaymentSnapshot } = await import('./lib/services/new-staff-balance-service.js');
    
    console.log('🗑️ Deleting payment snapshot...');
    
    // Delete the payment snapshot
    const deleted = await deletePaymentSnapshot(snapshot._id);
    
    if (deleted) {
      console.log('✅ Payment snapshot deleted successfully');
      
      // Wait a moment for database operations to complete
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if attendance records are unmarked
      const { getStaffAttendanceRecords } = await import('./lib/db/v4/operations/per-staff-ops.js');
      const records = await getStaffAttendanceRecords(snapshot.staffId, '2024-01-01', '2024-12-31');
      
      let allUnmarked = true;
      let unmarkedCount = 0;
      
      for (const attendanceId of attendanceIds) {
        const record = records.find(r => r.id === attendanceId);
        if (record) {
          if (!record.isPaid) {
            unmarkedCount++;
            console.log(`✅ Record ${attendanceId} is unmarked (unpaid)`);
          } else {
            allUnmarked = false;
            console.log(`❌ Record ${attendanceId} is still marked as paid`);
          }
        }
      }
      
      console.log(`📊 Summary: ${unmarkedCount}/${attendanceIds.length} records unmarked`);
      
      if (allUnmarked) {
        console.log('✅ All attendance records properly unmarked');
        return true;
      } else {
        console.log('❌ Some attendance records were not unmarked');
        return false;
      }
      
    } else {
      console.log('❌ Failed to delete payment snapshot');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Error testing payment deletion:', error);
    return false;
  }
}

/**
 * Run Full Attendance Marking Test
 */
async function runFullAttendanceTest(staffId = TEST_CONFIG.staffId) {
  console.log('🚀 Starting Per-Shift Attendance Marking Test\n');
  console.log('📋 Testing with staff ID:', staffId);
  console.log('=' .repeat(50));
  
  let attendanceIds = [];
  let snapshot = null;
  
  const tests = [
    {
      name: 'Check Attendance Before Payment',
      fn: async () => {
        attendanceIds = await testAttendanceBeforePayment(staffId);
        return attendanceIds.length > 0;
      }
    },
    {
      name: 'Create Per-Shift Payment',
      fn: async () => {
        snapshot = await testPerShiftPaymentWithAttendance(staffId, attendanceIds);
        return snapshot !== null;
      }
    },
    {
      name: 'Verify Attendance Marked as Paid',
      fn: async () => {
        return await testAttendanceAfterPayment(staffId, attendanceIds);
      }
    },
    {
      name: 'Test Payment Deletion and Unmarking',
      fn: async () => {
        return await testPaymentDeletionAndUnmarking(snapshot, attendanceIds);
      }
    }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      console.log('\n' + '-'.repeat(30));
      const result = await test.fn();
      if (result) {
        passed++;
        console.log(`✅ ${test.name}: PASSED`);
      } else {
        failed++;
        console.log(`❌ ${test.name}: FAILED`);
      }
    } catch (error) {
      failed++;
      console.log(`❌ ${test.name}: ERROR - ${error.message}`);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed! Attendance marking is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the implementation.');
  }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runFullAttendanceTest, testAttendanceBeforePayment, testPerShiftPaymentWithAttendance, testAttendanceAfterPayment, testPaymentDeletionAndUnmarking };
} else {
  window.PerShiftAttendanceTest = { runFullAttendanceTest, testAttendanceBeforePayment, testPerShiftPaymentWithAttendance, testAttendanceAfterPayment, testPaymentDeletionAndUnmarking };
}

// Auto-run if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  const staffId = process.argv[2] || TEST_CONFIG.staffId;
  runFullAttendanceTest(staffId).catch(console.error);
}
