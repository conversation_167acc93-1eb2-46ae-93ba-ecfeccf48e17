# 🖥️ Electron Development & Production Setup Guide

## 🔧 **Development Mode**

### Fixed Issues ✅
- **Problem**: Electron app couldn't connect to localhost:3000 
- **Solution**: Enhanced development workflow with proper server health checks
- **Problem**: Database lock errors (PouchDB/LevelDB LOCK files)
- **Solution**: Process cleanup and single-instance management

### New Development Commands

```bash
# 🚀 RECOMMENDED: Use the new improved dev script
npm run electron:dev

# 🧹 CLEANUP: If you encounter database lock errors
npm run electron:cleanup

# 🔄 Alternative: Use concurrent approach (if needed)
npm run electron:dev:concurrent

# ⚡ Safe mode with restart attempts
npm run electron:dev:safe
```

### What the New Script Does

1. **Cleanup Existing Processes**: Kills any running Electron/CouchDB processes for this project
2. **Remove Database Locks**: Deletes LOCK files that may prevent database access
3. **Starts Next.js Server**: Launches `npm run dev` 
4. **Health Check**: Waits for `http://localhost:3000` to be ready
5. **Builds Electron**: Compiles TypeScript in watch mode
6. **Launches Single Electron Instance**: Connects to the development server
7. **Cleanup on Exit**: <PERSON><PERSON><PERSON> terminates all processes and cleans up locks

### Development Architecture

```
┌─────────────────────┐    HTTP     ┌─────────────────────┐
│                     │   Request   │                     │
│   Electron App      │ ──────────► │   Next.js Server   │
│   (Desktop Window)  │             │   localhost:3000    │
│                     │             │                     │
└─────────────────────┘             └─────────────────────┘
        │
        ▼
┌─────────────────────┐
│                     │
│   PouchDB/LevelDB   │
│   (Local Database)  │
│                     │
└─────────────────────┘
```

---

## 🏭 **Production Mode**

### ⚠️ **IMPORTANT CLARIFICATION** ⚠️

The Electron app is designed for **OFFLINE-FIRST** operation, **NOT** to connect to a remote server like `bistro.icu`. Here's why:

### Production Architecture (Correct)

```
┌─────────────────────────────────────────────────────────────┐
│                    Electron App                             │
│  ┌─────────────────┐    ┌──────────────────────────────┐   │
│  │                 │    │                              │   │
│  │   Static Files  │    │        PouchDB               │   │
│  │   (Next.js      │    │      (Local Database)        │   │
│  │    Build)       │    │                              │   │
│  │                 │    │  ┌─────────────────────────┐ │   │
│  └─────────────────┘    │  │    Local CouchDB        │ │   │
│                         │  │     (for sync)          │ │   │
│                         │  └─────────────────────────┘ │   │
│                         └──────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Why Offline-First?

1. **Restaurant Operations**: Must work without internet 🔌
2. **POS Reliability**: Can't depend on network connectivity 📡  
3. **Data Security**: All transactional data stays local 🔒
4. **Performance**: No network latency for operations ⚡

### Production Build Process

```bash
# 1. Build static files for Electron
npm run build:electron

# 2. Build Electron app
npm run electron:build

# 3. Create distribution packages  
npm run electron:dist
```

### What Happens in Production

1. **Static Serving**: Electron serves the built Next.js app via `electron-serve`
2. **Local Database**: PouchDB with LevelDB for data storage
3. **Local CouchDB**: Embedded CouchDB server for sync (when needed)
4. **No External Server**: Everything runs locally on the device

---

## 🌐 **If You Need Remote Connectivity**

If you want to connect to `bistro.icu` or any remote server, you have these options:

### Option 1: API Endpoints (Recommended)
```typescript
// In your Next.js app, create API routes that connect to remote services
// app/api/sync/route.ts
export async function POST(request: Request) {
  // Sync local data with bistro.icu
  // This runs server-side in the Electron app
}
```

### Option 2: Hybrid Mode
```bash
# Use the hybrid development script
cd electron && npm run electron:start-hybrid
```

### Option 3: Custom Production Configuration
If you really need remote connectivity in production, you'd modify the Electron main process:

```typescript
// In electron/src/index.ts
if (!isDev) {
  // Instead of serving static files
  mainWindow.loadURL('https://bistro.icu');
}
```

**⚠️ Warning**: This breaks the offline-first architecture and requires constant internet.

---

## 🐛 **Troubleshooting**

### Database Lock Errors

If you see errors like:
```
IO error: lock /Users/<USER>/electron/pouchdb-data/.../LOCK: Resource temporarily unavailable
```

**Solution:**
```bash
# 🧹 Clean up all processes and locks
npm run electron:cleanup

# 🚀 Start fresh
npm run electron:dev
```

### Multiple Electron Instances

**Problem**: Multiple Electron processes trying to access the same database

**Check for conflicts:**
```bash
ps aux | grep -E "(electron|node)" | grep shop
```

**Solution:**
```bash
# Kill all Electron processes for this project
npm run electron:cleanup
```

### Development Issues

```bash
# If port 3000 is busy
lsof -ti:3000 | xargs kill -9

# If Electron fails to connect
npm run electron:dev:safe

# If build fails
cd electron && npm run build

# Complete cleanup and restart
npm run electron:cleanup && npm run electron:dev
```

### Production Issues

```bash
# Clean build
npm run build:electron
cd electron && rm -rf dist && npm run build

# Check Electron logs
# Look in: ~/Library/Logs/Shop/ (macOS)
```

---

## 📝 **Summary**

- **Development**: `npm run electron:dev` → Connects to localhost:3000 ✅
- **Cleanup**: `npm run electron:cleanup` → Fixes database locks ✅
- **Production**: Offline-first with static files and local database ✅  
- **Remote Server**: Use API routes or hybrid mode if needed ⚠️
- **Architecture**: Designed for restaurant POS reliability 🍽️

The Electron app is **not supposed** to connect to `bistro.icu` in production - it's designed to be fully self-contained for restaurant operations! 🎯

### 🎉 **Latest Fix**
The database lock issue has been resolved by implementing proper process management that:
- Prevents multiple Electron instances from running simultaneously
- Automatically cleans up database lock files on startup
- Provides robust cleanup commands for development
- Ensures single-instance database access

You should no longer see `LOCK: Resource temporarily unavailable` errors! 🚀 