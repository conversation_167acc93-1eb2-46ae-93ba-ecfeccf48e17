{"name": "bistro-electron", "productName": "Bistro", "version": "1.0.0", "description": "Bistro Electron App", "main": "dist/index.js", "scripts": {"build": "tsc", "watch": "tsc -w", "rebuild": "electron-rebuild", "rebuild:dev": "electron-rebuild --arch=arm64", "rebuild:win": "electron-rebuild --arch=x64 --platform=win32", "electron:start": "npm run build && electron .", "electron:dev": "npm run build && cross-env NODE_ENV=development ELECTRON_START_URL=http://localhost:3000 electron .", "electron:start:static": "npm run build && cross-env NODE_ENV=production ELECTRON_FORCE_STATIC=true electron .", "electron:start-live": "cross-env NODE_ENV=development ELECTRON_START_URL=http://localhost:3000 electron .", "electron:start-hybrid": "cd .. && npm run build && cd electron && npm run build && cross-env ELECTRON_OFFLINE_FALLBACK=true cross-env ELECTRON_START_URL=http://localhost:3000 electron .", "electron:build": "npm run build && electron-builder build", "electron:build:win": "npm run build && electron-builder build --win", "electron:build:win:slim": "npm run build && electron-builder build --win --config electron-builder-slim.js", "electron:build:win:publish": "npm run build && electron-builder build --win --publish always", "electron:build:mac": "npm run build && electron-builder build --mac", "electron:build:mac:slim": "npm run build && electron-builder build --mac --config electron-builder-mac-slim.js", "electron:build:mac:dmg": "npm run build && electron-builder build --mac --config electron-builder-mac-dmg-only.js", "dist": "electron-builder", "dist:win": "electron-builder --win", "dist:mac": "electron-builder --mac", "publish:mac": "npm run electron:build && cp release/*.dmg release/*.yml ../public/updates/", "preelectron:build:win": "npm prune --production && npx modclean --run --patterns=\"default:safe,default:caution\" --no-progress"}, "dependencies": {"@capacitor/core": "^5.0.0", "auto-launch": "^5.0.6", "axios": "^1.7.7", "bonjour": "^3.5.0", "bonjour-service": "^1.2.1", "cross-env": "^7.0.3", "dns-packet": "^5.6.1", "dns-txt": "^2.0.2", "electron-log": "^5.2.0", "electron-serve": "^1.1.0", "electron-store": "^10.0.0", "electron-updater": "^6.3.9", "express": "^4.21.1", "fs-extra": "^11.2.0", "ip": "^2.0.1", "multicast-dns": "^7.2.5", "multer": "^1.4.5-lts.1", "nano": "^10.1.4", "node-fetch": "^2.7.0", "pouchdb-browser": "^9.0.0", "pouchdb-find": "^9.0.0", "uuid": "^10.0.0", "get-port": "^7.1.0"}, "optionalDependencies": {"usb-detection": "^4.14.2"}, "devDependencies": {"@types/bonjour": "^3.5.13", "@types/electron": "^1.4.38", "@types/express": "^5.0.1", "@types/node": "^20.10.5", "@types/pouchdb-core": "^7.0.15", "@types/pouchdb-find": "^7.3.3", "@types/uuid": "^10.0.0", "electron": "^30.0.0", "electron-builder": "^24.13.3", "electron-rebuild": "^3.2.9", "typescript": "^5.3.3", "modclean": "^3.0.0-beta.1"}, "build": {"npmRebuild": false, "buildDependenciesFromSource": false, "nodeGypRebuild": false, "appId": "dev.bistro.app", "productName": "Bistro", "directories": {"output": "release/"}, "files": ["dist/**/*", "app/**/*", "config.json", "capacitor.config.json", "node_modules/electron-updater/**/*", "node_modules/nano/**/*", "node_modules/bonjour/**/*", "node_modules/bonjour-service/**/*", "node_modules/multicast-dns/**/*", "node_modules/dns-packet/**/*", "node_modules/dns-txt/**/*", "node_modules/ip/**/*", "node_modules/pouchdb-browser/**/*", "node_modules/pouchdb-find/**/*", "node_modules/get-port/**/*"], "asarUnpack": ["resources/couchdb-macos/**/*", "resources/couchdb-windows/**/*"], "extraResources": [{"from": "resources/couchdb-macos", "to": "couchdb-macos", "filter": ["**/*"]}, {"from": "resources/couchdb-windows", "to": "couchdb-windows", "filter": ["**/*"]}], "mac": {"category": "public.app-category.business", "target": ["dmg", "zip"]}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "resources/icon.ico", "publisherName": "Bistro Restaurant POS", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "perMachine": true, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": true, "artifactName": "bistro-${version}.${ext}"}, "publish": [{"provider": "generic", "url": "${R2_ENDPOINT}/${R2_BUCKET_NAME}/api/updates/check"}]}}