// P2P Sync module for Electron
// This file handles CouchDB Server, peer discovery via Bonjour, and database syncing

import { BrowserWindow, ipcMain, app } from 'electron';
// Remove express-pouchdb import
// import * as BonjourModule from 'bonjour'; // Changed to namespace import
// Fix: Use bonjour-service instead of bonjour
const BonjourService = require('bonjour-service');
import * as path from 'path';
import * as os from 'os';
import PouchDBConstructor from 'pouchdb-browser';
import PouchDBFind from 'pouchdb-find';
import * as fs from 'fs';
import * as http from 'http';
import { spawn, ChildProcess } from 'child_process';
import { 
  PREFERRED_COUCHDB_PORT, 
  FALLBACK_COUCHDB_PORTS, 
  ALL_COUCHDB_PORTS,
  getCouchDBUrl,
  getCouchDBAdminUrl 
} from './couchdb-config';

// Setup PouchDB plugins
PouchDBConstructor.plugin(PouchDBFind);

// Add PouchDB namespace for type definitions if needed
declare namespace PouchDB {
  namespace Replication {
    interface ReplicateOptions {
      live?: boolean;
      retry?: boolean;
      filter?: string | ((doc: any, params: any) => boolean);
      query_params?: any;
      view?: string;
      since?: number;
      timeout?: number;
      batch_size?: number;
      batches_limit?: number;
      back_off_function?: (delay: number) => number;
      checkpoint?: false | 'source' | 'target';
    }
  }
}

// Types
interface PeerInfo {
  id: string;
  ip: string;
  port: number;
  hostname: string;
  platform: string;
}

interface SyncStatus {
  peerId: string;
  dbName: string;
  direction: 'push' | 'pull' | 'both';
  status: 'active' | 'paused' | 'error' | 'complete';
  error?: string;
  progress?: {
    docsPushed: number;
    docsPulled: number;
    totalDocs: number;
  };
}

// Service name for Bonjour discovery
const SERVICE_TYPE = 'http'; // Simplified to match working test script

// Global state
let couchdbProcess: ChildProcess | null = null;
let bonjour: any = null;
let serverPort: number = PREFERRED_COUCHDB_PORT;
let localDeviceId: string = '';
let mainWindowRef: BrowserWindow | null = null;
let dbRootPathRef: string = '';
let mdnsStatus: 'not_running' | 'running' | 'error' = 'not_running';

// Map to track active sync replications
const activeSyncs: Map<string, any> = new Map();

// Map to track discovered peers
const peers: Map<string, PeerInfo> = new Map();

// Array to track sync statuses
const syncStatuses: SyncStatus[] = [];

const COUCHDB_CONFIG_FILENAME = 'local.ini';
let lastServiceInfo: any = null;

// Utility function to safely send logs to renderer without crashing the main process.
// Added robust guards because in production the BrowserWindow might be reloaded or
// already destroyed while background P2P operations continue emitting logs. Trying
// to access a destroyed webContents throws the fatal "Object has been destroyed"
// error that crashes the whole Electron main process (see issue reports on macOS &
// Windows releases). We therefore:
// 1. Check both the window and its webContents for existence & non-destruction.
// 2. Fallback to console output when the renderer is not available.
function sendP2PLog(message: string) {
  try {
    if (
      mainWindowRef &&
      !mainWindowRef.isDestroyed() &&
      mainWindowRef.webContents &&
      !mainWindowRef.webContents.isDestroyed()
    ) {
      mainWindowRef.webContents.send('p2p-log', message);
    } else {
      // Renderer not ready → simply log to main process console to avoid crashes.
      console.log(`[P2P] ${message}`);
    }
  } catch (error) {
    // Extra safety: never let logging itself crash the app.
    console.warn('[p2p-sync.ts] sendP2PLog failed (safely swallowed):', error);
  }
}

/**
 * Initialize the P2P sync system
 */
export async function initP2PSync(dbRootPath: string, mainWindow: Electron.BrowserWindow, deviceId: string) {
  console.log('[p2p-sync.ts] Attempting to initialize P2P Sync with device ID:', deviceId);
  
  // Store references
  mainWindowRef = mainWindow;
  localDeviceId = deviceId;
  dbRootPathRef = dbRootPath;
  
  // Register IPC handlers FIRST, before any other initialization
  // This ensures they're always available even if other parts fail
  try {
    setupP2PSyncIpcHandlers();
    console.log('[p2p-sync.ts] ✅ P2P Sync IPC handlers registered');
    sendP2PLog(`✅ P2P Sync IPC handlers configured`);
  } catch (error) {
    console.error('[p2p-sync.ts] ❌ Failed to register IPC handlers:', error);
    // Continue anyway, but log the error
  }
  
  // 🚨 CRITICAL FIX: Make P2P sync initialization non-blocking
  // Don't let P2P sync failures prevent the main app from loading
  const initializeP2PAsync = async () => {
    try {
      mdnsStatus = 'not_running';
      // Test the sendP2PLog at the beginning to verify the pipeline
      sendP2PLog(`🔄 Starting P2P Sync initialization with device ID: ${deviceId}`);
      sendP2PLog(`📱 Device ID: ${deviceId}`); // Add explicit device ID log for UI parsing
      
      // Add timeout to CouchDB startup to prevent hanging
      const couchdbStartPromise = startCouchDBServer(dbRootPath);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('CouchDB startup timeout after 30 seconds')), 30000);
      });
      
      await Promise.race([couchdbStartPromise, timeoutPromise]);
      sendP2PLog(`✅ CouchDB server started successfully on port: ${serverPort}`);
      sendP2PLog(`📡 Service port: ${serverPort}`);
      
      const bonjourStarted = await startBonjourDiscovery();
      if (bonjourStarted) {
        mdnsStatus = 'running';
        sendP2PLog(`✅ Bonjour discovery started successfully - watching for peers`);
      }
      
      // Additional diagnostics for debugging
      sendP2PLog(`📊 P2P System State - mdnsStatus: ${mdnsStatus}, peers: ${peers.size}, deviceId: ${deviceId}`);
      
      console.log(`[p2p-sync.ts] ✅ P2P Sync initialized successfully on port ${serverPort}`);
      sendP2PLog(`✅ P2P Sync initialized successfully on port ${serverPort}`);
      return true;
    } catch (error) {
      console.error('[p2p-sync.ts] ❌ Failed to initialize P2P sync:', error);
      sendP2PLog(`❌ Failed to initialize P2P sync: ${error}`);
      mdnsStatus = 'error';
      // Don't cleanup if only partial initialization failed - keep IPC handlers working
      return false;
    }
  };
  
  // Run P2P initialization in background - don't block main app
  initializeP2PAsync().catch(error => {
    console.error('[p2p-sync.ts] ❌ Background P2P initialization failed:', error);
    sendP2PLog(`❌ Background P2P initialization failed: ${error}`);
  });
  
  // Return immediately with IPC handlers ready
  console.log('[p2p-sync.ts] ✅ P2P Sync IPC ready, initialization continuing in background');
  return true;
}

/**
 * Start the CouchDB Server to expose databases over HTTP
 */
async function startCouchDBServer(dbRootPath: string) {
  console.log('[p2p-sync.ts] Starting CouchDB server...');
  
  try {
    // 🚀 ENHANCEMENT: Different behavior for dev vs prod
    const isDev = process.env.NODE_ENV === 'development' && process.env.ELECTRON_FORCE_STATIC !== 'true';
    
    if (isDev) {
      // 🔧 DEV MODE: Always start fresh, clean up any existing processes
      console.log('[p2p-sync.ts] 🛠️ Development mode: Starting fresh CouchDB instance');
      
      // Kill any existing CouchDB processes that might be running
      try {
        if (process.platform === 'darwin' || process.platform === 'linux') {
          // Kill any existing CouchDB processes
          const { exec } = require('child_process');
          await new Promise<void>((resolve) => {
            exec('pkill -f couchdb', (error: any) => {
              // Ignore errors - process might not exist
              console.log('[p2p-sync.ts] 🧹 Cleaned up any existing CouchDB processes');
              resolve();
            });
          });
        }
        
        // Clean up any existing URI files to prevent conflicts
        const uriFile = path.join(dbRootPath, 'couchdb-config', 'couch.uri');
        if (fs.existsSync(uriFile)) {
          fs.unlinkSync(uriFile);
          console.log('[p2p-sync.ts] 🧹 Removed existing URI file');
        }
        
        // Wait a moment for cleanup
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (cleanupError) {
        console.log('[p2p-sync.ts] Cleanup completed (some operations may have failed, continuing...)');
      }
      
    } else {
      // 🚀 PROD MODE: Check for existing CouchDB instance (existing behavior)
      const uriFile = path.join(dbRootPath, 'couchdb-config', 'couch.uri');
      if (fs.existsSync(uriFile)) {
        try {
          const uriContent = fs.readFileSync(uriFile, 'utf8').trim();
          const portMatch = uriContent.match(/:(\d+)\/$/);
          if (portMatch) {
            const existingPort = parseInt(portMatch[1]);
            console.log(`[p2p-sync.ts] Found existing CouchDB URI file, testing port ${existingPort}...`);
            
            // Test if CouchDB is responsive on this port
            const testResponse = await fetch(`http://localhost:${existingPort}/`, { 
              signal: AbortSignal.timeout(2000) 
            });
            
            if (testResponse.ok) {
              serverPort = existingPort;
              console.log(`[p2p-sync.ts] ✅ Reusing existing CouchDB instance on port ${existingPort}`);
              sendP2PLog(`✅ Connected to existing CouchDB instance on port ${existingPort}`);
              return true;
            }
          }
        } catch (error) {
          console.log(`[p2p-sync.ts] Existing CouchDB not responsive, starting new instance...`);
        }
      }
    }
    
    // Check if CouchDB is already running on expected ports
    let existingPort: number | null = null;
    
    // 🎯 Try preferred port first, then fallbacks
    const portsToTry = [PREFERRED_COUCHDB_PORT, ...FALLBACK_COUCHDB_PORTS];
    
    for (const testPort of portsToTry) {
      try {
        const testResponse = await fetch(`http://localhost:${testPort}/`, {
          signal: AbortSignal.timeout(2000) 
        });
        
        if (testResponse.ok) {
          const responseText = await testResponse.text();
          if (responseText.includes('couchdb')) {
            existingPort = testPort;
            break;
          }
        }
      } catch (error) {
        // Port not available or not CouchDB, try next
      }
    }
    
    if (existingPort) {
      serverPort = existingPort;
      console.log(`[p2p-sync.ts] ✅ Reusing existing CouchDB instance on port ${existingPort}`);
      sendP2PLog(`✅ Connected to existing CouchDB instance on port ${existingPort}`);
      return true;
    }
    
    // 🔍 Find the first available port from our preferred list
    async function findAvailablePort(): Promise<number> {
      // Dynamically import get-port as an ES module at runtime
      // @ts-ignore
      const { default: getPort } = await eval("import('get-port')");
      
      for (const testPort of portsToTry) {
        const availablePort = await getPort({ port: testPort });
        if (availablePort === testPort) {
          return testPort; // Our preferred port is available!
        }
      }
      
      // If none of our preferred ports are available, get any available port
      console.warn('[p2p-sync.ts] ⚠️ All preferred CouchDB ports busy, using random port');
      sendP2PLog('⚠️ All preferred CouchDB ports busy, using random port');
      return await getPort();
    }
    
    serverPort = await findAvailablePort();
    
    console.log(`[p2p-sync.ts] 🚀 Starting new CouchDB instance on port ${serverPort}`);
    sendP2PLog(`🚀 Starting new CouchDB instance on port ${serverPort}`);
    
    // Ensure DB data directory exists
    const couchDbDataPath = path.join(dbRootPath, 'couchdb-data');
    if (!fs.existsSync(couchDbDataPath)) {
      fs.mkdirSync(couchDbDataPath, { recursive: true });
    }
    
    // In packaged apps Electron copies everything specified in `extraResources` to
    // `process.resourcesPath` (e.g. "/Applications/Bistro.app/Contents/Resources").
    // During development we keep binaries under `electron/resources/*` which is
    // resolved via `app.getAppPath() + '/resources'`.

    const resourcesBasePath = app.isPackaged
      ? process.resourcesPath // Production / DMG build
      : path.join(app.getAppPath(), 'resources'); // Dev / live reload

    // ------------------------------------------------------------
    // 🚀 Universal CouchDB path resolution
    // ------------------------------------------------------------
    // Packages sometimes rename / relocate the embedded CouchDB folder.
    // We therefore:
    //  1. Try conventional name based on OS (e.g. couchdb-macos).
    //  2. Scan the resources directory for ANY folder that starts with
    //     "couchdb" (case-insensitive) and pick the first match.
    //  3. Finally, look in `./electron/resources/*` when running unpackaged
    //     in dev mode (already covered via fallbackPath).

    const conventionalDir = process.platform === 'darwin'
      ? 'couchdb-macos'
      : process.platform === 'win32'
        ? 'couchdb-windows'
        : 'couchdb-linux';

    let couchDbPath = path.join(resourcesBasePath, conventionalDir);

    const pathExists = (p: string) => {
      try { return fs.existsSync(p); } catch { return false; }
    };

    if (!pathExists(couchDbPath)) {
      // 🔍 Scan resourcesBasePath for any directory starting with "couchdb"
      const match = fs.readdirSync(resourcesBasePath)
        .find(d => fs.statSync(path.join(resourcesBasePath, d)).isDirectory() && d.toLowerCase().startsWith('couchdb'));
      if (match) {
        couchDbPath = path.join(resourcesBasePath, match);
      }
    }

    // Dev fallback (unpacked run)
    if (!pathExists(couchDbPath)) {
      const fallbackPath = path.resolve(__dirname, '../../resources');
      const match = fs.readdirSync(fallbackPath)
        .find(d => fs.statSync(path.join(fallbackPath, d)).isDirectory() && d.toLowerCase().startsWith('couchdb'));
      if (match) {
        couchDbPath = path.join(fallbackPath, match);
      }
    }

    if (!pathExists(couchDbPath)) {
      const err = `❗ Embedded CouchDB directory not found inside resources. Looked for '${conventionalDir}' and scanned for *couchdb* folders.`;
      console.error(`[p2p-sync.ts] ${err}`);
      sendP2PLog(err);
      throw new Error(err);
    }

    // 👉 NEW: On Windows, CouchDB batch scripts fail when ROOTDIR contains spaces (e.g. "Program Files").
    //         To guarantee startup we copy the embedded CouchDB directory once to a path **without spaces**
    //         inside the per-user data folder, then launch CouchDB from there on subsequent runs.
    if (process.platform === 'win32' && couchDbPath.includes(' ')) {
      const portableCouchPath = path.join(app.getPath('userData'), 'couchdb-portable');

      try {
        if (!fs.existsSync(portableCouchPath)) {
          console.log(`[p2p-sync.ts] ⚠️ Detected space in CouchDB path. Copying binaries to safe location: ${portableCouchPath}`);
          sendP2PLog(`⚠️ Copying CouchDB binaries to space-free path…`);

          // Node 16+: fs.cpSync supports recursive directory copy
          fs.cpSync(couchDbPath, portableCouchPath, { recursive: true });
        }

        couchDbPath = portableCouchPath; // Always launch from space-free directory
      } catch (copyErr) {
        console.error('[p2p-sync.ts] ❌ Failed to copy CouchDB binaries to portable location:', copyErr);
        sendP2PLog(`❌ Failed to copy CouchDB binaries: ${copyErr}`);
        // Fallback to original path (may still work on some systems)
      }
    }

    sendP2PLog(`🔧 Using CouchDB path: ${couchDbPath}`);
    console.log(`[p2p-sync.ts] Using CouchDB path: ${couchDbPath}`);
    
    // Create CouchDB config directory
    const couchDbConfigPath = path.join(dbRootPath, 'couchdb-config');
    if (!fs.existsSync(couchDbConfigPath)) {
      fs.mkdirSync(couchDbConfigPath, { recursive: true });
    }
    
    // Create a custom configuration file for CouchDB
    const configFile = path.join(couchDbConfigPath, COUCHDB_CONFIG_FILENAME);
    
    // 🔧 DEV MODE: Use unique node name to prevent conflicts
    const nodeNameSuffix = isDev ? `-dev-${Date.now()}` : '';
    const nodeName = `couchdb@127.0.0.1${nodeNameSuffix}`;
    
    // Basic CouchDB configuration
    const couchDbConfig = `
[couchdb]
uuid = ${localDeviceId}
database_dir = ${couchDbDataPath}
view_index_dir = ${couchDbDataPath}
uri_file = ${path.join(couchDbConfigPath, 'couch.uri')}
single_node = true

[log]
file = ${path.join(dbRootPath, 'couchdb.log')}
level = info

[chttpd]
port = ${serverPort}
bind_address = 0.0.0.0
enable_cors = true

[cors]
origins = *
credentials = true
methods = GET, PUT, POST, HEAD, DELETE
headers = accept, authorization, content-type, origin, referer, x-csrf-token

[couch_httpd_auth]
require_valid_user = false

[admins]
admin = admin

[vm_args]
-name ${nodeName}
    `;
    
    fs.writeFileSync(configFile, couchDbConfig);
    
    // Start CouchDB
    let couchDbExecutable: string;
    
    // ----------------------------------------------------------------
    // 🔑 Executable resolution – support .cmd, .bat, and .exe on Windows
    // ----------------------------------------------------------------
    if (process.platform === 'darwin' || process.platform === 'linux') {
      couchDbExecutable = path.join(couchDbPath, 'bin', 'couchdb');
    } else {
      // try multiple common filenames in order of preference
      const candidates = [
        path.join(couchDbPath, 'bin', 'couchdb.cmd'),
        path.join(couchDbPath, 'bin', 'couchdb.bat'),
        path.join(couchDbPath, 'bin', 'couchdb.exe')
      ];
      couchDbExecutable = candidates.find(pathExists) || candidates[0];
    }
    
    // 🚨 Bullet-proof validation – abort early if executable is missing
    if (!fs.existsSync(couchDbExecutable)) {
      const errMsg = `[p2p-sync.ts] CouchDB executable not found at ${couchDbExecutable}. ` +
                     'The bundled CouchDB resources may be missing or incorrectly packaged.';
      console.error(errMsg);
      sendP2PLog(`❌ ${errMsg}`);
      throw new Error(errMsg);
    }
    
    // Ensure execute permission when possible. On read-only volumes (e.g. DMG)
    // chmod will fail – that is safe to ignore because the packager already
    // made the file executable. We only try to fix perms when writable.
    if (process.platform !== 'win32') {
      try {
        fs.chmodSync(couchDbExecutable, 0o755);
      } catch (permErr: any) {
        if (permErr.code !== 'EROFS' && permErr.code !== 'EPERM') {
          // For unexpected errors, log and re-throw; otherwise continue.
          console.warn('[p2p-sync.ts] chmod failed, continuing anyway:', permErr.message);
        }
      }
    }
    
    // Launch CouchDB process with detailed debug logging
    const stdoutPath = path.join(dbRootPath, 'couchdb-stdout.log');
    const stderrPath = path.join(dbRootPath, 'couchdb-stderr.log');
    
    // Setup environment variables for Windows CouchDB
    let spawnEnv = { ...process.env };
    let spawnArgs: string[] = [];
    let spawnOptions: any = {
      stdio: ['ignore', 'pipe', 'pipe'],
    };
    
    if (process.platform === 'win32') {
       // 🪟 Rock-solid Windows portable CouchDB setup
       // Dynamically locate bundled Erlang dir (erts-*)
       const erlangDirName = fs.readdirSync(couchDbPath).find((d) => d.startsWith('erts-')) || 'erts';
       const erlangHomeDir = path.join(couchDbPath, erlangDirName);
       const erlangBinDir = path.join(erlangHomeDir, 'bin');
       const couchBinDir = path.join(couchDbPath, 'bin');

       // Environment expected by couchdb.cmd
       spawnEnv.ROOTDIR = couchDbPath;
       spawnEnv.COUCHDB_BIN_DIR = couchBinDir;
       spawnEnv.COUCHDB_LIB_DIR = path.join(couchDbPath, 'lib');
       spawnEnv.COUCHDB_ETC_DIR = path.join(couchDbPath, 'etc');
       spawnEnv.COUCHDB_QUERY_SERVER_JAVASCRIPT = './bin/couchjs ./share/server/main.js';
       spawnEnv.COUCHDB_QUERY_SERVER_COFFEESCRIPT = './bin/couchjs ./share/server/main-coffee.js';
       spawnEnv.COUCHDB_FAUXTON_DOCROOT = './share/www';
       spawnEnv.ERLANG_HOME = erlangHomeDir;

       // Ensure Erlang & CouchDB bins are early in PATH
       const currentPath = spawnEnv.PATH || process.env.PATH || '';
       spawnEnv.PATH = `${erlangBinDir};${couchBinDir};${currentPath}`;

       // Pass default.ini THEN our local.ini so overrides win
       spawnArgs = ['-couch_ini', path.join(couchDbPath, 'etc', 'default.ini'), configFile];
       spawnOptions.cwd = couchDbPath;
       spawnOptions.shell = true; // .cmd requires shell on Windows
    } else {
      // 🐧/🍎 Unix-like (macOS & Linux) setup
      spawnEnv.ROOTDIR = couchDbPath;

      // Call script with merged ini list (default first)
      spawnArgs = ['-couch_ini', path.join(couchDbPath, 'etc', 'default.ini'), configFile];
      spawnOptions.cwd = couchDbPath;
    }
    
    spawnOptions.env = spawnEnv;
    
    // Enhanced Windows debugging
    if (process.platform === 'win32') {
      console.log(`[p2p-sync.ts] Windows CouchDB Debug:`);
      console.log(`  - Executable: ${couchDbExecutable}`);
      console.log(`  - Working Dir: ${couchDbPath}`);
      console.log(`  - Args: ${JSON.stringify(spawnArgs)}`);
      console.log(`  - ROOTDIR: ${spawnEnv.ROOTDIR}`);
      console.log(`  - COUCHDB_BIN_DIR: ${spawnEnv.COUCHDB_BIN_DIR}`);
      console.log(`  - PATH: ${spawnEnv.PATH?.split(';').slice(0, 3).join(';')}...`);
      sendP2PLog(`🔧 Starting Windows CouchDB with enhanced environment setup`);
    }
    
    couchdbProcess = spawn(couchDbExecutable, spawnArgs, spawnOptions);
    console.log(`[p2p-sync.ts] Debug: Spawned CouchDB process` +
      ` PID=${couchdbProcess.pid}, cwd=${couchDbPath}, port=${serverPort}, ini=${configFile}`);
    if (couchdbProcess.stdout) {
      couchdbProcess.stdout.on('data', (chunk) => {
        const msg = chunk.toString();
        console.log(`[CouchDB stdout] ${msg.trim()}`);
        fs.appendFileSync(stdoutPath, msg);
      });
    }
    if (couchdbProcess.stderr) {
      couchdbProcess.stderr.on('data', (chunk) => {
        const msg = chunk.toString();
        console.error(`[CouchDB stderr] ${msg.trim()}`);
        fs.appendFileSync(stderrPath, msg);
      });
    }
    // End detailed debug logging
    
    // Handle process events
    couchdbProcess.on('error', (err) => {
      console.error('[p2p-sync.ts] Failed to start CouchDB:', err);
      sendP2PLog(`❌ Failed to start CouchDB: ${err.message}`);
      throw err;
    });
    
    couchdbProcess.on('exit', (code, signal) => {
      if (code !== 0 && code !== null) {
        console.error(`[p2p-sync.ts] CouchDB process exited with code ${code}, signal ${signal}`);
        sendP2PLog(`⚠️ CouchDB process exited with code ${code}`);
      }
    });
    
    // Wait for CouchDB to start
    await waitForCouchDB(serverPort);
    
    // 🔧 DEV MODE: Add extra stabilization time for dev environment
    if (isDev) {
      console.log('[p2p-sync.ts] 🛠️ Dev mode: Adding stabilization delay...');
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    const msg = `🚀 CouchDB server started on port ${serverPort}. Databases in: ${couchDbDataPath}`;
    console.log(msg);
    sendP2PLog(msg);
    return true;
  } catch (error) {
    console.error('[p2p-sync.ts] ❌ Failed to start CouchDB server:', error);
    sendP2PLog(`❌ Failed to start CouchDB server: ${error}`);
    throw error;
  }
}

/**
 * Helper function to wait for CouchDB to be ready
 */
async function waitForCouchDB(port: number, maxRetries = 30, retryDelay = 500): Promise<void> {
  const checkUrl = getCouchDBUrl(port);
  const adminUrl = getCouchDBAdminUrl(port);
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      // Step 1: Check basic CouchDB connectivity
      sendP2PLog(`⏳ Checking CouchDB connectivity (attempt ${retries + 1}/${maxRetries})...`);
      
      const response = await fetch(checkUrl);
      if (!response.ok) {
        throw new Error(`Basic connectivity failed: ${response.status}`);
      }
      
      // Step 2: Check admin interface is ready (critical for dev mode)
      sendP2PLog(`⏳ Verifying admin interface...`);
      const adminResponse = await fetch(adminUrl);
      if (!adminResponse.ok) {
        throw new Error(`Admin interface not ready: ${adminResponse.status}`);
      }
      
      // Step 3: Verify we can list databases (full admin functionality)
      sendP2PLog(`⏳ Testing database operations...`);
      const dbListUrl = `${getCouchDBAdminUrl(port)}/_all_dbs`;
      const dbListResponse = await fetch(dbListUrl);
      if (!dbListResponse.ok) {
        throw new Error(`Database listing failed: ${dbListResponse.status}`);
      }
      
      const dbList = await dbListResponse.json();
      console.log(`[p2p-sync.ts] CouchDB is ready on port ${port} with ${dbList.length} databases`);
      sendP2PLog(`✅ CouchDB is ready and responding on port ${port} (admin interface verified)`);
      
      return;
    } catch (error: any) {
      // Log detailed error for debugging
      if (retries % 5 === 0) {
        console.log(`[p2p-sync.ts] CouchDB not ready yet (attempt ${retries}/${maxRetries}): ${error.message}`);
      }
    }
    
    await new Promise(resolve => setTimeout(resolve, retryDelay));
    retries++;
    
    if (retries % 5 === 0) {
      sendP2PLog(`⏳ Waiting for CouchDB to start... (${retries}/${maxRetries})`);
    }
  }
  
  throw new Error(`CouchDB failed to start after ${maxRetries} attempts`);
}

/**
 * Start Bonjour service discovery to find other devices
 */
async function startBonjourDiscovery() {
  try {
    console.log('[p2p-sync.ts] Attempting to initialize Bonjour...');
    
    // Simplified bonjour initialization to match test script
    // Add better error handling for different import scenarios
    try {
      if (typeof BonjourService === 'function') {
        bonjour = BonjourService();
      } else if (BonjourService && typeof BonjourService.default === 'function') {
        bonjour = BonjourService.default();
      } else if (BonjourService && typeof BonjourService === 'object') {
        // Try to call it as a constructor
        bonjour = new (BonjourService as any)();
      } else {
        throw new Error('Bonjour module not found or not callable');
      }
    } catch (bonjourError) {
      console.error('[p2p-sync.ts] Failed to initialize Bonjour module:', bonjourError);
      sendP2PLog(`❌ Failed to initialize Bonjour module: ${bonjourError}`);
      mdnsStatus = 'error';
      return false;
    }
    
    if (!bonjour) {
      console.error('[p2p-sync.ts] Failed to initialize Bonjour instance!');
      mdnsStatus = 'error';
      sendP2PLog(`❌ Failed to initialize Bonjour instance - bonjour is null`);
      return false;
    }

    console.log('[p2p-sync.ts] ✅ Bonjour instance created successfully');

    // Get hostname and IP
    const hostname = os.hostname();
    const networkInterfaces = os.networkInterfaces();
    
    // Find a suitable IP address (prefer IPv4)
    let localIp = '127.0.0.1';
    for (const interfaceName in networkInterfaces) {
      const addresses = networkInterfaces[interfaceName];
      if (addresses) {
        for (const address of addresses) {
          // Skip internal and IPv6 addresses
          if (!address.internal && address.family === 'IPv4') {
            localIp = address.address;
            break;
          }
        }
      }
    }
    
    // Simplified service info to match working test script
    const serviceInfo = {
      name: `PouchDB-${hostname}-${localDeviceId.substring(0, 8)}`,
      type: SERVICE_TYPE, // Now just 'http'
      port: serverPort,
      txt: {
        id: localDeviceId,
        version: '1.0',
        platform: 'desktop'
      }
    };
    
    console.log('[p2p-sync.ts] Publishing service with config:', JSON.stringify(serviceInfo, null, 2));
    
    // Store the latest service info for retrieval in renderer
    lastServiceInfo = { ...serviceInfo, ip: localIp };
    
    // Publish our service (simplified)
    let service;
    try {
      service = bonjour.publish(serviceInfo);
      console.log('[p2p-sync.ts] ✅ Service published successfully');
    } catch (publishError) {
      console.error('[p2p-sync.ts] Failed to publish service:', publishError);
      sendP2PLog(`❌ Failed to publish service: ${publishError}`);
      mdnsStatus = 'error';
      return false;
    }
    
    // Log more detailed information about the published service
    const bonjourMsg = `📡 Published Bonjour service: ${serviceInfo.name}`;
    console.log(bonjourMsg);
    sendP2PLog(bonjourMsg);
    sendP2PLog(`📡 Service name: ${serviceInfo.name}`);
    sendP2PLog(`📡 Service details: type=${SERVICE_TYPE}, port=${serverPort}`);
    sendP2PLog(`📡 TXT records: ${JSON.stringify(serviceInfo.txt)}`);
    sendP2PLog(`📡 Local IP: ${localIp}`);
    
    // Browser for other services (simplified)
    let browser;
    try {
      browser = bonjour.find({ type: SERVICE_TYPE });
      console.log('[p2p-sync.ts] ✅ Service browser created successfully');
    } catch (browserError) {
      console.error('[p2p-sync.ts] Failed to create service browser:', browserError);
      sendP2PLog(`❌ Failed to create service browser: ${browserError}`);
      // Continue anyway - publishing might still work
    }
    
    if (browser) {
      // When we find a service
      browser.on('up', (service: any) => {
        // Skip our own service
        if (service.txt.id === localDeviceId) return;
        
        sendP2PLog(`👂 Bonjour event: action=up, service=${service.name}`);
        
        // Detailed service logging
        try {
          const safeServiceForLog = {
            name: service.name,
            type: service.type,
            protocol: service.protocol,
            port: service.port,
            txt: service.txt || {},
            addresses: service.addresses || [],
            referer: service.referer || {}
          };
          sendP2PLog(`🔍 Raw Bonjour service data: ${JSON.stringify(safeServiceForLog)}`);
        } catch (error) {
          console.error('Error logging service object:', error);
        }
        
        sendP2PLog(`🔍 Discovered peer: ${service.name}`);
        // Create peer info object with fields compatible with mobile implementation
        const peerInfo: PeerInfo = {
          id: service.txt.id,
          ip: service.referer.address,
          port: service.port,
          hostname: service.name,
          platform: service.txt.platform || 'unknown'
        };
        
        // Extra logging for IP address resolution
        sendP2PLog(`🔍 Detected IP address for peer: ${peerInfo.ip}, port: ${peerInfo.port}`);
        
        // Verify we have a valid IP address
        if (!peerInfo.ip || peerInfo.ip === '0.0.0.0' || peerInfo.ip === '127.0.0.1') {
          // Try to use addresses array if referer address is not usable
          if (service.addresses && service.addresses.length > 0) {
            // Filter for IPv4 addresses that are not localhost
            const validIPs = service.addresses.filter((addr: string) => {
              return addr.indexOf(':') === -1 && !addr.startsWith('127.') && !addr.startsWith('169.254');
            });
            
            if (validIPs.length > 0) {
              peerInfo.ip = validIPs[0];
              sendP2PLog(`🔄 Updated peer IP from addresses array: ${peerInfo.ip}`);
            }
          }
        }
        
        // Debug output for platform detection
        sendP2PLog(`🔍 Detected platform for peer: ${peerInfo.platform}`);
        
        // Store the peer
        peers.set(peerInfo.id, peerInfo);
        // Ensure mDNS status is set to running when peers are discovered
        if (mdnsStatus !== 'running') {
          mdnsStatus = 'running';
        }
        // Notify the UI
        if (mainWindowRef) {
          mainWindowRef.webContents.send('peer-discovered', peerInfo);
        }
      });
      
      // When a service goes down
      browser.on('down', (service: any) => {
        // Skip our own service
        if (service.txt.id === localDeviceId) return;
        sendP2PLog(`👂 Bonjour event: action=down, service=${service.name}`);
        sendP2PLog(`👋 Lost peer: ${service.name}`);
        const peerId = service.txt.id;
        // Stop any active syncs with this peer
        stopAllSyncsWithPeer(peerId);
        // Remove from peers map
        peers.delete(peerId);
        // Notify the UI
        if (mainWindowRef) {
          mainWindowRef.webContents.send('peer-lost', peerId);
        }
      });
    }
    
    mdnsStatus = 'running';
    console.log('[p2p-sync.ts] ✅ Bonjour discovery setup complete');
    return true;
  } catch (error) {
    console.error('❌ Failed to start Bonjour discovery:', error);
    sendP2PLog(`❌ Failed to start Bonjour discovery: ${error}`);
    mdnsStatus = 'error';
    return false;
  }
}

/**
 * Set up IPC handlers for P2P sync operations
 */
function setupP2PSyncIpcHandlers() {
  console.log('[p2p-sync.ts] Setting up IPC handlers...');
  
  // Get all discovered peers
  ipcMain.handle('p2p-get-peers', () => {
    console.log('[p2p-sync.ts] IPC: p2p-get-peers called, returning', peers.size, 'peers');
    return Array.from(peers.values());
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-get-peers handler');
  
  // Get status of all active syncs
  ipcMain.handle('p2p-get-sync-status', () => {
    console.log('[p2p-sync.ts] IPC: p2p-get-sync-status called, returning', syncStatuses.length, 'statuses');
    return syncStatuses;
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-get-sync-status handler');
  
  // Get mDNS status
  ipcMain.handle('p2p-get-mdns-status', () => {
    console.log('[p2p-sync.ts] IPC: p2p-get-mdns-status called, returning:', mdnsStatus);
    return mdnsStatus;
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-get-mdns-status handler');
  
  // Get system ID (device ID) for diagnostics
  ipcMain.handle('p2p-get-system-id', () => {
    console.log('[p2p-sync.ts] IPC: p2p-get-system-id called, returning:', localDeviceId);
    return localDeviceId;
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-get-system-id handler');
  
  // NEW: Get server port for diagnostics
  ipcMain.handle('p2p-get-server-port', () => {
    console.log('[p2p-sync.ts] IPC: p2p-get-server-port called, returning:', serverPort);
    return serverPort;
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-get-server-port handler');
  
  // NEW: Get CouchDB port specifically (alias for UI database detection)
  ipcMain.handle('get-couchdb-port', () => {
    console.log('[p2p-sync.ts] IPC: get-couchdb-port called, returning:', serverPort);
    return serverPort;
  });
  console.log('[p2p-sync.ts] ✅ Registered get-couchdb-port handler');
  
  // NEW: Get service info for renderer
  ipcMain.handle('p2p-get-service-info', () => {
    console.log('[p2p-sync.ts] IPC: p2p-get-service-info called, returning:', lastServiceInfo);
    return lastServiceInfo;
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-get-service-info handler');
  
  // Start syncing with a peer
  ipcMain.handle('p2p-start-sync', async (_event, peerId: string, dbName: string, direction: 'push' | 'pull' | 'both' = 'both') => {
    console.log('[p2p-sync.ts] IPC: p2p-start-sync called with:', { peerId, dbName, direction });
    try {
      const result = await startSync(peerId, dbName, direction);
      return { success: true, data: result };
    } catch (error) {
      console.error(`Failed to start sync with peer ${peerId} for DB ${dbName}:`, error);
      return { success: false, error: String(error) };
    }
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-start-sync handler');
  
  // Stop syncing with a peer for a specific database
  ipcMain.handle('p2p-stop-sync', async (_event, peerId: string, dbName: string) => {
    console.log('[p2p-sync.ts] IPC: p2p-stop-sync called with:', { peerId, dbName });
    try {
      await stopSync(peerId, dbName);
      return { success: true };
    } catch (error) {
      console.error(`Failed to stop sync with peer ${peerId} for DB ${dbName}:`, error);
      return { success: false, error: String(error) };
    }
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-stop-sync handler');
  
  // Stop all syncs with a peer
  ipcMain.handle('p2p-stop-all-syncs-with-peer', async (_event, peerId: string) => {
    console.log('[p2p-sync.ts] IPC: p2p-stop-all-syncs-with-peer called with:', { peerId });
    try {
      await stopAllSyncsWithPeer(peerId);
      return { success: true };
    } catch (error) {
      console.error(`Failed to stop all syncs with peer ${peerId}:`, error);
      return { success: false, error: String(error) };
    }
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-stop-all-syncs-with-peer handler');
  
  // Set up periodic heartbeat to send diagnostic logs
  const heartbeatInterval = setInterval(() => {
    sendP2PLog(`❤️ P2P sync heartbeat - mDNS status: ${mdnsStatus}, peers: ${peers.size}, syncs: ${activeSyncs.size}`);
  }, 30000);
  
  // Cleanup function to clear the interval if needed
  function clearHeartbeat() {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
    }
  }
  
  console.log('[p2p-sync.ts] ✅ All IPC handlers registered successfully');
}

/**
 * Cleanup P2P sync resources on error or shutdown
 */
export async function cleanupP2PSync() {
  console.log('[p2p-sync.ts] Cleaning up P2P sync resources');
  
  // 🚀 CRITICAL FIX: Terminate embedded CouchDB when application exits on **all** platforms & modes
  if (couchdbProcess) {
    try {
      if (process.platform === 'win32') {
        // Use SIGTERM on Windows (graceful) but fallback to SIGKILL if unsupported
        couchdbProcess.kill('SIGTERM');
      } else {
        couchdbProcess.kill('SIGTERM');
      }
      console.log('[p2p-sync.ts] ✅ CouchDB process terminated');
    } catch (error) {
      console.error('[p2p-sync.ts] Error terminating CouchDB process:', error);
    }
    couchdbProcess = null;
  }
  
  // Destroy the Bonjour instance
  if (bonjour) {
    try {
      if (typeof bonjour.destroy === 'function') {
        bonjour.destroy();
        console.log('[p2p-sync.ts] Bonjour instance destroyed');
      }
    } catch (error) {
      console.error('[p2p-sync.ts] Error destroying Bonjour instance:', error);
    }
    bonjour = null;
  }
  
  // Clear other state
  peers.clear();
  activeSyncs.clear();
  syncStatuses.length = 0;
  
  console.log('[p2p-sync.ts] P2P sync resources cleaned up');
  return true;
}

/**
 * Start syncing a database with a peer
 */
async function startSync(peerId: string, dbName: string, direction: 'push' | 'pull' | 'both' = 'both'): Promise<SyncStatus> {
  console.log(`[p2p-sync.ts] Starting sync with peer ${peerId} for DB ${dbName}, direction: ${direction}`);
  
  // Find the peer in our peers map
  const peer = peers.get(peerId);
  if (!peer) {
    const error = `Peer ${peerId} not found`;
    console.error(`[p2p-sync.ts] ${error}`);
    throw new Error(error);
  }
  
  // Check if this is a mobile peer - mobile doesn't host HTTP servers
  if (peer.platform === 'mobile' || peer.platform === 'android' || peer.platform === 'ios') {
    const error = 'Cannot sync directly to mobile peers - mobile devices do not host HTTP servers';
    console.error(`[p2p-sync.ts] ${error}`);
    sendP2PLog(`⚠️ ${error}`);
    throw new Error(error);
  }
  
  // Generate a unique key for this sync
  const syncKey = `${peerId}:${dbName}`;
  
  // Check if we already have a sync running for this peer and DB
  if (activeSyncs.has(syncKey)) {
    console.log(`[p2p-sync.ts] Sync already exists for ${syncKey}, returning existing status`);
    // Find and return the existing sync status
    const existingStatus = syncStatuses.find(
      s => s.peerId === peerId && s.dbName === dbName
    );
    if (existingStatus) {
      return existingStatus;
    }
  }
  
  try {
    // Create a PouchDB instance for the local database
    const PouchDB = PouchDBConstructor.defaults({
      prefix: dbRootPathRef + path.sep,
      auto_compaction: true,
    });
    const localDb = new PouchDB(dbName);
    
    // Construct the remote database URL
    const remoteDbUrl = `http://${peer.ip}:${peer.port}/${dbName}`;
    console.log(`[p2p-sync.ts] Remote database URL: ${remoteDbUrl}`);
    const remoteDb = new PouchDB(remoteDbUrl);
    
    // Setup sync options based on direction
    const syncOptions: PouchDB.Replication.ReplicateOptions = {
      live: true,
      retry: true,
    };
    
    // Determine replication method based on direction
    let syncHandler;
    switch (direction) {
      case 'push':
        console.log(`[p2p-sync.ts] Setting up one-way push sync to ${remoteDbUrl}`);
        syncHandler = localDb.replicate.to(remoteDb, syncOptions);
        break;
      case 'pull':
        console.log(`[p2p-sync.ts] Setting up one-way pull sync from ${remoteDbUrl}`);
        syncHandler = localDb.replicate.from(remoteDb, syncOptions);
        break;
      case 'both':
      default:
        console.log(`[p2p-sync.ts] Setting up two-way sync with ${remoteDbUrl}`);
        syncHandler = localDb.sync(remoteDb, syncOptions);
        break;
    }
    
    // Create a status object
    const syncStatus: SyncStatus = {
      peerId,
      dbName,
      direction,
      status: 'active',
    };
    
    // Add the status to our array
    const existingStatusIndex = syncStatuses.findIndex(
      s => s.peerId === peerId && s.dbName === dbName
    );
    
    if (existingStatusIndex >= 0) {
      syncStatuses[existingStatusIndex] = syncStatus;
    } else {
      syncStatuses.push(syncStatus);
    }
    
    // Store the sync handler with the localDb reference
    activeSyncs.set(syncKey, { 
      syncHandler, 
      localDb,
      remoteDb
    });
    
    // Set up event handlers
    (syncHandler as any).on('change', (info: any) => {
      console.log(`[p2p-sync.ts] Sync change for ${syncKey}:`, info);
      
      // Update the sync status with progress info
      const statusIndex = syncStatuses.findIndex(
        s => s.peerId === peerId && s.dbName === dbName
      );
      
      if (statusIndex >= 0) {
        const progress = syncStatuses[statusIndex].progress || { 
          docsPushed: 0, 
          docsPulled: 0, 
          totalDocs: 0 
        };
        
        // Update progress based on the sync direction and available info
        if (direction === 'push' || direction === 'both') {
          progress.docsPushed += info.docs ? info.docs.length : 0;
        }
        if (direction === 'pull' || direction === 'both') {
          progress.docsPulled += info.docs ? info.docs.length : 0;
        }
        
        syncStatuses[statusIndex].progress = progress;
        syncStatuses[statusIndex].status = 'active';
        
        // Notify the UI of the status update
        if (mainWindowRef) {
          mainWindowRef.webContents.send('sync-status-updated', syncStatuses[statusIndex]);
        }
      }
    });
    
    (syncHandler as any).on('paused', () => {
      console.log(`[p2p-sync.ts] Sync paused for ${syncKey}`);
      
      // Update the status
      const statusIndex = syncStatuses.findIndex(
        s => s.peerId === peerId && s.dbName === dbName
      );
      
      if (statusIndex >= 0) {
        syncStatuses[statusIndex].status = 'paused';
        
        // Notify the UI
        if (mainWindowRef) {
          mainWindowRef.webContents.send('sync-status-updated', syncStatuses[statusIndex]);
        }
      }
    });
    
    (syncHandler as any).on('active', () => {
      console.log(`[p2p-sync.ts] Sync active for ${syncKey}`);
      
      // Update the status
      const statusIndex = syncStatuses.findIndex(
        s => s.peerId === peerId && s.dbName === dbName
      );
      
      if (statusIndex >= 0) {
        syncStatuses[statusIndex].status = 'active';
        
        // Notify the UI
        if (mainWindowRef) {
          mainWindowRef.webContents.send('sync-status-updated', syncStatuses[statusIndex]);
        }
      }
    });
    
    (syncHandler as any).on('error', (err: any) => {
      console.error(`[p2p-sync.ts] Sync error for ${syncKey}:`, err);
      
      // Update the status
      const statusIndex = syncStatuses.findIndex(
        s => s.peerId === peerId && s.dbName === dbName
      );
      
      if (statusIndex >= 0) {
        syncStatuses[statusIndex].status = 'error';
        syncStatuses[statusIndex].error = err.message || String(err);
        
        // Notify the UI
        if (mainWindowRef) {
          mainWindowRef.webContents.send('sync-status-updated', syncStatuses[statusIndex]);
        }
      }
    });
    
    (syncHandler as any).on('complete', (info: any) => {
      console.log(`[p2p-sync.ts] Sync complete for ${syncKey}:`, info);
      
      // Update the status
      const statusIndex = syncStatuses.findIndex(
        s => s.peerId === peerId && s.dbName === dbName
      );
      
      if (statusIndex >= 0) {
        syncStatuses[statusIndex].status = 'complete';
        
        // Notify the UI
        if (mainWindowRef) {
          mainWindowRef.webContents.send('sync-status-updated', syncStatuses[statusIndex]);
        }
      }
    });
    
    // Return the sync status
    return syncStatus;
  } catch (error) {
    console.error(`[p2p-sync.ts] Error starting sync for ${syncKey}:`, error);
    
    // Create an error status
    const errorStatus: SyncStatus = {
      peerId,
      dbName,
      direction,
      status: 'error',
      error: error instanceof Error ? error.message : String(error)
    };
    
    // Add to or update the statuses array
    const existingStatusIndex = syncStatuses.findIndex(
      s => s.peerId === peerId && s.dbName === dbName
    );
    
    if (existingStatusIndex >= 0) {
      syncStatuses[existingStatusIndex] = errorStatus;
    } else {
      syncStatuses.push(errorStatus);
    }
    
    // Notify the UI
    if (mainWindowRef) {
      mainWindowRef.webContents.send('sync-status-updated', errorStatus);
    }
    
    throw error;
  }
}

/**
 * Stop syncing a database with a peer
 */
async function stopSync(peerId: string, dbName: string): Promise<boolean> {
  console.log(`[p2p-sync.ts] Stopping sync with peer ${peerId} for DB ${dbName}`);
  
  // Generate the sync key
  const syncKey = `${peerId}:${dbName}`;
  
  // Find the sync in our active syncs
  const syncInfo = activeSyncs.get(syncKey);
  if (!syncInfo) {
    console.log(`[p2p-sync.ts] No active sync found for ${syncKey}`);
    return false;
  }
  
  try {
    // Cancel the sync
    if (syncInfo.syncHandler && typeof syncInfo.syncHandler.cancel === 'function') {
      syncInfo.syncHandler.cancel();
    }
    
    // Close the database connections
    if (syncInfo.localDb && typeof syncInfo.localDb.close === 'function') {
      await syncInfo.localDb.close();
    }
    
    if (syncInfo.remoteDb && typeof syncInfo.remoteDb.close === 'function') {
      await syncInfo.remoteDb.close();
    }
    
    // Remove from active syncs
    activeSyncs.delete(syncKey);
    
    // Update the status
    const statusIndex = syncStatuses.findIndex(
      s => s.peerId === peerId && s.dbName === dbName
    );
    
    if (statusIndex >= 0) {
      // Remove from the status array
      syncStatuses.splice(statusIndex, 1);
      
      // Notify the UI
      if (mainWindowRef) {
        mainWindowRef.webContents.send('sync-status-updated', { 
          peerId, 
          dbName, 
          status: 'complete',
          direction: 'both' 
        });
      }
    }
    
    console.log(`[p2p-sync.ts] Successfully stopped sync for ${syncKey}`);
    return true;
  } catch (error) {
    console.error(`[p2p-sync.ts] Error stopping sync for ${syncKey}:`, error);
    
    // Still remove from active syncs even if there was an error
    activeSyncs.delete(syncKey);
    
    // Still notify the UI about the error
    const statusIndex = syncStatuses.findIndex(
      s => s.peerId === peerId && s.dbName === dbName
    );
    
    if (statusIndex >= 0) {
      syncStatuses[statusIndex].status = 'error';
      syncStatuses[statusIndex].error = error instanceof Error ? error.message : String(error);
      
      if (mainWindowRef) {
        mainWindowRef.webContents.send('sync-status-updated', syncStatuses[statusIndex]);
      }
    }
    
    return false;
  }
}

/**
 * Stop all syncs with a specific peer
 */
async function stopAllSyncsWithPeer(peerId: string): Promise<boolean> {
  console.log(`[p2p-sync.ts] Stopping all syncs with peer ${peerId}`);
  
  // Find all syncs with this peer
  const peerSyncs = syncStatuses.filter(s => s.peerId === peerId);
  if (peerSyncs.length === 0) {
    console.log(`[p2p-sync.ts] No active syncs found for peer ${peerId}`);
    return true;
  }
  
  let allSucceeded = true;
  
  // Stop each sync
  for (const sync of peerSyncs) {
    try {
      const success = await stopSync(peerId, sync.dbName);
      if (!success) {
        allSucceeded = false;
      }
    } catch (error) {
      console.error(`[p2p-sync.ts] Error stopping sync for peer ${peerId}, DB ${sync.dbName}:`, error);
      allSucceeded = false;
    }
  }
  
  return allSucceeded;
}

/**
 * Shutdown the P2P sync system
 */
export async function shutdownP2PSync() {
  console.log('[p2p-sync.ts] Shutting down P2P sync system');
  
  // Stop all active syncs
  for (const [syncKey, _] of activeSyncs) {
    const [peerId, dbName] = syncKey.split(':');
    try {
      await stopSync(peerId, dbName);
    } catch (error) {
      console.error(`[p2p-sync.ts] Error stopping sync for ${syncKey} during shutdown:`, error);
    }
  }
  
  // Clean up all resources
  return cleanupP2PSync();
}

// -- Helper to get the CouchDB port for other modules
export function getCouchDBPort(): number {
  return serverPort;
}