# Capacitor iOS Setup & Camera Integration 📱✨

## Overview
Successfully implemented Capacitor iOS support with camera and photos access for the addstock component functionality, following the same pattern as Android but optimized for iOS/Electron.

## ✅ What's Been Implemented

### 1. iOS Platform Setup
- ✅ Added `@capacitor/ios` platform
- ✅ Added `@capacitor/camera` plugin for camera/photos access
- ✅ Created iOS project structure in `/ios` directory
- ✅ Configured Xcode workspace and project files

### 2. Camera & Photos Permissions
- ✅ Added iOS permissions to `Info.plist`:
  - `NSCameraUsageDescription` - Camera access for receipt/stock photos
  - `NSPhotoLibraryUsageDescription` - Photo library access for existing photos
  - `NSPhotoLibraryAddUsageDescription` - Save photos to library
- ✅ Configured Capacitor camera plugin with iOS-specific settings

### 3. Development Scripts
- ✅ Created `scripts/capacitor-dev-ios.js` - iOS development mode
- ✅ Created `scripts/capacitor-prod-ios.js` - iOS production builds
- ✅ Added npm scripts:
  - `npm run cap:dev:ios` - Start iOS development
  - `npm run cap:build:ios` - Build iOS app
  - `npm run cap:prod:ios` - Production iOS build
  - `npm run dev:all:ios` - Run all platforms including iOS

### 4. Camera Hook Integration
- ✅ Created `hooks/use-camera.ts` for cross-platform camera functionality
- ✅ Supports all platforms: Web, Android, iOS, Electron
- ✅ Handles permissions automatically
- ✅ Provides methods for:
  - Taking photos with camera
  - Picking photos from gallery
  - Multiple photo selection
  - Action sheet picker (camera or gallery)

## 🚀 Usage

### Development Mode
```bash
# Start iOS development (opens Xcode)
npm run cap:dev:ios

# Start Next.js dev server (in another terminal)
npm run dev

# Or run everything together
npm run dev:all:ios
```

### Production Build
```bash
# Build and open iOS project for App Store submission
npm run cap:prod:ios
```

### Camera Usage in Components
```typescript
import { useCamera } from '@/hooks/use-camera';

function AddStockComponent() {
  const { takePhoto, pickPhoto, showImagePicker, isLoading, error } = useCamera();
  
  const handleTakePhoto = async () => {
    try {
      const photo = await takePhoto();
      // Use photo.webPath for display
      // Use photo.path for file operations
    } catch (err) {
      console.error('Camera error:', err);
    }
  };
  
  const handlePickPhoto = async () => {
    try {
      const photo = await pickPhoto();
      // Handle selected photo
    } catch (err) {
      console.error('Photo picker error:', err);
    }
  };
  
  const handleShowPicker = async () => {
    try {
      // Shows action sheet: "Take Photo" or "Choose from Library"
      const photo = await showImagePicker();
      // Handle photo
    } catch (err) {
      console.error('Image picker error:', err);
    }
  };
}
```

## 📱 Platform Features

### iOS Specific
- ✅ Native camera access with iOS permissions
- ✅ Photo library integration
- ✅ Action sheet for camera/gallery selection
- ✅ Automatic permission handling
- ✅ Optimized for iPhone/iPad

### Cross-Platform Support
- ✅ **Web**: File input fallback
- ✅ **Android**: Native camera & gallery
- ✅ **iOS**: Native camera & gallery  
- ✅ **Electron**: Desktop camera access

## 🔧 Configuration Files

### Capacitor Config (`capacitor.config.ts`)
```typescript
plugins: {
  Camera: {
    iosPermissions: {
      camera: 'This app needs access to camera to take photos of receipts and stock items for inventory management.',
      photos: 'This app needs access to photo library to select existing photos of receipts and stock items for inventory management.'
    }
  }
}
```

### iOS Permissions (`ios/App/App/Info.plist`)
```xml
<key>NSCameraUsageDescription</key>
<string>This app needs access to camera to take photos of receipts and stock items for inventory management.</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>This app needs access to photo library to select existing photos of receipts and stock items for inventory management.</string>
<key>NSPhotoLibraryAddUsageDescription</key>
<string>This app needs access to save photos of receipts and stock items to your photo library for inventory management.</string>
```

## 🎯 AddStock Component Integration

The camera functionality is specifically designed for the addstock component:

1. **Receipt Photos**: Take/select photos of purchase receipts
2. **Stock Item Photos**: Document inventory items visually
3. **Offline Support**: Works with PouchDB for offline inventory management
4. **Multi-Platform**: Same API works across web, mobile, and desktop

## 📋 Next Steps

1. **Test on Physical Device**: Deploy to iOS device for real camera testing
2. **App Store Preparation**: Configure signing, icons, and metadata
3. **Camera Integration**: Integrate camera hook into addstock components
4. **Photo Storage**: Implement photo storage with R2/local file system

## 🔍 Troubleshooting

### Common Issues
- **Permissions Denied**: Check Info.plist descriptions are user-friendly
- **Camera Not Working**: Ensure physical device testing (simulator has limitations)
- **Build Errors**: Run `npx cap sync` after configuration changes

### Development Tips
- Use `npm run cap:dev:ios` for live reload during development
- Test camera functionality on physical iOS devices
- Check Xcode console for detailed error messages
- Ensure proper signing certificates for device testing

## 🎉 Success!

iOS Capacitor implementation is complete with full camera and photos access for the addstock functionality! The setup mirrors the Android implementation while being optimized for iOS development workflows. 