# Project Issues and To-Do List

This document outlines the identified issues within the application, categorized by department, along with their current status, relevant codebase context, and proposed actions.

## 1. UI/UX - App Sidebar & User Pop-up

**Status:** Addressed

**Issues & Actions:**

*   **App Sidebar Clean-up:**
    *   **Issue:** Remove unnecessary items (e.g., "Home" button, test features) from the app sidebar in production.
    *   **Status:** **ADDRESSED.**
        *   The "Home" button has been removed from the sidebar.
        *   "Offline Test," "🧪 Printer Test," "P2P Sync," and "Connect Devices" are now conditionally rendered and will only appear in `development` environments (verified in `components/app-sidebar.tsx`).

*   **User Pop-up Clean-up:**
    *   **Issue:** Remove "NDNS browser," "connect device," "P2P sync," and "cleared cache" from the user pop-up in production.
    *   **Status:** **ADDRESSED.**
        *   "Cache Cleaner" has been made development-only in `components/nav-user.tsx` (now conditionally rendered).
        *   The "mDNS Browser" page (`app/mdns-browser/page.tsx`) has been confirmed to render only in `development` mode.
        *   "Connect device" and "P2P Sync" are not directly linked from `components/nav-user.tsx` and are controlled by `NODE_ENV` checks in `components/app-sidebar.tsx`.

---

## 2. Kitchen & Printing

**Status:** To Be Investigated

**Issues & Actions:**

*   **Kitchen Printer Content Optimization:**
    *   **Issue:** The kitchen printer content needs to be optimized, enhanced, and include special handling for custom pizzas. [[memory:552461]]
    *   **Context:** Likely involves `app/components/print/KitchenPrint.tsx` or related printing services.
    *   **Action:** Investigate existing kitchen print implementation to identify areas for optimization and enhancement. Determine how custom pizza data is structured and how it can be specially handled for clear kitchen display.

---

## 3. Ordering & Delivery

**Status:** To Be Investigated

**Issues & Actions:**

*   **Prevent Order Failure After Money Collection:**
    *   **Issue:** Once money has been collected for an order (either by cashier closing session or delivery driver completing delivery), the order should not have the ability to be marked as failed or partially failed in the order list.
    *   **Context:** Likely involves `app/components/OrderList.tsx`, `app/components/DeliveryFailureHandler.tsx`, and relevant order status management logic.
    *   **Action:** Re-investigate and re-implement a solution to disable failure options for paid/collected orders, considering alternative approaches if necessary. (Note: Previous changes were reverted as per prior `ISSUES_AND_TODO.md`.)

*   **Confirmation Dialog for "Total Failure":**
    *   **Issue:** When "total failure" is clicked, a confirmation dialog should appear.
    *   **Context:** Likely involves `app/components/DeliveryFailureHandler.tsx`.
    *   **Action:** Re-identify the "total failure" action and re-implement a Shadcn UI `AlertDialog` component for confirmation. (Note: Previous changes were reverted as per prior `ISSUES_AND_TODO.md`.)

*   **Optional Customer Info for Takeaway Orders:**
    *   **Issue:** For "takeaway" orders, customer name and phone should *not* be required.
    *   **Context:** Likely involves `lib/types/order-types.ts`, `app/components/NewOrderingInterface.tsx`, and associated UI validation.
    *   **Action:** Re-evaluate how to make customer name and phone optional for takeaway orders, ensuring it aligns with `requiresCustomerInfo` logic and UI validation. (Note: Previous changes were reverted/modified by user as per prior `ISSUES_AND_TODO.md`.)

*   **Required Phone & Address, Optional Name for Delivery Orders:**
    *   **Issue:** For "delivery" orders, the customer's phone and address should be required, but the name should be optional. Other details like personal/freelance, payment method, and amount are also required.
    *   **Context:** Likely involves `app/components/NewOrderingInterface.tsx` and UI validation.
    *   **Action:** Ensure address is validated as required for delivery. Acknowledge that customer name and phone are currently both required as per user's direct changes; confirm if this is the final desired state or if name should become optional.

*   **Display Customer Info in Ordering List for Delivery:**
    *   **Issue:** The order list should display the customer's name, phone, and address directly within the order details for delivery orders.
    *   **Context:** Likely involves `app/(protected)/orders/page.tsx` or components it renders, such as `components/OrderList.tsx` or `components/orders/OrderCard.tsx`.
    *   **Action:** Identify the main order listing component and add these customer details to the order display for delivery orders.

---

## 4. Staff & Permissions (Authentication/User Management)

**Status:** To Be Investigated

**Issues & Actions:**

*   **Simplified Staff Creation (Delivery vs. Normal Staff Toggle):**
    *   **Issue:** In staff creation, there should only be two types: "delivery" and "normal staff", managed by a simple toggle. The dropdown for staff roles should be removed. When a staff is marked as "delivery guy", they should be shown in the ordering interface under a "personal" selection, and the staff dropdown will *only* show delivery staff.
    *   **Context:** Likely involves `app/(protected)/staff/page.tsx`, `app/(protected)/staff/components/SimpleStaffForm.tsx`, `lib/types/staff.ts` or `lib/types/unified-staff.ts`, and relevant staff selection components in the ordering interface (e.g., `app/components/NewOrderingInterface.tsx`).
    *   **Action:** Modify the staff creation form to use a toggle for "delivery guy" status. Update staff type definitions and implement logic to filter staff in the ordering interface based on this status.

*   **Add New User Redirection to Auth Page:**
    *   **Issue:** When attempting to add a new user, the application is not correctly redirecting to the authentication page.
    *   **Context:** Likely involves `app/admin/users/page.tsx` or related user management components, and authentication routing logic.
    *   **Action:** Investigate the user creation flow to identify why redirection to the auth page is failing and implement the correct redirection.

