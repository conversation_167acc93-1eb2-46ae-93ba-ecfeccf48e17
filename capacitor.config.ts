import type { CapacitorConfig } from '@capacitor/cli';

/**
 * Capacitor configuration for the app
 * 
 * Development vs Production modes:
 * - Development: Uses server.url for live reloading from local dev server
 * - Production: Uses static files (webDir: 'out') but can call remote APIs when online
 */

// Check if we're in development mode
const isDev = process.env.NODE_ENV === 'development' || process.env.CAP_MODE === 'dev';

const config: CapacitorConfig = {
  appId: 'com.bistro.pos',
  appName: 'bistro',
  webDir: isDev ? 'public' : 'out', // Dev: public folder, Prod: static build
  server: isDev ? {
    // Development: Use local dev server with live reload
    url: process.env.CAPACITOR_DEV_SERVER_URL || 'http://***********:3000',
    androidScheme: 'https',
    cleartext: true
  } : {
    // Production: No server URL, uses static files
    androidScheme: 'https',
    cleartext: true
  },
  plugins: {
    CapacitorCommunityElectron: {
      electronOptions: {
        title: 'Bistro',
        windowWidth: 1200,
        windowHeight: 800,
        resizable: true,
        singleInstance: true
      }
    },
    ZeroConf: {
      enabled: true,
      clientModeOnly: true,
      watchForIPAddressChange: true
    },
    Camera: {
      iosPermissions: {
        camera: 'This app needs access to camera to take photos of receipts and stock items for inventory management.',
        photos: 'This app needs access to photo library to select existing photos of receipts and stock items for inventory management.'
      }
    }
  }
};

export default config;
