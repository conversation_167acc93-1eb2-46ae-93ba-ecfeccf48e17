# 🚀 Production Configuration Guide

## 🌐 **Hosted Server URL Configuration**

Your hosted server URL: `https://bistro.icu`

### **How Static Apps Connect to Your Server** 📱

1. **🌐 Online Mode**: Mobile/Electron apps call your hosted server APIs
2. **💾 Offline Mode**: Apps use local PouchDB storage
3. **🔄 Auto-Sync**: When connection restored, syncs with remote CouchDB

## ⚙️ **Environment Variables**

### **Option 1: Set Environment Variable** 🔧
```bash
export NEXT_PUBLIC_REMOTE_SERVER_URL=https://bistro.icu
```

### **Option 2: Create .env.production** 📝
```bash
# Create .env.production file
echo "NEXT_PUBLIC_REMOTE_SERVER_URL=https://bistro.icu" > .env.production
```

### **Option 3: Use Default (Already Set!)** ✅
The URL is now hardcoded as default in:
- `scripts/capacitor-prod-android.js`
- `scripts/capacitor-prod-ios.js` 
- `lib/build-config.ts`

## 🚀 **Build Commands with Your Server**

### **Android Production Build** 📱
```bash
# Uses your hosted server automatically
npm run cap:prod:android
```

### **iOS Production Build** 🍎
```bash
# Uses your hosted server automatically  
npm run cap:prod:ios
```

### **Custom Server URL** 🔧
```bash
# Override with different server
NEXT_PUBLIC_REMOTE_SERVER_URL=https://your-other-server.com npm run cap:prod:android
```

## 🎯 **How It Works**

### **API Call Flow** 🔄

1. **Static App Starts** → Loads from local files
2. **API Call Made** → Checks connectivity
3. **If Online** → Calls `https://bistro.icu/api/endpoint`
4. **If Offline** → Uses local PouchDB data
5. **When Back Online** → Syncs changes to remote

### **Example API Call** 💻
```typescript
// This automatically handles online/offline
import { offlineApi } from '@/lib/api/offline-api-client';

const result = await offlineApi.getStaff('restaurant123');
// Online: calls your hosted server
// Offline: uses local PouchDB
```

## 🔧 **Testing Connectivity**

### **Test Your Server** 🌐
```bash
# Test if your server is reachable
curl https://bistro.icu/api/health
```

### **Test in Mobile App** 📱
1. Build and install APK
2. Connect to WiFi → Should show "Online" mode
3. Turn off WiFi → Should switch to "Offline" mode
4. Turn WiFi back on → Should sync changes

## 🚨 **Important Notes**

- ✅ **CORS**: Make sure your hosted server allows requests from mobile apps
- ✅ **HTTPS**: Your server uses HTTPS (required for mobile apps)
- ✅ **Health Check**: Ensure `/api/health` endpoint exists
- ✅ **Authentication**: Mobile apps will send auth tokens to your server

## 🎉 **Ready to Build!**

Your configuration is now complete! Run:
```bash
npm run cap:prod:android
```

The mobile app will automatically:
- 🌐 Use your hosted server when online
- 💾 Work offline with local data
- 🔄 Sync when connection is restored