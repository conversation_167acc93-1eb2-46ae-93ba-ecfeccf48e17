# 🍎 macOS Local Release Guide

This guide explains how to build and distribute macOS releases locally (without cloud deployment).

## 🚀 Quick Start

### Build macOS Release
```bash
# Full deployment (recommended)
npm run deploy:macos

# Or just build without organization
npm run electron:build:mac
```

## 📋 Available Commands

### Main Commands
- `npm run electron:build:mac` - Build macOS app (DMG + ZIP)
- `npm run deploy:macos` - Full local deployment with organization
- `cd electron && npm run electron:build:mac` - Direct electron build
- `cd electron && npm run electron:build:mac:slim` - Slim build (macOS-only resources)

### Build Variants
- **Standard Build**: Includes both macOS and Windows CouchDB resources
- **Slim Build**: macOS-only resources (smaller file size)

## 📁 Output Structure

### After `npm run deploy:macos`:
```
releases/macos/
├── latest -> 20241215_143022/     # Symlink to latest release
├── 20241215_143022/               # Timestamped release
│   ├── Shop-1.0.0.dmg            # DMG installer
│   ├── Shop-1.0.0-mac.zip        # ZIP archive
│   ├── latest-mac.yml             # Auto-updater metadata
│   └── release-info.txt           # Build information
└── 20241214_120000/               # Previous release
    └── ...
```

### After `npm run electron:build:mac`:
```
electron/release/
├── Shop-1.0.0.dmg                # DMG installer
├── Shop-1.0.0-mac.zip            # ZIP archive
├── latest-mac.yml                 # Auto-updater metadata
└── mac/                           # Unpacked app directory
    └── Bistro.app/
```

## 🛠️ Requirements

### System Requirements
- **macOS**: Required for building macOS apps
- **Node.js**: Version 16+ recommended
- **Xcode Command Line Tools**: For native module compilation

### Install Xcode Tools
```bash
xcode-select --install
```

## 📦 File Types Explained

### DMG (Disk Image)
- **Purpose**: Standard macOS installer format
- **Usage**: Double-click to mount, drag app to Applications
- **Best for**: End-user distribution

### ZIP Archive
- **Purpose**: Compressed app bundle
- **Usage**: Extract and move to Applications
- **Best for**: Programmatic distribution, CI/CD

### YML Metadata
- **Purpose**: Auto-updater configuration
- **Usage**: Upload to your update server
- **Contains**: Version info, file hashes, download URLs

## 🔧 Configuration

### Electron Builder Config
The macOS build configuration is in `electron/package.json`:

```json
"mac": {
  "category": "public.app-category.business",
  "target": [
    "dmg",
    "zip"
  ]
}
```

### Slim Configuration
For smaller builds, use the slim config in `electron/electron-builder-mac-slim.js`:
- Excludes Windows CouchDB resources
- Supports both x64 and ARM64 architectures
- Includes only macOS-specific files

## 🚀 Distribution Workflow

### 1. Build Release
```bash
npm run deploy:macos
```

### 2. Test Installation
- Navigate to `releases/macos/latest/`
- Test the DMG on a clean Mac
- Verify app functionality

### 3. Distribute
- Share the DMG file with users
- Upload to your website/distribution platform
- Use ZIP for automated deployment

## 🔄 Auto-Updates

### Setup Auto-Updates
1. Upload `latest-mac.yml` to your update server
2. Configure the update URL in `electron/package.json`:
   ```json
   "publish": [
     {
       "provider": "generic",
       "url": "https://your-server.com/updates/"
     }
   ]
   ```

### Update Server Structure
```
https://your-server.com/updates/
├── latest-mac.yml              # Update metadata
├── Shop-1.0.0.dmg             # Current version
└── Shop-1.0.1.dmg             # New version
```

## 🐛 Troubleshooting

### Common Issues

#### "Cannot build macOS on non-macOS platform"
- **Solution**: Use a Mac or macOS virtual machine
- **Alternative**: Use GitHub Actions with macOS runners

#### "Code signing failed"
- **Cause**: Missing Apple Developer certificates
- **Solution**: Either sign with certificates or disable signing:
  ```json
  "mac": {
    "identity": null
  }
  ```

#### "Native module compilation failed"
- **Solution**: Install Xcode Command Line Tools
- **Command**: `xcode-select --install`

#### "Permission denied" when running script
- **Solution**: Make script executable
- **Command**: `chmod +x scripts/deploy-macos-local.sh`

### Build Logs
Check build logs in:
- Terminal output during build
- `electron/release/builder-debug.yml`

## 📊 File Sizes

Typical file sizes for macOS builds:
- **DMG**: ~300-400 MB (includes CouchDB)
- **ZIP**: ~250-350 MB (compressed)
- **Slim builds**: ~20-30% smaller

## 🔗 Related Commands

### Development
- `npm run electron:dev` - Start development mode
- `npm run electron:cleanup` - Clean development files

### Cross-Platform
- `npm run electron:build:win` - Build Windows version
- `npm run electron:build` - Build for current platform

### Deployment
- `npm run deploy:windows:r2` - Deploy Windows to cloud
- `npm run deploy:macos` - Deploy macOS locally

---

## 📝 Notes

- macOS builds require macOS host system
- DMG files are the standard for macOS distribution
- Auto-updater requires proper server setup
- Code signing improves user trust but requires Apple Developer account

For questions or issues, check the main project documentation or create an issue.