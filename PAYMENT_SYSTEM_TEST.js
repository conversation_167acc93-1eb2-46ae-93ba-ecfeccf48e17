/**
 * Payment System Robustness Test Script
 * 
 * This script tests the fixes implemented for payment system consistency issues:
 * 1. Database index performance
 * 2. Payment filtering logic
 * 3. Error handling improvements
 * 4. Transaction-like behavior
 */

// Test configuration
const TEST_CONFIG = {
  staffId: 'test-staff-123',
  testPayments: [
    { type: 'SALARY', amount: 3000, status: 'COMPLETED' },
    { type: 'BONUS', amount: 500, status: 'PENDING' },
    { type: 'DEDUCTION', amount: 200, status: 'PENDING' },
    { type: 'ADVANCE', amount: 1000, status: 'COMPLETED' },
    { type: 'SHIFT_PAYMENT', amount: 150, status: 'COMPLETED' }
  ]
};

/**
 * Test 1: Database Index Performance
 */
async function testDatabaseIndexes() {
  console.log('🧪 Testing Database Index Performance...');
  
  try {
    const { createPerStaffIndexes } = await import('./lib/db/v4/operations/per-staff-ops.js');
    
    const startTime = performance.now();
    await createPerStaffIndexes();
    const endTime = performance.now();
    
    console.log(`✅ Index creation completed in ${endTime - startTime}ms`);
    
    // Test query performance with indexes
    const { getStaffPayments } = await import('./lib/db/v4/operations/per-staff-ops.js');
    
    const queryStartTime = performance.now();
    const payments = await getStaffPayments(TEST_CONFIG.staffId);
    const queryEndTime = performance.now();
    
    console.log(`✅ Payment query completed in ${queryEndTime - queryStartTime}ms`);
    console.log(`📊 Retrieved ${payments.length} payments`);
    
    return true;
  } catch (error) {
    console.error('❌ Database index test failed:', error);
    return false;
  }
}

/**
 * Test 2: Payment Consolidation Logic
 */
async function testPaymentConsolidation() {
  console.log('🧪 Testing Payment Consolidation Logic...');
  
  try {
    const { getConsolidatedStaffPayments } = await import('./lib/services/staff-payment-service.js');
    
    const consolidatedPayments = await getConsolidatedStaffPayments(TEST_CONFIG.staffId);
    
    console.log(`✅ Consolidated ${consolidatedPayments.length} payments`);
    
    // Verify consolidation logic
    let consolidatedCount = 0;
    let standaloneCount = 0;
    
    consolidatedPayments.forEach(payment => {
      if (payment.isConsolidated) {
        consolidatedCount++;
        console.log(`   📋 Consolidated payment: ${payment._id} (${payment.amount})`);
        
        // Verify components exist
        if (payment.consolidatedComponents) {
          const components = payment.consolidatedComponents;
          console.log(`      Base: ${components.baseSalary || 0}`);
          console.log(`      Bonuses: ${components.bonuses.length} items`);
          console.log(`      Deductions: ${components.deductions.length} items`);
          // ✅ Simplified: Advances are now handled as separate payments
          console.log(`      Net: ${components.netAmount}`);
        }
      } else {
        standaloneCount++;
        console.log(`   📄 Standalone payment: ${payment._id} (${payment.paymentType}, ${payment.amount})`);
      }
    });
    
    console.log(`📊 Summary: ${consolidatedCount} consolidated, ${standaloneCount} standalone`);
    
    return true;
  } catch (error) {
    console.error('❌ Payment consolidation test failed:', error);
    return false;
  }
}

/**
 * Test 3: Error Handling and Recovery
 */
async function testErrorHandling() {
  console.log('🧪 Testing Error Handling and Recovery...');
  
  try {
    const { getConsolidatedStaffPayments } = await import('./lib/services/staff-payment-service.js');
    
    // Test with non-existent staff ID
    try {
      await getConsolidatedStaffPayments('non-existent-staff');
      console.log('✅ Handled non-existent staff gracefully');
    } catch (error) {
      console.log(`✅ Properly threw error for non-existent staff: ${error.message}`);
    }
    
    // Test with invalid data
    const { getStaffPayments } = await import('./lib/db/v4/operations/per-staff-ops.js');
    
    try {
      await getStaffPayments('');
      console.log('⚠️ Should have thrown error for empty staff ID');
    } catch (error) {
      console.log(`✅ Properly handled empty staff ID: ${error.message}`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error handling test failed:', error);
    return false;
  }
}

/**
 * Test 4: Transaction-like Behavior
 */
async function testTransactionBehavior() {
  console.log('🧪 Testing Transaction-like Behavior...');
  
  try {
    const { completePendingAdjustments } = await import('./lib/services/staff-payment-service.js');
    
    // Test with empty adjustment list
    await completePendingAdjustments(TEST_CONFIG.staffId, [], 'test-payroll-123');
    console.log('✅ Handled empty adjustment list gracefully');
    
    // Test with invalid adjustment IDs
    try {
      await completePendingAdjustments(TEST_CONFIG.staffId, ['invalid-id-1', 'invalid-id-2'], 'test-payroll-123');
      console.log('✅ Handled invalid adjustment IDs gracefully');
    } catch (error) {
      console.log(`✅ Properly handled invalid adjustments: ${error.message}`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Transaction behavior test failed:', error);
    return false;
  }
}

/**
 * Test 5: Payment History Consistency
 */
async function testPaymentHistoryConsistency() {
  console.log('🧪 Testing Payment History Consistency...');
  
  try {
    const { getConsolidatedStaffPayments } = await import('./lib/services/staff-payment-service.js');
    const { getStaffPayments } = await import('./lib/db/v4/operations/per-staff-ops.js');
    
    // Get both raw and consolidated payments
    const rawPayments = await getStaffPayments(TEST_CONFIG.staffId);
    const consolidatedPayments = await getConsolidatedStaffPayments(TEST_CONFIG.staffId);
    
    console.log(`📊 Raw payments: ${rawPayments.length}`);
    console.log(`📊 Consolidated view: ${consolidatedPayments.length}`);
    
    // Verify no payments are lost in consolidation
    const rawPaymentIds = new Set(rawPayments.map(p => p._id));
    const consolidatedPaymentIds = new Set();
    
    consolidatedPayments.forEach(payment => {
      if (payment.isConsolidated && payment.consolidatedComponents) {
        // Add main payment ID
        consolidatedPaymentIds.add(payment._id);
        
        // Add component payment IDs
        payment.consolidatedComponents.bonuses.forEach(b => consolidatedPaymentIds.add(b.id));
        payment.consolidatedComponents.deductions.forEach(d => consolidatedPaymentIds.add(d.id));
      } else {
        consolidatedPaymentIds.add(payment._id);
      }
    });
    
    const missingPayments = [...rawPaymentIds].filter(id => !consolidatedPaymentIds.has(id));
    
    if (missingPayments.length === 0) {
      console.log('✅ All payments accounted for in consolidated view');
    } else {
      console.log(`⚠️ Missing payments in consolidated view: ${missingPayments.length}`);
      missingPayments.forEach(id => {
        const payment = rawPayments.find(p => p._id === id);
        console.log(`   Missing: ${id} (${payment?.paymentType}, ${payment?.status})`);
      });
    }
    
    return missingPayments.length === 0;
  } catch (error) {
    console.error('❌ Payment history consistency test failed:', error);
    return false;
  }
}

/**
 * Main Test Runner
 */
async function runAllTests() {
  console.log('🚀 Starting Payment System Robustness Tests\n');
  
  const tests = [
    { name: 'Database Indexes', fn: testDatabaseIndexes },
    { name: 'Payment Consolidation', fn: testPaymentConsolidation },
    { name: 'Error Handling', fn: testErrorHandling },
    { name: 'Transaction Behavior', fn: testTransactionBehavior },
    { name: 'Payment History Consistency', fn: testPaymentHistoryConsistency }
  ];
  
  const results = [];
  
  for (const test of tests) {
    console.log(`\n${'='.repeat(50)}`);
    console.log(`Running: ${test.name}`);
    console.log('='.repeat(50));
    
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
      console.log(`${result ? '✅' : '❌'} ${test.name}: ${result ? 'PASSED' : 'FAILED'}`);
    } catch (error) {
      results.push({ name: test.name, passed: false, error: error.message });
      console.log(`❌ ${test.name}: FAILED - ${error.message}`);
    }
  }
  
  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('TEST SUMMARY');
  console.log('='.repeat(50));
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}`);
    if (result.error) {
      console.log(`      Error: ${result.error}`);
    }
  });
  
  console.log(`\n📊 Overall: ${passed}/${total} tests passed (${Math.round(passed/total*100)}%)`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! Payment system is robust.');
  } else {
    console.log('⚠️ Some tests failed. Review the issues above.');
  }
  
  return passed === total;
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    testDatabaseIndexes,
    testPaymentConsolidation,
    testErrorHandling,
    testTransactionBehavior,
    testPaymentHistoryConsistency
  };
}

// Run tests if this script is executed directly
if (typeof window === 'undefined' && require.main === module) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}