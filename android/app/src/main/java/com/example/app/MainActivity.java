package com.example.app;

import com.getcapacitor.BridgeActivity;
import android.os.Bundle;
import io.trik.capacitor.zeroconf.ZeroConfPlugin;
import com.capacitorjs.barcodescanner.CapacitorBarcodeScannerPlugin;

public class MainActivity extends BridgeActivity {
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Explicitly register plugins for ZeroConf discovery (mobile client)
        // and barcode scanning
        registerPlugin(ZeroConfPlugin.class);
        registerPlugin(CapacitorBarcodeScannerPlugin.class);
    }
}
