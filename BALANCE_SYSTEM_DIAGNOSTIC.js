/**
 * Balance System Diagnostic Tool
 * 
 * This tool helps diagnose issues with the balance system integration
 * and checks if bonuses/deductions/advances are properly stored and retrieved.
 */

// Test configuration
const DIAGNOSTIC_CONFIG = {
  testStaffId: 'staff-test-123', // Replace with actual staff ID
  testBonus: {
    amount: 500,
    reason: 'Test bonus for diagnostic'
  }
};

/**
 * Test 1: Check Database Initialization
 */
async function testDatabaseInitialization() {
  console.log('🔍 Test 1: Database Initialization');
  
  try {
    // Import database instance
    const { databaseV4 } = await import('./lib/db/v4/core/db-instance.js');
    
    console.log('📊 Database status:', {
      isInitialized: databaseV4.isInitialized,
      restaurantId: databaseV4.getCurrentRestaurantId(),
      dbName: databaseV4.dbName
    });
    
    if (!databaseV4.isInitialized) {
      console.warn('⚠️ Database not initialized - this could be the issue');
      return false;
    }
    
    console.log('✅ Database is properly initialized');
    return true;
    
  } catch (error) {
    console.error('❌ Database initialization check failed:', error);
    return false;
  }
}

/**
 * Test 2: Check New Balance Service Functions
 */
async function testNewBalanceService() {
  console.log('🔍 Test 2: New Balance Service Functions');
  
  try {
    // Import new balance service
    const { getAllBalances, addBonus } = await import('./lib/services/new-staff-balance-service.js');
    
    console.log('📋 Functions available:', {
      getAllBalances: typeof getAllBalances,
      addBonus: typeof addBonus
    });
    
    if (typeof getAllBalances !== 'function' || typeof addBonus !== 'function') {
      console.error('❌ Required functions not available');
      return false;
    }
    
    console.log('✅ New balance service functions are available');
    return true;
    
  } catch (error) {
    console.error('❌ New balance service import failed:', error);
    return false;
  }
}

/**
 * Test 3: Test Balance Retrieval
 */
async function testBalanceRetrieval(staffId) {
  console.log('🔍 Test 3: Balance Retrieval for staff:', staffId);
  
  try {
    const { getAllBalances } = await import('./lib/services/new-staff-balance-service.js');
    
    console.log('📊 Attempting to get balances...');
    const balances = await getAllBalances(staffId);
    
    console.log('✅ Balance retrieval successful:', balances);
    
    if (balances.bonuses > 0 || balances.deductions > 0 || balances.advances > 0) {
      console.log('💰 Found existing balances - system is working');
    } else {
      console.log('📝 No balances found - either none exist or there\'s an issue');
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Balance retrieval failed:', error);
    return false;
  }
}

/**
 * Test 4: Test Balance Creation
 */
async function testBalanceCreation(staffId) {
  console.log('🔍 Test 4: Balance Creation for staff:', staffId);
  
  try {
    const { addBonus, getAllBalances } = await import('./lib/services/new-staff-balance-service.js');
    
    // Get balances before
    const balancesBefore = await getAllBalances(staffId);
    console.log('📊 Balances before test bonus:', balancesBefore);
    
    // Add test bonus
    console.log('💰 Adding test bonus...');
    const bonusEntry = await addBonus({
      staffId: staffId,
      amount: DIAGNOSTIC_CONFIG.testBonus.amount,
      reason: DIAGNOSTIC_CONFIG.testBonus.reason
    });
    
    console.log('✅ Test bonus created:', {
      id: bonusEntry._id,
      amount: bonusEntry.amount,
      reason: bonusEntry.reason,
      isUsed: bonusEntry.isUsed
    });
    
    // Get balances after
    const balancesAfter = await getAllBalances(staffId);
    console.log('📊 Balances after test bonus:', balancesAfter);
    
    // Verify the bonus was added
    const bonusIncrease = balancesAfter.bonuses - balancesBefore.bonuses;
    if (bonusIncrease === DIAGNOSTIC_CONFIG.testBonus.amount) {
      console.log('✅ Bonus correctly added to balance');
      return true;
    } else {
      console.error('❌ Bonus not reflected in balance. Expected increase:', DIAGNOSTIC_CONFIG.testBonus.amount, 'Actual increase:', bonusIncrease);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Balance creation test failed:', error);
    return false;
  }
}

/**
 * Test 5: Check Old vs New System
 */
async function testOldVsNewSystem(staffId) {
  console.log('🔍 Test 5: Old vs New System Comparison for staff:', staffId);
  
  try {
    // Get balances from new system
    const { getAllBalances } = await import('./lib/services/new-staff-balance-service.js');
    const newBalances = await getAllBalances(staffId);
    
    // Get balances from old system
    const { getStaffFinancialBalance } = await import('./lib/services/staff-payment-service.js');
    const oldBalances = await getStaffFinancialBalance(staffId);
    
    console.log('📊 Balance comparison:');
    console.log('New System:', newBalances);
    console.log('Old System:', oldBalances);
    
    // Check for discrepancies
    const discrepancies = [];
    if (Math.abs(newBalances.bonuses - (oldBalances.pendingBonuses || 0)) > 0.01) {
      discrepancies.push('bonuses');
    }
    if (Math.abs(newBalances.deductions - (oldBalances.pendingDeductions || 0)) > 0.01) {
      discrepancies.push('deductions');
    }
    if (Math.abs(newBalances.advances - (oldBalances.advanceBalance || 0)) > 0.01) {
      discrepancies.push('advances');
    }
    
    if (discrepancies.length > 0) {
      console.warn('⚠️ Discrepancies found in:', discrepancies);
      console.log('💡 This suggests data exists in old system but not new system');
    } else {
      console.log('✅ Both systems show consistent data');
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ System comparison failed:', error);
    return false;
  }
}

/**
 * Run Full Diagnostic
 */
async function runFullDiagnostic(staffId = DIAGNOSTIC_CONFIG.testStaffId) {
  console.log('🚀 Starting Balance System Diagnostic\n');
  console.log('📋 Testing with staff ID:', staffId);
  console.log('=' .repeat(50));
  
  const tests = [
    { name: 'Database Initialization', fn: () => testDatabaseInitialization() },
    { name: 'New Balance Service', fn: () => testNewBalanceService() },
    { name: 'Balance Retrieval', fn: () => testBalanceRetrieval(staffId) },
    { name: 'Balance Creation', fn: () => testBalanceCreation(staffId) },
    { name: 'Old vs New System', fn: () => testOldVsNewSystem(staffId) }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      console.log('\n' + '-'.repeat(30));
      const result = await test.fn();
      if (result) {
        passed++;
        console.log(`✅ ${test.name}: PASSED`);
      } else {
        failed++;
        console.log(`❌ ${test.name}: FAILED`);
      }
    } catch (error) {
      failed++;
      console.log(`❌ ${test.name}: ERROR - ${error.message}`);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 Diagnostic Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed! Balance system is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the logs above for details.');
    
    // Provide recommendations
    console.log('\n💡 Recommendations:');
    if (failed >= 3) {
      console.log('- Database may not be properly initialized');
      console.log('- Try refreshing the page and running diagnostic again');
    }
    console.log('- Check browser console for additional error messages');
    console.log('- Verify staff ID exists in the system');
  }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runFullDiagnostic, testDatabaseInitialization, testNewBalanceService, testBalanceRetrieval, testBalanceCreation, testOldVsNewSystem };
} else {
  window.BalanceSystemDiagnostic = { runFullDiagnostic, testDatabaseInitialization, testNewBalanceService, testBalanceRetrieval, testBalanceCreation, testOldVsNewSystem };
}

// Auto-run if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  // Get staff ID from command line argument if provided
  const staffId = process.argv[2] || DIAGNOSTIC_CONFIG.testStaffId;
  runFullDiagnostic(staffId).catch(console.error);
}
