import { NextRequest, NextResponse } from 'next/server';
import { jwtDecode } from 'jwt-decode';

interface JWTPayload {
  sub: string;
  name: string;
  email?: string;
  role: string;
  restaurantId: string;
  permissions?: any;
  exp: number;
}

// 🚨 CRITICAL: Skip middleware entirely for static/electron builds to allow static export
// Static builds handle auth client-side and reach out to remote server when needed
export function middleware(request: NextRequest) {
  // Skip middleware entirely for static/electron builds to allow static export
  if (process.env.BUILD_TARGET === 'electron' || process.env.BUILD_TARGET === 'static') {
    return NextResponse.next();
  }

  // Handle CORS for API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new NextResponse(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
          'Access-Control-Max-Age': '86400',
        },
      });
    }

    // Add CORS headers to all API responses
    const response = NextResponse.next();
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    return response;
  }
  
  // Get path and check if it's a protected route
  const path = request.nextUrl.pathname;

  // Define public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/auth',
    '/landing',
    '/api/auth/login',
    '/api/auth/register',
    '/api/auth/refresh',
    '/api/auth/demo',
    '/api/health',
    '/api/updates/check',
    '/api/updates/download',
    '/api/releases/latest'
  ];

  // For web builds, only allow landing page and auth routes
  const isWebBuild = process.env.BUILD_TARGET === 'web' || process.env.NEXT_PUBLIC_BUILD_TARGET === 'web';
  
  if (isWebBuild) {
    const webAllowedRoutes = [
      '/',
      '/landing',
      '/auth',
      '/api/auth/login',
      '/api/auth/register',
      '/api/health'
    ];
    
    const isWebAllowed = webAllowedRoutes.some(route => path === route || path.startsWith(route + '/'));
    
    if (!isWebAllowed && !path.startsWith('/_next') && !path.startsWith('/public') && !path.match(/\.(ico|png|jpg|jpeg|svg|css|js|woff|woff2|ttf|otf)$/)) {
      // For web builds, redirect to landing page instead of protected routes
      return NextResponse.redirect(new URL('/landing', request.url));
    }
  }

  // API routes that don't require authentication
  const authApiRoutes = [
    '/api/auth/login',
    '/api/auth/register',
    '/api/auth/refresh',
    '/api/auth/demo',
    '/api/health'
  ];

  // Get token from Authorization header or cookies
  const authHeader = request.headers.get('authorization');
  const token = authHeader?.replace('Bearer ', '') || request.cookies.get('token')?.value;

  // Check if the path is a public route
  if (publicRoutes.includes(path) || path.startsWith('/_next') || path.startsWith('/public')) {
    return NextResponse.next();
  }

  // Public files like images, etc.
  if (path.match(/\.(ico|png|jpg|jpeg|svg|css|js|woff|woff2|ttf|otf)$/)) {
    return NextResponse.next();
  }

  // If no token, check if this is a static app that might have sessions in localStorage
  if (!token) {
    // Allow API auth routes without token
    if (authApiRoutes.includes(path)) {
      return NextResponse.next();
    }

    // If this is an API route, return 401 error (but allow static apps to handle offline)
    if (path.startsWith('/api/')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // For static/electron/mobile apps, ALWAYS allow initial page load without token
    // The client-side auth will handle session restoration and redirect if needed
    const isStaticApp = request.headers.get('user-agent')?.includes('Electron') || 
                       request.headers.get('user-agent')?.includes('CapacitoriOS') ||
                       request.headers.get('user-agent')?.includes('CapacitorAndroid') ||
                       request.headers.get('x-app-type') === 'static' ||
                       process.env.NODE_ENV === 'development';
    
    if (isStaticApp && !path.startsWith('/api/')) {
      console.log('🖥️ [Middleware] Allowing static/mobile app to load without token for client-side auth check');
      return NextResponse.next();
    }

    // For web apps, redirect to login
    const loginUrl = new URL('/auth', request.url);
    loginUrl.searchParams.set('redirect', path);
    return NextResponse.redirect(loginUrl);
  }

  // If we have a token, verify it's valid (simplified check)
  // In a real app, you'd verify the JWT signature here
  if (token && token.length > 10) {
    // Token exists and looks valid, allow the request
    return NextResponse.next();
  }

  // Invalid token, redirect to auth
  const authUrl = new URL('/auth', request.url);
  authUrl.searchParams.set('redirect', path);
  return NextResponse.redirect(authUrl);
}

export const config = {
  // Only run middleware for specific paths, exclude static assets and API routes that don't need auth
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * Skip middleware entirely for static builds
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};