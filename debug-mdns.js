const bonjour = require('bonjour')();
const os = require('os');

console.log('🔍 mDNS Debug Tool Starting...\n');

// Get network interfaces
const networkInterfaces = os.networkInterfaces();
console.log('📡 Available Network Interfaces:');
for (const interfaceName in networkInterfaces) {
  const addresses = networkInterfaces[interfaceName];
  if (addresses) {
    for (const address of addresses) {
      if (!address.internal && address.family === 'IPv4') {
        console.log(`  ${interfaceName}: ${address.address}`);
      }
    }
  }
}

// Test service configuration
const testServiceConfig = {
  name: 'DebugTest-PouchDB-Test',
  type: 'http',
  port: 9999,
  txt: {
    id: 'debug-test-123',
    version: '1.0',
    platform: 'debug'
  }
};

console.log('\n🧪 Publishing test service with config:');
console.log(JSON.stringify(testServiceConfig, null, 2));

// Publish the service
const service = bonjour.publish(testServiceConfig);

service.on('up', () => {
  console.log('✅ Service published successfully!');
  console.log(`   Name: ${service.name}`);
  console.log(`   Type: ${service.type}`);
  console.log(`   Port: ${service.port}`);
  console.log(`   Host: ${service.host}`);
});

service.on('error', (err) => {
  console.error('❌ Service publish error:', err);
});

// Browse for our own service
console.log('\n👀 Browsing for HTTP services...');
const browser = bonjour.find({ type: 'http' });

browser.on('up', (service) => {
  console.log(`📢 Found service: ${service.name} at ${service.host}:${service.port}`);
  if (service.name.includes('DebugTest')) {
    console.log('🎉 SUCCESS: Our test service is discoverable!');
  }
});

browser.on('down', (service) => {
  console.log(`📴 Service down: ${service.name}`);
});

// Keep running for 30 seconds
console.log('\n⏰ Running for 30 seconds to test discovery...');
setTimeout(() => {
  console.log('\n🔚 Test complete. Cleaning up...');
  browser.stop();
  service.stop();
  bonjour.destroy();
  process.exit(0);
}, 30000);

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping...');
  browser.stop();
  service.stop();
  bonjour.destroy();
  process.exit(0);
}); 