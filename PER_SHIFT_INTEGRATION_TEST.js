/**
 * Per-Shift Payment Integration Test
 * 
 * This test verifies that the per-shift payment system works correctly
 * with the new balance system integration.
 */

// Test configuration
const TEST_CONFIG = {
  staffId: 'test-staff-123',
  testShifts: [
    { shiftId: 'morning', shiftName: 'Morning Shift', rate: 2500, count: 1 },
    { shiftId: 'evening', shiftName: 'Evening Shift', rate: 3000, count: 1 }
  ],
  testBalances: {
    bonuses: 500,
    deductions: 200,
    advances: 1000
  }
};

/**
 * Test 1: Balance Loading with New Service
 */
async function testBalanceLoading() {
  console.log('🧪 Test 1: Balance Loading with New Service');
  
  try {
    // Import the new balance service
    const { getAllBalances } = await import('./lib/services/new-staff-balance-service.js');
    
    // Test balance loading
    const balances = await getAllBalances(TEST_CONFIG.staffId);
    
    console.log('✅ Balance loading successful:', {
      advances: balances.advances,
      deductions: balances.deductions,
      bonuses: balances.bonuses,
      total: balances.total
    });
    
    // Verify structure
    if (typeof balances.advances === 'number' && 
        typeof balances.deductions === 'number' && 
        typeof balances.bonuses === 'number') {
      console.log('✅ Balance structure is correct');
      return true;
    } else {
      console.error('❌ Balance structure is incorrect');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Balance loading failed:', error.message);
    return false;
  }
}

/**
 * Test 2: Per-Shift Payment Snapshot Creation
 */
async function testPerShiftPaymentCreation() {
  console.log('🧪 Test 2: Per-Shift Payment Snapshot Creation');
  
  try {
    // Import the new balance service
    const { createPerShiftPaymentSnapshot } = await import('./lib/services/new-staff-balance-service.js');
    
    // Prepare shift data
    const shiftData = {
      attendanceIds: ['att-1', 'att-2'],
      shiftBreakdown: TEST_CONFIG.testShifts.map(shift => ({
        shiftId: shift.shiftId,
        shiftName: shift.shiftName,
        count: shift.count,
        rate: shift.rate,
        amount: shift.rate * shift.count
      }))
    };
    
    // Test payment snapshot creation
    const snapshot = await createPerShiftPaymentSnapshot({
      staffId: TEST_CONFIG.staffId,
      shiftData,
      useAllBonuses: true,
      useAllDeductions: true,
      useAllAdvances: true,
      notes: 'Test per-shift payment'
    });
    
    console.log('✅ Per-shift payment snapshot created:', {
      id: snapshot._id,
      paymentType: snapshot.paymentType,
      baseSalary: snapshot.baseSalary,
      bonusAmount: snapshot.bonusAmount,
      deductionAmount: snapshot.deductionAmount,
      advanceAmount: snapshot.advanceAmount,
      netAmount: snapshot.netAmount,
      shiftData: snapshot.shiftData
    });
    
    // Verify snapshot structure
    if (snapshot.paymentType === 'SHIFT_PAYMENT' && 
        snapshot.shiftData && 
        snapshot.shiftData.attendanceIds.length > 0 &&
        snapshot.shiftData.shiftBreakdown.length > 0) {
      console.log('✅ Per-shift snapshot structure is correct');
      return true;
    } else {
      console.error('❌ Per-shift snapshot structure is incorrect');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Per-shift payment creation failed:', error.message);
    return false;
  }
}

/**
 * Test 3: Payment Calculation Logic
 */
async function testPaymentCalculation() {
  console.log('🧪 Test 3: Payment Calculation Logic');
  
  try {
    // Calculate expected amounts
    const baseShiftAmount = TEST_CONFIG.testShifts.reduce((sum, shift) => sum + (shift.rate * shift.count), 0);
    const expectedGross = baseShiftAmount + TEST_CONFIG.testBalances.bonuses;
    const expectedDeductions = TEST_CONFIG.testBalances.deductions + Math.min(TEST_CONFIG.testBalances.advances, expectedGross - TEST_CONFIG.testBalances.deductions);
    const expectedNet = Math.max(0, expectedGross - expectedDeductions);
    
    console.log('📊 Expected calculation:', {
      baseShiftAmount,
      bonuses: TEST_CONFIG.testBalances.bonuses,
      deductions: TEST_CONFIG.testBalances.deductions,
      advances: Math.min(TEST_CONFIG.testBalances.advances, expectedGross - TEST_CONFIG.testBalances.deductions),
      grossAmount: expectedGross,
      totalDeductions: expectedDeductions,
      netAmount: expectedNet
    });
    
    console.log('✅ Payment calculation logic verified');
    return true;
    
  } catch (error) {
    console.error('❌ Payment calculation test failed:', error.message);
    return false;
  }
}

/**
 * Test 4: UI Integration Points
 */
async function testUIIntegration() {
  console.log('🧪 Test 4: UI Integration Points');
  
  try {
    // Test that the UI components can import the new functions
    console.log('📋 Checking import compatibility...');
    
    // These should be available for the UI components
    const requiredFunctions = [
      'getAllBalances',
      'createPerShiftPaymentSnapshot'
    ];
    
    const { getAllBalances, createPerShiftPaymentSnapshot } = await import('./lib/services/new-staff-balance-service.js');
    
    if (typeof getAllBalances === 'function' && typeof createPerShiftPaymentSnapshot === 'function') {
      console.log('✅ UI integration functions are available');
      return true;
    } else {
      console.error('❌ UI integration functions are missing');
      return false;
    }
    
  } catch (error) {
    console.error('❌ UI integration test failed:', error.message);
    return false;
  }
}

/**
 * Run All Tests
 */
async function runAllTests() {
  console.log('🚀 Starting Per-Shift Payment Integration Tests\n');
  
  const tests = [
    { name: 'Balance Loading', fn: testBalanceLoading },
    { name: 'Payment Creation', fn: testPerShiftPaymentCreation },
    { name: 'Calculation Logic', fn: testPaymentCalculation },
    { name: 'UI Integration', fn: testUIIntegration }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
        console.log(`✅ ${test.name}: PASSED\n`);
      } else {
        failed++;
        console.log(`❌ ${test.name}: FAILED\n`);
      }
    } catch (error) {
      failed++;
      console.log(`❌ ${test.name}: ERROR - ${error.message}\n`);
    }
  }
  
  console.log('📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed! Per-shift integration is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.');
  }
}

// Export for use in Node.js or browser
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests, testBalanceLoading, testPerShiftPaymentCreation, testPaymentCalculation, testUIIntegration };
} else {
  // Browser environment - attach to window
  window.PerShiftIntegrationTest = { runAllTests, testBalanceLoading, testPerShiftPaymentCreation, testPaymentCalculation, testUIIntegration };
}

// Auto-run if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  runAllTests().catch(console.error);
}
