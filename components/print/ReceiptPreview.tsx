import React, { useEffect, useState } from 'react';
// Removed Button, Receipt, Eye, Printer as they are managed by parent component
// import { Button } from '@/components/ui/button';
import { Printer } from 'lucide-react';
import { printService } from '@/lib/services/print-service';
import { kitchenPrintService } from '@/lib/services/kitchen-print-service';
import { Order } from '@/lib/db/v4/schemas/order-schema';
import { formatCurrency } from '@/lib/utils/currency';

interface ReceiptPreviewProps {
  className?: string;
  restaurantName: string;
  restaurantPhone: string;
  restaurantAddress: string;
  restaurantSecondaryPhone: string;
  restaurantLogoUrl: string;
  restaurantFooter: string;
}

export function ReceiptPreview({
  className,
  restaurantName,
  restaurantPhone,
  restaurantAddress,
  restaurantSecondaryPhone,
  restaurantLogoUrl,
  restaurantFooter,
}: ReceiptPreviewProps) {
  const [receiptHtml, setReceiptHtml] = useState('');
  const [loading, setLoading] = useState(false);

  // Sample order for preview
  const sampleOrder: Order = {
    _id: 'order:20241215-001',
    id: 'order:20241215-001',
    type: 'order_document',
    schemaVersion: 'v4.0',
    tableId: 'table-5',
    orderType: 'dine-in',
    items: [
      {
        id: 'item-1',
        name: 'Pizza Margherita',
        price: 1200,
        quantity: 1,
        menuItemId: 'pizza-margherita',
        addons: [
          { id: 'addon-1', name: 'Extra Cheese', price: 200 }
        ]
      },
      {
        id: 'item-2',
        name: 'Coca Cola',
        price: 300,
        quantity: 2,
        menuItemId: 'coca-cola'
      },
      {
        id: 'item-3',
        name: 'Salade César',
        price: 800,
        quantity: 0, // Voided item
        menuItemId: 'salade-cesar',
        isVoided: true,
        originalQuantity: 1,
        voidedQuantity: 1
      }
    ],
    total: 2000 - 800, // Adjusted total for voided item
    status: 'completed',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  const samplePayment = {
    method: 'cash',
    received: 1600, 
    change: 0
  };

  const generatePreview = async () => {
    setLoading(true);
    try {
      // Set restaurant info for print service to generate header/footer correctly
      printService.setRestaurantInfo(
        restaurantName,
        restaurantPhone,
        restaurantAddress,
        restaurantSecondaryPhone,
        restaurantLogoUrl,
        restaurantFooter
      );

      const restaurantHeaderHtml = await printService.generateRestaurantHeader({
        header: 14, // Slightly smaller header for compactness
        normal: 9, // Smaller font for general text
        bold: 11,
      });

      const orderItemsHtml = sampleOrder.items
        .map((item) => {
          const itemTotal = item.isVoided ? 0 : item.price * item.quantity;
          const addonsHtml = item.addons
            ?.map((addon) => `<div style="padding-left: 8px; font-size: 8px; line-height: 1.1;">+ ${addon.name}</div>`)
            .join('');

          return `
            <div style="display: flex; justify-content: space-between; font-size: 9px; margin-bottom: 1px; line-height: 1.2;">
              <span>${item.name}${item.isVoided ? ' <span style="color:#888;">(REMBOURSÉ)</span>' : ''} x${item.quantity}</span>
              <span>${formatCurrency(itemTotal)}</span>
            </div>
            ${addonsHtml || ''}
          `;
        })
        .join('');

      const footerHtml = await printService.generateRestaurantFooter({
        normal: 9 // Smaller font for footer
      });

      const fullReceiptHtml = `
        <div style="font-family: 'Liberation Mono', monospace; font-size: 9px; line-height: 1.1; color: #000; width: 200px; margin: 0 auto; padding: 4px; box-sizing: border-box; background-color: #fff;">
          ${restaurantHeaderHtml}
          <div style="text-align: center; margin-top: 6px; margin-bottom: 6px;">
            <div style="font-size: 11px; font-weight: bold; margin-bottom: 2px;">RECEIPT</div>
            <div style="font-size: 9px;">Order #${sampleOrder._id.split('-').pop()}</div>
            <div style="font-size: 9px;">${new Date(sampleOrder.createdAt!).toLocaleString()}</div>
          </div>
          <div style="border-top: 1px dashed #bbb; border-bottom: 1px dashed #bbb; padding: 4px 0; margin-bottom: 6px;">
            ${orderItemsHtml}
          </div>
          <div style="display: flex; justify-content: space-between; font-size: 11px; font-weight: bold; margin-top: 6px;">
            <span>TOTAL:</span>
            <span>${formatCurrency(sampleOrder.total)}</span>
          </div>
          <div style="font-size: 9px; margin-top: 6px; line-height: 1.2;">
            <div>Payment Method: ${samplePayment.method.toUpperCase()}</div>
            <div>Amount Received: ${formatCurrency(samplePayment.received)}</div>
            <div>Change: ${formatCurrency(samplePayment.change)}</div>
          </div>
          ${footerHtml}
        </div>
      `;

      setReceiptHtml(fullReceiptHtml);

    } catch (error) {
      console.error('Failed to generate receipt preview:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Regenerate preview whenever relevant props change
    generatePreview();
  }, [restaurantName, restaurantPhone, restaurantAddress, restaurantSecondaryPhone, restaurantLogoUrl, restaurantFooter]);

  return (
    <div className={className}>
      {receiptHtml ? (
        <div className="border rounded-lg p-0 bg-white shadow-inner flex justify-center items-center overflow-hidden" style={{ width: '220px', height: 'auto' }}>
          <div 
            className="receipt-content-wrapper"
            dangerouslySetInnerHTML={{ __html: receiptHtml }}
            style={{
              // Remove conflicting styles, rely on inner HTML styles
            }}
          />
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center h-64 border rounded-lg p-4 text-center text-muted-foreground bg-muted/50 w-full">
          <Printer className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-sm">No preview available</p>
          <p className="text-xs">Configure restaurant information to see preview</p>
        </div>
      )}
    </div>
  );
} 