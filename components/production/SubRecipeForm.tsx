"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { StockItem } from "@/types/stock";
import { SubRecipe } from "@/lib/db/v4/schemas/sub-recipe-schema";
import { Loader2, Plus, Trash2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

// Schema for sub-recipe validation
const subRecipeSchema = z.object({
  name: z.string().min(2, { message: "Le nom doit comporter au moins 2 caractères." }),
  ingredients: z.array(
    z.object({
      stockItemId: z.string().min(1, { message: "Veuillez sélectionner un ingrédient." }),
      quantity: z.coerce.number().positive({ message: "La quantité doit être positive." }),
    })
  ).min(1, { message: "Au moins un ingrédient est requis." }),
  yield: z.object({
    quantity: z.coerce.number().positive({ message: "La quantité produite doit être positive." }),
    unit: z.enum(["kg", "L", "pcs", "g", "ml"], {
      required_error: "Veuillez sélectionner une unité de mesure.",
    }),
  }),
});

interface SubRecipeFormProps {
  onSubmit: (data: z.infer<typeof subRecipeSchema>) => void;
  onCancel: () => void;
  initialData?: SubRecipe | null;
  stockItems: StockItem[];
  isMobile?: boolean;
}

export function SubRecipeForm({ onSubmit, onCancel, initialData, stockItems, isMobile = false }: SubRecipeFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // No categories needed anymore

  // Initialize form with default values or initial data
  const form = useForm<z.infer<typeof subRecipeSchema>>({
    resolver: zodResolver(subRecipeSchema),
    defaultValues: initialData
      ? {
          name: initialData.name,
          ingredients: initialData.ingredients,
          yield: initialData.yield,
        }
      : {
          name: "",
          ingredients: [{ stockItemId: "", quantity: 1 }],
          yield: { quantity: 1, unit: "kg" },
        },
  });

  // Use field array for ingredients
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "ingredients",
  });

  // Handle form submission
  const handleSubmit = async (values: z.infer<typeof subRecipeSchema>) => {
    setIsSubmitting(true);
    try {
      await onSubmit(values);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className={isMobile ? "space-y-3" : "space-y-4"}>
        {/* Name */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className={isMobile ? "text-sm font-semibold" : "text-base font-semibold"}>
                Recipe Name
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Sauce tomate"
                  className={isMobile ? "h-9" : ""}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Yield */}
        <div>
          <h3 className={isMobile ? "text-sm font-semibold mb-2" : "text-base font-semibold mb-3"}>
            Yield Information
          </h3>
          <div className={`grid grid-cols-2 gap-3 ${isMobile ? 'p-3' : 'p-4'} bg-muted/20 rounded-md`}>
            <FormField
              control={form.control}
              name="yield.quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={isMobile ? "text-xs" : "text-sm"}>Quantity</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0.01"
                      step="0.01"
                      className={`font-medium ${isMobile ? 'h-8 text-sm' : ''}`}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="yield.unit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={isMobile ? "text-xs" : "text-sm"}>Unit</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className={isMobile ? 'h-8 text-sm' : ''}>
                        <SelectValue placeholder="Unit" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="kg">kg</SelectItem>
                      <SelectItem value="g">g</SelectItem>
                      <SelectItem value="L">L</SelectItem>
                      <SelectItem value="ml">ml</SelectItem>
                      <SelectItem value="pcs">pcs</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Ingredients */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <FormLabel className={isMobile ? "text-sm font-semibold" : "text-base font-semibold"}>
              Ingredients
            </FormLabel>
            <Button
              type="button"
              variant="outline"
              size={isMobile ? "sm" : "sm"}
              onClick={() => append({ stockItemId: "", quantity: 1 })}
              className={isMobile ? "h-7 px-2 text-xs" : ""}
            >
              <Plus className={`${isMobile ? 'h-3 w-3 mr-1' : 'h-4 w-4 mr-2'}`} />
              {isMobile ? "Add" : "Add Ingredient"}
            </Button>
          </div>

          {fields.length === 0 ? (
            <div className={`text-center ${isMobile ? 'py-4' : 'py-6'} bg-muted/20 rounded-md text-muted-foreground`}>
              <p className={isMobile ? "text-xs" : "text-sm"}>
                No ingredients added. Click "Add" to start.
              </p>
            </div>
          ) : (
            <div className={`space-y-2 ${isMobile ? 'max-h-[200px]' : 'max-h-[300px]'} overflow-y-auto`}>
              {fields.map((field, index) => (
                <div key={field.id} className={`flex items-center gap-2 ${isMobile ? 'p-2' : 'p-3'} bg-muted/20 rounded-md`}>
                  <div className="flex-1 grid grid-cols-3 gap-2 items-center">
                    <div className="col-span-2">
                      <FormField
                        control={form.control}
                        name={`ingredients.${index}.stockItemId`}
                        render={({ field }) => (
                          <FormItem className="space-y-0">
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className={isMobile ? 'h-8 text-xs' : ''}>
                                  <SelectValue placeholder="Select ingredient" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {stockItems.map((item) => (
                                  <SelectItem key={item.id} value={item.id}>
                                    <span className={isMobile ? "text-xs" : ""}>
                                      {item.name} ({item.unit})
                                    </span>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name={`ingredients.${index}.quantity`}
                      render={({ field }) => (
                        <FormItem className="space-y-0">
                          <FormControl>
                            <Input
                              type="number"
                              min="0.01"
                              step="0.01"
                              placeholder="Qty"
                              className={isMobile ? 'h-8 text-xs' : ''}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className={`text-muted-foreground hover:text-destructive ${isMobile ? 'h-7 w-7' : ''}`}
                    onClick={() => remove(index)}
                    disabled={fields.length === 1}
                  >
                    <Trash2 className={isMobile ? "h-3 w-3" : "h-4 w-4"} />
                  </Button>
                </div>
              ))}
            </div>
          )}

          {form.formState.errors.ingredients?.message && (
            <p className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-destructive mt-2`}>
              {form.formState.errors.ingredients.message}
            </p>
          )}
        </div>

        {/* Actions */}
        <div className={`flex ${isMobile ? 'gap-2 pt-2' : 'justify-end gap-3 pt-4'}`}>
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            className={isMobile ? "flex-1 h-9" : ""}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={isSubmitting}
            className={isMobile ? "flex-1 h-9" : ""}
          >
            {isSubmitting && <Loader2 className={`${isMobile ? 'mr-1 h-3 w-3' : 'mr-2 h-4 w-4'} animate-spin`} />}
            {initialData ? "Update" : "Create"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
