"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useCOGSV4 } from "@/lib/hooks/useCOGSV4";
import { useMenuV4 } from '@/lib/hooks/use-menu-v4';
import { useIsMobile } from "@/hooks/use-mobile";
import { Plus, Edit, Trash2, Search, RefreshCw, ChefHat, Package, Clock, Eye, MoreHorizontal, TrendingUp, DollarSign } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { MenuItemRecipeForm } from "./MenuItemRecipeForm";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { formatCurrency } from "@/lib/utils";
import type { MenuItemRecipe } from '@/types/cogs';
import type { Category, MenuItem } from '@/lib/db/v4-menu-service';

export function MenuRecipesList() {
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const { menuItemRecipes, createMenuItemRecipe, updateMenuItemRecipe, deleteMenuItemRecipe, subRecipes } = useCOGSV4();
  const { categories } = useMenuV4();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedRecipe, setSelectedRecipe] = useState<MenuItemRecipe | null>(null);
  const [viewMode, setViewMode] = useState<'form' | 'details'>('form');

  // Get all menu items from all categories
  const menuItems = categories.flatMap((category: Category) =>
    category.items.map((item: MenuItem) => ({
      id: item.id,
      name: item.name,
      categoryName: category.name,
      sizes: category.sizes || [],
      prices: item.prices
    }))
  );

  // Show all recipes (no filtering)
  const filteredRecipes = menuItemRecipes;

  // Handle create/edit recipe
  const handleRecipeSubmit = async (data: any) => {
    try {
      // Find the menu item to get its name
      const menuItem = menuItems.find((item: MenuItem) => item.id === data.menuItemId);
      if (menuItem) {
        data.menuItemName = menuItem.name;
      }

      if (selectedRecipe) {
        await updateMenuItemRecipe(selectedRecipe._id, data);
        toast({
          title: "✅ Updated",
          description: `Recipe for ${data.menuItemName || 'menu item'} updated successfully.`,
          duration: 2000,
        });
      } else {
        await createMenuItemRecipe(data);
        toast({
          title: "✅ Created",
          description: `Recipe for ${data.menuItemName || 'menu item'} created successfully.`,
          duration: 2000,
        });
      }
      setIsFormOpen(false);
      setSelectedRecipe(null);
    } catch (error) {
      toast({
        title: "❌ Error",
        description: "Failed to save recipe.",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  // Handle delete recipe
  const handleDeleteRecipe = async (recipe: MenuItemRecipe) => {
    try {
      await deleteMenuItemRecipe(recipe._id);
      toast({
        title: "🗑️ Deleted",
        description: `Recipe deleted successfully.`,
        duration: 2000,
      });
    } catch (error) {
      toast({
        title: "❌ Error",
        description: "Failed to delete recipe.",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  // Handle edit recipe
  const handleEditRecipe = (recipe: MenuItemRecipe) => {
    setSelectedRecipe(recipe);
    setViewMode('form');
    setIsFormOpen(true);
  };

  // Handle view details
  const handleViewDetails = (recipe: MenuItemRecipe) => {
    setSelectedRecipe(recipe);
    setViewMode('details');
    setIsFormOpen(true);
  };

  // Handle new recipe
  const handleNewRecipe = () => {
    setSelectedRecipe(null);
    setViewMode('form');
    setIsFormOpen(true);
  };

  // Calculate profit margin
  const calculateProfitMargin = (recipe: MenuItemRecipe) => {
    const menuItem = menuItems.find((item: MenuItem) => item.id === recipe.menuItemId);
    if (!menuItem || !recipe.costPerUnit) return null;

    const price = recipe.size ? menuItem.prices[recipe.size] : Object.values(menuItem.prices)[0];
    if (!price) return null;

    const margin = ((price - recipe.costPerUnit) / price) * 100;
    return { margin, price, profit: price - recipe.costPerUnit };
  };

  // Mobile card component
  const MobileRecipeCard = ({ recipe }: { recipe: MenuItemRecipe }) => {
    const menuItem = menuItems.find((item: MenuItem) => item.id === recipe.menuItemId);
    const profitData = calculateProfitMargin(recipe);

    return (
      <Card className="group cursor-pointer transition-all duration-200 hover:shadow-md hover:border-primary/20 active:scale-[0.98]">
        <CardContent className="p-3">
          <div className="flex items-start justify-between mb-2">
            <div className="flex items-start gap-2 min-w-0 flex-1">
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-blue-500/10 to-blue-600/5 shrink-0">
                <ChefHat className="h-4 w-4 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-sm truncate">
                  {recipe.menuItemName || menuItem?.name || "Unknown Item"}
                </h3>
                <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                  <span className="text-muted-foreground">{menuItem?.categoryName}</span>
                  {recipe.size && (
                    <>
                      <span>•</span>
                      <Badge variant="outline" className="text-xs px-1 py-0 h-4">
                        {recipe.size}
                      </Badge>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-7 w-7 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleViewDetails(recipe)}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleEditRecipe(recipe)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => handleDeleteRecipe(recipe)}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          
          {/* Stats row */}
          <div className="grid grid-cols-2 gap-3 pt-2 border-t border-muted/30">
            <div className="text-xs">
              <div className="flex items-center gap-1 text-muted-foreground mb-1">
                <DollarSign className="h-3 w-3" />
                <span>Cost</span>
              </div>
              <span className="font-medium">{formatCurrency(recipe.costPerUnit || 0)}</span>
            </div>
            <div className="text-xs">
              <div className="flex items-center gap-1 text-muted-foreground mb-1">
                <TrendingUp className="h-3 w-3" />
                <span>Margin</span>
              </div>
              {profitData ? (
                <div className="flex items-center gap-1">
                  <span className={`font-medium ${profitData.margin < 0 ? 'text-destructive' : 'text-green-600'}`}>
                    {profitData.margin > 0 ? "+" : ""}{profitData.margin.toFixed(1)}%
                  </span>
                </div>
              ) : (
                <span className="text-muted-foreground">N/A</span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={isMobile ? "space-y-3" : "space-y-4"}>
      {/* Header */}
      <div className={isMobile ? "space-y-2" : "flex justify-between items-center"}>
        <div>
          <h2 className={isMobile ? "text-lg font-semibold" : "text-xl font-semibold"}>Recettes des articles du menu</h2>
          {!isMobile && (
            <p className="text-sm text-muted-foreground">
              Gérez les recettes de vos articles du menu
            </p>
          )}
        </div>
        <div className={isMobile ? "flex gap-2" : "flex justify-end"}>
          <Button size="sm" onClick={handleNewRecipe} className={isMobile ? "flex-1" : ""}>
            <Plus className={`h-4 w-4 ${isMobile ? 'mr-1' : 'mr-2'}`} />
            {isMobile ? "New" : "Nouvelle recette"}
          </Button>
        </div>
      </div>

      {/* Content */}
      {filteredRecipes.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className={isMobile ? "p-6 text-center" : "p-8 text-center"}>
            <ChefHat className={`${isMobile ? 'h-10 w-10' : 'h-12 w-12'} mx-auto mb-3 text-muted-foreground/50`} />
            <h3 className={`${isMobile ? 'text-sm' : 'text-base'} font-medium mb-2`}>
              Aucune recette trouvée
            </h3>
            <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground mb-4`}>
              Créez votre première recette pour commencer.
            </p>
            <Button size="sm" onClick={handleNewRecipe}>
              <Plus className="h-4 w-4 mr-2" />
              Créer la première
            </Button>
          </CardContent>
        </Card>
      ) : isMobile ? (
        <div className="space-y-2">
          {filteredRecipes.map((recipe) => (
            <MobileRecipeCard key={recipe._id} recipe={recipe} />
          ))}
        </div>
      ) : (
        <Card className="border-0 shadow-sm">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="border-b">
                  <TableHead className="font-semibold">Menu Item</TableHead>
                  <TableHead className="font-semibold">Taille</TableHead>
                  <TableHead className="font-semibold">Coût</TableHead>
                  <TableHead className="font-semibold">Prix de vente</TableHead>
                  <TableHead className="font-semibold">Marge bénéficiaire</TableHead>
                  <TableHead className="font-semibold text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRecipes.map((recipe) => {
                  const menuItem = menuItems.find((item: MenuItem) => item.id === recipe.menuItemId);
                  const price = menuItem && recipe.size
                    ? menuItem.prices[recipe.size]
                    : menuItem
                      ? Object.values(menuItem.prices)[0]
                      : 0;
                  const profitData = calculateProfitMargin(recipe);

                  return (
                    <TableRow key={recipe._id} className="cursor-pointer hover:bg-muted/50 transition-colors">
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <div className="p-1.5 rounded-lg bg-gradient-to-br from-blue-500/10 to-blue-600/5">
                            <ChefHat className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <span>{recipe.menuItemName || menuItem?.name || "Unknown Item"}</span>
                            <div className="text-xs text-muted-foreground">{menuItem?.categoryName}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {recipe.size ? (
                          <Badge variant="outline" className="text-xs">
                            {recipe.size}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground text-sm">Default</span>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(recipe.costPerUnit || 0)}
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(price)}
                      </TableCell>
                      <TableCell>
                        {profitData ? (
                          <div className="flex items-center gap-2">
                            <span className={`text-sm font-medium ${profitData.profit < 0 ? "text-destructive" : "text-green-600"}`}>
                              {profitData.profit > 0 ? "+" : ""}{formatCurrency(profitData.profit)}
                            </span>
                            <Badge
                              variant={profitData.margin < 0 ? "destructive" : profitData.margin < 65 ? "secondary" : "default"}
                              className="text-xs"
                            >
                              {profitData.margin > 0 ? "+" : ""}{profitData.margin.toFixed(1)}%
                            </Badge>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">N/A</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewDetails(recipe)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditRecipe(recipe)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDeleteRecipe(recipe)}
                              className="text-destructive focus:text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Form/Details Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className={isMobile ? "sm:max-w-full max-h-[90vh] overflow-hidden" : "max-w-3xl max-h-[90vh] overflow-hidden"}>
          <DialogHeader className={isMobile ? "pb-2" : ""}>
            <DialogTitle className="flex items-center gap-2">
              <ChefHat className="h-5 w-5" />
              {viewMode === 'details' 
                ? selectedRecipe?.menuItemName || "Recipe Details"
                : selectedRecipe 
                  ? "Modifier la recette" 
                  : "Créer une recette"
              }
            </DialogTitle>
          </DialogHeader>
          
          <div className="overflow-y-auto flex-1">
            {viewMode === 'form' ? (
              <MenuItemRecipeForm
                onSubmit={handleRecipeSubmit}
                initialData={selectedRecipe}
                menuItems={menuItems}
                subRecipes={subRecipes}
                onCancel={() => setIsFormOpen(false)}
                isMobile={isMobile}
              />
            ) : selectedRecipe && (
              <div className={isMobile ? "space-y-3" : "space-y-4"}>
                {/* Recipe details view */}
                <div className={isMobile ? "space-y-2" : "space-y-3"}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`${isMobile ? 'text-sm' : 'text-base'} font-medium`}>
                        Menu Item: {selectedRecipe.menuItemName}
                      </p>
                      {selectedRecipe.size && (
                        <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>
                          Size: {selectedRecipe.size}
                        </p>
                      )}
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => {
                        setViewMode('form');
                      }}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  </div>
                  
                  {/* Cost and profit info */}
                  {(() => {
                    const profitData = calculateProfitMargin(selectedRecipe);
                    return profitData && (
                      <div className="grid grid-cols-3 gap-3 p-3 bg-muted/20 rounded-md">
                        <div className="text-center">
                          <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>Cost</p>
                          <p className={`${isMobile ? 'text-sm' : 'text-base'} font-semibold`}>
                            {formatCurrency(selectedRecipe.costPerUnit || 0)}
                          </p>
                        </div>
                        <div className="text-center">
                          <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>Price</p>
                          <p className={`${isMobile ? 'text-sm' : 'text-base'} font-semibold`}>
                            {formatCurrency(profitData.price)}
                          </p>
                        </div>
                        <div className="text-center">
                          <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>Profit</p>
                          <p className={`${isMobile ? 'text-sm' : 'text-base'} font-semibold ${profitData.profit < 0 ? 'text-destructive' : 'text-green-600'}`}>
                            {profitData.profit > 0 ? "+" : ""}{formatCurrency(profitData.profit)}
                          </p>
                        </div>
                      </div>
                    );
                  })()}
                </div>

                {/* Ingredients */}
                <div>
                  <h3 className={`${isMobile ? 'text-sm' : 'text-base'} font-semibold mb-2`}>Ingredients</h3>
                  <ScrollArea className={isMobile ? "h-[200px]" : "h-[300px]"}>
                    <div className="space-y-2">
                      {selectedRecipe.ingredients.map((ingredient, index) => (
                        <div key={index} className={`flex items-center justify-between ${isMobile ? 'p-2' : 'p-3'} bg-muted/20 rounded-md`}>
                          <div className="flex-1">
                            <p className={`${isMobile ? 'text-sm' : 'text-base'} font-medium`}>
                              {'stockItemId' in ingredient ? 'Stock Item' : 'Sub-Recipe'}
                            </p>
                            <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>
                              Quantity: {ingredient.quantity}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
