"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, Plus, Trash2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useStockV4 } from '@/lib/hooks/useStockV4';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { formatCurrency } from "@/lib/utils";

// Schema for menu item recipe validation
const menuItemRecipeSchema = z.object({
  menuItemId: z.string().min(1, { message: "Veuillez sélectionner un article du menu." }),
  size: z.string().optional(),
  ingredients: z.array(
    z.object({
      type: z.enum(["stock", "subRecipe"]),
      stockItemId: z.string().optional(),
      subRecipeId: z.string().optional(),
      quantity: z.coerce.number().positive({ message: "La quantité doit être positive." }),
    }).refine(data => {
      if (data.type === "stock") {
        return !!data.stockItemId;
      } else {
        return !!data.subRecipeId;
      }
    }, {
      message: "Veuillez sélectionner un ingrédient ou une sous-recette.",
      path: ["stockItemId"]
    })
  ).min(1, { message: "Au moins un ingrédient est requis." }),
});

interface MenuItem {
  id: string;
  name: string;
  categoryName: string;
  sizes: string[];
  prices: Record<string, number>;
}

interface MenuItemRecipeFormProps {
  onSubmit: (data: any) => void;
  onCancel: () => void;
  initialData?: any | null;
  menuItems: MenuItem[];
  subRecipes: any[];
  isMobile?: boolean;
}

export function MenuItemRecipeForm({ onSubmit, onCancel, initialData, menuItems, subRecipes, isMobile = false }: MenuItemRecipeFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { stockItems } = useStockV4();
  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem | null>(null);

  // Initialize form with default values or initial data
  const form = useForm<z.infer<typeof menuItemRecipeSchema>>({
    resolver: zodResolver(menuItemRecipeSchema),
    defaultValues: initialData
      ? {
          menuItemId: initialData.menuItemId,
          size: initialData.size || "",
          ingredients: initialData.ingredients.map((ingredient: any) => {
            if ('stockItemId' in ingredient) {
              return {
                type: "stock" as const,
                stockItemId: ingredient.stockItemId,
                quantity: ingredient.quantity,
              };
            } else if ('subRecipeId' in ingredient) {
              return {
                type: "subRecipe" as const,
                subRecipeId: ingredient.subRecipeId,
                quantity: ingredient.quantity,
              };
            } else {
              return ingredient;
            }
          }),
        }
      : {
          menuItemId: "",
          size: "",
          ingredients: [{ type: "stock", stockItemId: "", quantity: 1 }],
        },
  });

  // Use field array for ingredients
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "ingredients",
  });

  // Update selected menu item when menuItemId changes
  useEffect(() => {
    const menuItemId = form.watch("menuItemId");
    if (menuItemId) {
      const menuItem = menuItems.find(item => item.id === menuItemId);
      setSelectedMenuItem(menuItem || null);
    } else {
      setSelectedMenuItem(null);
    }
  }, [form.watch("menuItemId"), menuItems]);

  // Handle form submission
  const handleSubmit = async (values: z.infer<typeof menuItemRecipeSchema>) => {
    setIsSubmitting(true);
    try {
      // Transform the data to match the expected format
      const transformedIngredients = values.ingredients.map(ingredient => {
        if (ingredient.type === "stock") {
          return {
            stockItemId: ingredient.stockItemId!,
            quantity: ingredient.quantity,
          };
        } else {
          return {
            subRecipeId: ingredient.subRecipeId!,
            quantity: ingredient.quantity,
          };
        }
      });

      await onSubmit({
        ...values,
        ingredients: transformedIngredients,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className={isMobile ? "space-y-3" : "space-y-4"}>
        {/* Menu Item Selection */}
        <div className={`${isMobile ? 'p-3' : 'p-4'} bg-muted/20 rounded-md`}>
          <h3 className={isMobile ? "text-sm font-semibold mb-2" : "text-base font-semibold mb-3"}>
            Menu Item Selection
          </h3>
          <div className={`grid ${isMobile ? 'grid-cols-1 gap-2' : 'grid-cols-1 md:grid-cols-2 gap-4'}`}>
            {/* Menu Item */}
            <FormField
              control={form.control}
              name="menuItemId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={isMobile ? "text-xs" : "text-sm"}>Article du menu</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className={isMobile ? 'h-8 text-xs' : ''}>
                        <SelectValue placeholder="Select menu item" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {menuItems.map((item) => (
                        <SelectItem key={item.id} value={item.id}>
                          <span className={isMobile ? "text-xs" : ""}>
                            {item.name} ({item.categoryName})
                          </span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Size (if applicable) */}
            {selectedMenuItem && selectedMenuItem.sizes && selectedMenuItem.sizes.length > 0 && (
              <FormField
                control={form.control}
                name="size"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={isMobile ? "text-xs" : "text-sm"}>Size</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className={isMobile ? 'h-8 text-xs' : ''}>
                          <SelectValue placeholder="Select size" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {selectedMenuItem.sizes.map((size) => (
                          <SelectItem key={size} value={size}>
                            <span className={isMobile ? "text-xs" : ""}>
                              {size} - {formatCurrency(selectedMenuItem.prices[size] || 0)}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>
        </div>

        {/* Ingredients */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <FormLabel className={isMobile ? "text-sm font-semibold" : "text-base font-semibold"}>
              Recipe Ingredients
            </FormLabel>
            <Button
              type="button"
              variant="outline"
              size={isMobile ? "sm" : "sm"}
              onClick={() => append({ type: "stock", stockItemId: "", quantity: 1 })}
              className={isMobile ? "h-7 px-2 text-xs" : ""}
            >
              <Plus className={`${isMobile ? 'h-3 w-3 mr-1' : 'h-4 w-4 mr-2'}`} />
              {isMobile ? "Add" : "Add Ingredient"}
            </Button>
          </div>

          {fields.length === 0 ? (
            <div className={`text-center ${isMobile ? 'py-4' : 'py-6'} bg-muted/20 rounded-md text-muted-foreground`}>
              <p className={isMobile ? "text-xs" : "text-sm"}>
                No ingredients added. Click "Add" to start.
              </p>
            </div>
          ) : (
            <div className={`space-y-2 ${isMobile ? 'max-h-[250px]' : 'max-h-[400px]'} overflow-y-auto`}>
              {fields.map((field, index) => (
                <div key={field.id} className={`${isMobile ? 'p-2' : 'p-3'} bg-muted/20 rounded-md border border-border/50`}>
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex items-center space-x-2">
                      <span className={`bg-primary/10 text-primary font-medium px-2 py-1 rounded ${isMobile ? 'text-xs' : 'text-sm'}`}>
                        #{index + 1}
                      </span>

                      {/* Ingredient Type */}
                      <FormField
                        control={form.control}
                        name={`ingredients.${index}.type`}
                        render={({ field }) => (
                          <FormItem className="space-y-0">
                            <FormControl>
                              <RadioGroup
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                className={`flex ${isMobile ? 'space-x-2' : 'space-x-4'}`}
                              >
                                <div className="flex items-center space-x-1">
                                  <RadioGroupItem value="stock" id={`stock-${index}`} className={isMobile ? "h-3 w-3" : ""} />
                                  <Label htmlFor={`stock-${index}`} className={isMobile ? "text-xs" : "text-sm"}>Stock</Label>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <RadioGroupItem value="subRecipe" id={`subrecipe-${index}`} className={isMobile ? "h-3 w-3" : ""} />
                                  <Label htmlFor={`subrecipe-${index}`} className={isMobile ? "text-xs" : "text-sm"}>Sub-Recipe</Label>
                                </div>
                              </RadioGroup>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className={`text-muted-foreground hover:text-destructive ${isMobile ? 'h-6 w-6' : ''}`}
                      onClick={() => remove(index)}
                      disabled={fields.length === 1}
                    >
                      <Trash2 className={isMobile ? "h-3 w-3" : "h-4 w-4"} />
                    </Button>
                  </div>

                  <div className={`grid ${isMobile ? 'grid-cols-1 gap-2' : 'grid-cols-1 md:grid-cols-4 gap-3'} items-end`}>
                    {/* Stock Item or Sub-Recipe Selection */}
                    <div className={isMobile ? "" : "md:col-span-3"}>
                      {form.watch(`ingredients.${index}.type`) === "stock" ? (
                        <FormField
                          control={form.control}
                          name={`ingredients.${index}.stockItemId`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={isMobile ? "text-xs" : "text-sm"}>Stock Item</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger className={isMobile ? 'h-8 text-xs' : ''}>
                                    <SelectValue placeholder="Select stock item" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {stockItems.map((item) => (
                                    <SelectItem key={item.id} value={item.id}>
                                      <span className={isMobile ? "text-xs" : ""}>
                                        {item.name} ({item.unit})
                                      </span>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      ) : (
                        <FormField
                          control={form.control}
                          name={`ingredients.${index}.subRecipeId`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={isMobile ? "text-xs" : "text-sm"}>Sub-Recipe</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger className={isMobile ? 'h-8 text-xs' : ''}>
                                    <SelectValue placeholder="Select sub-recipe" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {subRecipes.map((subRecipe, idx) => (
                                    <SelectItem key={subRecipe._id + '-' + idx} value={subRecipe._id}>
                                      <span className={isMobile ? "text-xs" : ""}>
                                        {subRecipe.name} ({subRecipe.yield.unit})
                                      </span>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>

                    {/* Quantity */}
                    <FormField
                      control={form.control}
                      name={`ingredients.${index}.quantity`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className={isMobile ? "text-xs" : "text-sm"}>Quantity</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0.01"
                              step="0.01"
                              placeholder="Qty"
                              className={isMobile ? 'h-8 text-xs' : ''}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}

          {form.formState.errors.ingredients?.message && (
            <p className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-destructive mt-2`}>
              {form.formState.errors.ingredients.message}
            </p>
          )}
        </div>

        {/* Actions */}
        <div className={`flex ${isMobile ? 'gap-2 pt-2' : 'justify-end gap-3 pt-4'}`}>
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            className={isMobile ? "flex-1 h-9" : ""}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={isSubmitting}
            className={isMobile ? "flex-1 h-9" : ""}
          >
            {isSubmitting && <Loader2 className={`${isMobile ? 'mr-1 h-3 w-3' : 'mr-2 h-4 w-4'} animate-spin`} />}
            {initialData ? "Update" : "Create"} Recipe
          </Button>
        </div>
      </form>
    </Form>
  );
}
