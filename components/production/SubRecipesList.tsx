"use client";

import { useState } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useCOGSV4 } from "@/lib/hooks/useCOGSV4";
import { useStockV4 } from '@/lib/hooks/useStockV4';
import { useIsMobile } from "@/hooks/use-mobile";
import { Plus, Edit, Trash2, Search, RefreshCw, ChefHat, Package, Clock, Eye, MoreHorizontal, AlertTriangle } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { SubRecipeForm } from "./SubRecipeForm";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { formatCurrency } from "@/lib/utils";
import { SubRecipe } from '@/types/cogs';
import { format, formatDistanceToNow } from 'date-fns';

export function SubRecipesList() {
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const { subRecipes, createSubRecipe, updateSubRecipe, deleteSubRecipe, loading, error } = useCOGSV4();
  const { stockItems } = useStockV4();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedSubRecipe, setSelectedSubRecipe] = useState<SubRecipe | null>(null);
  const [viewMode, setViewMode] = useState<'form' | 'details'>('form');

  // Show all sub-recipes (no filtering)
  const filteredSubRecipes = subRecipes;

  // Handle create/edit sub-recipe
  const handleSubRecipeSubmit = async (data: any) => {
    try {
      if (selectedSubRecipe) {
        await updateSubRecipe(selectedSubRecipe._id, data);
        toast({
          title: "✅ Updated",
          description: `${data.name} updated successfully.`,
          duration: 2000,
        });
      } else {
        await createSubRecipe(data);
        toast({
          title: "✅ Created",
          description: `${data.name} created successfully.`,
          duration: 2000,
        });
      }
      setIsFormOpen(false);
      setSelectedSubRecipe(null);
    } catch (error) {
      toast({
        title: "❌ Error",
        description: "Failed to save sub-recipe.",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  // Handle delete sub-recipe
  const handleDeleteSubRecipe = async (subRecipe: SubRecipe) => {
    try {
      await deleteSubRecipe(subRecipe._id);
      toast({
        title: "🗑️ Deleted",
        description: `${subRecipe.name} deleted successfully.`,
        duration: 2000,
      });
    } catch (error) {
      toast({
        title: "❌ Error",
        description: "Failed to delete sub-recipe.",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  // Handle edit sub-recipe
  const handleEditSubRecipe = (subRecipe: SubRecipe) => {
    setSelectedSubRecipe(subRecipe);
    setViewMode('form');
    setIsFormOpen(true);
  };

  // Handle view details
  const handleViewDetails = (subRecipe: SubRecipe) => {
    setSelectedSubRecipe(subRecipe);
    setViewMode('details');
    setIsFormOpen(true);
  };

  // Handle new sub-recipe
  const handleNewSubRecipe = () => {
    setSelectedSubRecipe(null);
    setViewMode('form');
    setIsFormOpen(true);
  };

  // Get stock item name by ID
  const getStockItemName = (stockItemId: string) => {
    const stockItem = stockItems.find((item) => item.id === stockItemId);
    return stockItem ? stockItem.name : 'Unknown Item';
  };

  // Get stock item unit by ID
  const getStockItemUnit = (stockItemId: string) => {
    const stockItem = stockItems.find((item) => item.id === stockItemId);
    return stockItem ? stockItem.unit : '';
  };

  // Get stock item cost by ID
  const getStockItemCost = (stockItemId: string) => {
    const stockItem = stockItems.find((item) => item.id === stockItemId);
    return stockItem && stockItem.costPerUnit ? stockItem.costPerUnit : 0;
  };

  // Calculate total cost for an ingredient
  const calculateIngredientCost = (stockItemId: string, quantity: number) => {
    const costPerUnit = getStockItemCost(stockItemId);
    return costPerUnit * quantity;
  };

  // Mobile card component
  const MobileSubRecipeCard = ({ subRecipe }: { subRecipe: SubRecipe }) => {
    const totalCost = subRecipe.ingredients.reduce(
      (sum, ingredient) => sum + calculateIngredientCost(ingredient.stockItemId, ingredient.quantity),
      0
    );

    return (
      <Card className="group cursor-pointer transition-all duration-200 hover:shadow-md hover:border-primary/20 active:scale-[0.98]">
        <CardContent className="p-3">
          <div className="flex items-start justify-between mb-2">
            <div className="flex items-start gap-2 min-w-0 flex-1">
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-orange-500/10 to-orange-600/5 shrink-0">
                <ChefHat className="h-4 w-4 text-orange-600" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-sm truncate">{subRecipe.name}</h3>
                <div className="flex items-center gap-3 text-xs text-muted-foreground mt-1">
                  <div className="flex items-center gap-1">
                    <Package className="h-3 w-3" />
                    <span>{subRecipe.yield.quantity} {subRecipe.yield.unit}</span>
                  </div>
                  {subRecipe.lastProduced && (
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{formatDistanceToNow(new Date(subRecipe.lastProduced), { addSuffix: true })}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-7 w-7 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleViewDetails(subRecipe)}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleEditSubRecipe(subRecipe)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => handleDeleteSubRecipe(subRecipe)}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          
          {/* Bottom stats */}
          <div className="flex items-center justify-between pt-2 border-t border-muted/30">
            <div className="text-xs">
              <span className="text-muted-foreground">Cost: </span>
              <span className="font-medium">{formatCurrency(subRecipe.costPerUnit || 0)}</span>
            </div>
            <div className="text-xs">
              <span className="text-muted-foreground">Total: </span>
              <span className="font-medium">{formatCurrency(totalCost)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={isMobile ? "space-y-3" : "space-y-4"}>
      {/* Header */}
      <div className={isMobile ? "space-y-2" : "flex justify-between items-center"}>
        <div>
          <h2 className={isMobile ? "text-lg font-semibold" : "text-xl font-semibold"}>Sous-recettes</h2>
          {!isMobile && (
            <p className="text-sm text-muted-foreground">
              Visualisez et gérez votre inventaire de sous-recettes
            </p>
          )}
        </div>
        <div className={isMobile ? "flex gap-2" : "flex justify-end gap-2"}>
          <Button size="sm" onClick={handleNewSubRecipe} className={isMobile ? "flex-1" : ""}>
            <Plus className={`h-4 w-4 ${isMobile ? 'mr-1' : 'mr-2'}`} />
            {isMobile ? "New" : "Nouvelle sous-recette"}
          </Button>
        </div>
      </div>

      {/* Content */}
      {loading ? (
        <Card className="border-dashed">
          <CardContent className={isMobile ? "p-6 text-center" : "p-8 text-center"}>
            <RefreshCw className={`${isMobile ? 'h-10 w-10' : 'h-12 w-12'} mx-auto mb-3 text-muted-foreground/50 animate-spin`} />
            <h3 className={`${isMobile ? 'text-sm' : 'text-base'} font-medium mb-2`}>
              Chargement des sous-recettes...
            </h3>
            <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>
              Veuillez patienter
            </p>
          </CardContent>
        </Card>
      ) : error ? (
        <Card className="border-dashed border-red-200">
          <CardContent className={isMobile ? "p-6 text-center" : "p-8 text-center"}>
            <AlertTriangle className={`${isMobile ? 'h-10 w-10' : 'h-12 w-12'} mx-auto mb-3 text-red-400`} />
            <h3 className={`${isMobile ? 'text-sm' : 'text-base'} font-medium mb-2 text-red-600`}>
              Erreur de chargement
            </h3>
            <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground mb-4`}>
              {error.message || 'Une erreur est survenue lors du chargement des données'}
            </p>
            <Button size="sm" variant="outline" onClick={() => window.location.reload()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Recharger
            </Button>
          </CardContent>
        </Card>
      ) : filteredSubRecipes.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className={isMobile ? "p-6 text-center" : "p-8 text-center"}>
            <ChefHat className={`${isMobile ? 'h-10 w-10' : 'h-12 w-12'} mx-auto mb-3 text-muted-foreground/50`} />
            <h3 className={`${isMobile ? 'text-sm' : 'text-base'} font-medium mb-2`}>
              Aucune sous-recette trouvée
            </h3>
            <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground mb-4`}>
              Créez-en dans la section Production !
            </p>
            <Button size="sm" onClick={handleNewSubRecipe}>
              <Plus className="h-4 w-4 mr-2" />
              Créer la première
            </Button>
          </CardContent>
        </Card>
      ) : isMobile ? (
        <div className="space-y-2">
          {filteredSubRecipes.map((subRecipe) => (
            <MobileSubRecipeCard key={subRecipe._id} subRecipe={subRecipe} />
          ))}
        </div>
      ) : (
        <Card className="border-0 shadow-sm">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="border-b">
                  <TableHead className="font-semibold">Nom</TableHead>
                  <TableHead className="font-semibold">Rendement</TableHead>
                  <TableHead className="font-semibold">Stock actuel</TableHead>
                  <TableHead className="font-semibold">Coût par unité</TableHead>
                  <TableHead className="font-semibold">Dernière production</TableHead>
                  <TableHead className="font-semibold text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSubRecipes.map((subRecipe) => (
                  <TableRow key={subRecipe._id} className="cursor-pointer hover:bg-muted/50 transition-colors">
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        <div className="p-1.5 rounded-lg bg-gradient-to-br from-orange-500/10 to-orange-600/5">
                          <ChefHat className="h-4 w-4 text-orange-600" />
                        </div>
                        <span>{subRecipe.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {subRecipe.yield.quantity} {subRecipe.yield.unit}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {typeof subRecipe.currentStock === 'number' ? subRecipe.currentStock.toFixed(2) : '0.00'} {subRecipe.yield.unit}
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(subRecipe.costPerUnit || 0)} / {subRecipe.yield.unit}
                    </TableCell>
                    <TableCell>
                      {subRecipe.lastProduced ? (
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm" title={format(new Date(subRecipe.lastProduced), 'PPP')}>
                            {formatDistanceToNow(new Date(subRecipe.lastProduced), { addSuffix: true })}
                          </span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-sm">Jamais</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewDetails(subRecipe)}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditSubRecipe(subRecipe)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleDeleteSubRecipe(subRecipe)}
                            className="text-destructive focus:text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Form/Details Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className={isMobile ? "sm:max-w-full max-h-[90vh] overflow-hidden" : "max-w-2xl max-h-[90vh] overflow-hidden"}>
          <DialogHeader className={isMobile ? "pb-2" : ""}>
            <DialogTitle className="flex items-center gap-2">
              <ChefHat className="h-5 w-5" />
              {viewMode === 'details' 
                ? selectedSubRecipe?.name 
                : selectedSubRecipe 
                  ? "Modifier la sous-recette" 
                  : "Créer une sous-recette"
              }
            </DialogTitle>
          </DialogHeader>
          
          <div className="overflow-y-auto flex-1">
            {viewMode === 'form' ? (
              <SubRecipeForm
                onSubmit={handleSubRecipeSubmit}
                initialData={selectedSubRecipe ? { ...selectedSubRecipe, type: 'sub-recipe' } : null}
                stockItems={stockItems}
                onCancel={() => setIsFormOpen(false)}
                isMobile={isMobile}
              />
            ) : selectedSubRecipe && (
              <div className={isMobile ? "space-y-3" : "space-y-4"}>
                {/* Recipe details view */}
                <div className={isMobile ? "space-y-2" : "space-y-3"}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`${isMobile ? 'text-sm' : 'text-base'} font-medium`}>
                        Yields: {selectedSubRecipe.yield.quantity} {selectedSubRecipe.yield.unit}
                      </p>
                      <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>
                        Current Stock: {typeof selectedSubRecipe.currentStock === 'number' ? selectedSubRecipe.currentStock.toFixed(2) : '0.00'} {selectedSubRecipe.yield.unit}
                      </p>
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => {
                        setViewMode('form');
                      }}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  </div>
                  
                  {selectedSubRecipe.lastProduced && (
                    <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>
                      Last produced: {format(new Date(selectedSubRecipe.lastProduced), 'PPP')}
                    </p>
                  )}
                </div>

                {/* Ingredients */}
                <div>
                  <h3 className={`${isMobile ? 'text-sm' : 'text-base'} font-semibold mb-2`}>Ingredients</h3>
                  <ScrollArea className={isMobile ? "h-[200px]" : "h-[300px]"}>
                    <div className="space-y-2">
                      {selectedSubRecipe.ingredients.map((ingredient, index) => {
                        const stockItemName = getStockItemName(ingredient.stockItemId);
                        const stockItemUnit = getStockItemUnit(ingredient.stockItemId);
                        const costPerUnit = getStockItemCost(ingredient.stockItemId);
                        const totalCost = calculateIngredientCost(ingredient.stockItemId, ingredient.quantity);

                        return (
                          <div key={index} className={`flex items-center justify-between ${isMobile ? 'p-2' : 'p-3'} bg-muted/20 rounded-md`}>
                            <div className="flex-1">
                              <p className={`${isMobile ? 'text-sm' : 'text-base'} font-medium`}>{stockItemName}</p>
                              <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>
                                {ingredient.quantity} {stockItemUnit} × {formatCurrency(costPerUnit)}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className={`${isMobile ? 'text-sm' : 'text-base'} font-semibold`}>
                                {formatCurrency(totalCost)}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                      
                      {/* Total cost */}
                      <div className={`flex items-center justify-between ${isMobile ? 'p-2' : 'p-3'} bg-primary/5 rounded-md border`}>
                        <p className={`${isMobile ? 'text-sm' : 'text-base'} font-semibold`}>Total Cost</p>
                        <p className={`${isMobile ? 'text-sm' : 'text-base'} font-bold`}>
                          {formatCurrency(selectedSubRecipe.ingredients.reduce(
                            (sum, ingredient) => sum + calculateIngredientCost(ingredient.stockItemId, ingredient.quantity),
                            0
                          ))}
                        </p>
                      </div>
                      
                      {/* Cost per unit */}
                      <div className={`flex items-center justify-between ${isMobile ? 'p-2' : 'p-3'} bg-secondary/20 rounded-md`}>
                        <p className={`${isMobile ? 'text-sm' : 'text-base'} font-semibold`}>Cost Per Unit</p>
                        <p className={`${isMobile ? 'text-sm' : 'text-base'} font-bold`}>
                          {selectedSubRecipe.costPerUnit
                            ? formatCurrency(selectedSubRecipe.costPerUnit)
                            : 'Not calculated'}
                        </p>
                      </div>
                    </div>
                  </ScrollArea>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
