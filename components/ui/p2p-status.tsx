"use client";

import { useState, useEffect } from "react";
import { useP2PSync } from "@/hooks/use-p2p-sync";
import { cn } from "@/lib/utils";
import { Network } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface P2PStatusProps {
  className?: string;
}

export function P2PStatus({ className }: P2PStatusProps) {
  const { isElectron, mdnsStatus, peers } = useP2PSync();
  const [statusText, setStatusText] = useState("Checking P2P status...");
  const [tooltipText, setTooltipText] = useState("P2P Sync Status");

  useEffect(() => {
    if (!isElectron) {
      setStatusText("N/A");
      setTooltipText("P2P sync only available in desktop app");
      return;
    }

    if (mdnsStatus === "running") {
      setStatusText(`${peers.length} peers`);
      setTooltipText(`Connected to ${peers.length} peers on LAN`);
    } else if (mdnsStatus === "error") {
      setStatusText("Error");
      setTooltipText("P2P service encountered an error");
    } else {
      setStatusText("Inactive");
      setTooltipText("P2P service is not running");
    }
  }, [isElectron, mdnsStatus, peers.length]);

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div
          className={cn(
            "flex items-center gap-1.5 text-sm px-2 py-1 rounded-md",
            !isElectron ? "text-muted-foreground" :
            mdnsStatus === "running" ? "text-green-500" :
            mdnsStatus === "error" ? "text-red-500" : "text-yellow-500",
            className
          )}
        >
          <Network className="h-4 w-4" />
          <span>{statusText}</span>
        </div>
      </TooltipTrigger>
      <TooltipContent side="bottom">
        <p>{tooltipText}</p>
      </TooltipContent>
    </Tooltip>
  );
} 