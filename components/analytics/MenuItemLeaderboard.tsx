"use client"

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from '@/lib/utils/currency';
import { TrendingUpIcon, TrendingDownIcon, UtensilsIcon } from 'lucide-react';

interface MenuItem {
  id: string;
  name: string;
  quantity: number;
  revenue: number;
  cost?: number;
}

interface MenuItemLeaderboardProps {
  orders: any[]; // Replace with proper type
  cogsEnabled?: boolean;
  className?: string;
}

export default function MenuItemLeaderboard({
  orders,
  cogsEnabled = false,
  className
}: MenuItemLeaderboardProps) {
  const [view, setView] = React.useState<'top' | 'bottom'>('top');

  // Process orders to get menu item statistics
  const menuItems = useMemo(() => {
    const itemMap = new Map<string, MenuItem>();
    
    orders.forEach(order => {
      if (order.status !== 'completed') return;
      
      order.items.forEach((item: any) => {
        const itemId = item.id || item._id;
        const existingItem = itemMap.get(itemId);
        
        if (existingItem) {
          existingItem.quantity += item.quantity;
          existingItem.revenue += (item.price * item.quantity);
        } else {
          itemMap.set(itemId, {
            id: itemId,
            name: item.name,
            quantity: item.quantity,
            revenue: item.price * item.quantity,
            cost: item.cost || undefined
          });
        }
      });
    });
    
    return Array.from(itemMap.values());
  }, [orders]);

  // Sort items by quantity or revenue
  const topItems = useMemo(() => {
    return [...menuItems]
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);
  }, [menuItems]);

  const bottomItems = useMemo(() => {
    return [...menuItems]
      .sort((a, b) => a.revenue - b.revenue)
      .slice(0, 5);
  }, [menuItems]);

  // Calculate profit margin if COGS is enabled
  const calculateProfitMargin = (item: MenuItem) => {
    if (!cogsEnabled || !item.cost) return null;
    return ((item.revenue - (item.cost * item.quantity)) / item.revenue) * 100;
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
          <div>
            <CardTitle className="text-lg font-medium flex items-center gap-2">
              <UtensilsIcon className="h-5 w-5 text-primary" />
              Menu Item Leaderboard
            </CardTitle>
            <CardDescription>
              Performance analysis of your menu items
            </CardDescription>
          </div>
          <Tabs value={view} onValueChange={(v) => setView(v as 'top' | 'bottom')}>
            <TabsList className="h-8">
              <TabsTrigger value="top" className="text-xs px-3">
                <TrendingUpIcon className="h-3.5 w-3.5 mr-1" />
                Top Performers
              </TabsTrigger>
              <TabsTrigger value="bottom" className="text-xs px-3">
                <TrendingDownIcon className="h-3.5 w-3.5 mr-1" />
                Underperformers
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Item</TableHead>
                <TableHead className="text-right">Quantity</TableHead>
                <TableHead className="text-right">Revenue</TableHead>
                {cogsEnabled && <TableHead className="text-right">Profit Margin</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {(view === 'top' ? topItems : bottomItems).length === 0 ? (
                <TableRow>
                  <TableCell colSpan={cogsEnabled ? 4 : 3} className="text-center text-muted-foreground">
                    No data available
                  </TableCell>
                </TableRow>
              ) : (
                (view === 'top' ? topItems : bottomItems).map((item, index) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Badge variant={view === 'top' ? "default" : "destructive"} className="w-5 h-5 p-0 flex items-center justify-center rounded-full">
                          {index + 1}
                        </Badge>
                        <span className="font-medium">{item.name}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">{item.quantity}</TableCell>
                    <TableCell className="text-right">{formatCurrency(item.revenue)}</TableCell>
                    {cogsEnabled && (
                      <TableCell className="text-right">
                        {calculateProfitMargin(item) !== null ? (
                          <span className={calculateProfitMargin(item)! > 30 ? "text-green-600" : "text-amber-600"}>
                            {calculateProfitMargin(item)!.toFixed(1)}%
                          </span>
                        ) : (
                          <span className="text-muted-foreground text-xs">N/A</span>
                        )}
                      </TableCell>
                    )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
