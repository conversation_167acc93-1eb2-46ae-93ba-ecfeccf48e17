"use client"

import React from 'react';
import { DateRange } from 'react-day-picker';
import { formatCurrency } from '@/lib/utils/currency';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  DollarSignIcon,
  ReceiptIcon,
  TagIcon,
  PercentIcon,
  UsersIcon,
  TrendingUpIcon,
  BarChart2Icon,
  CircleDollarSignIcon,
  ScanIcon,
  ClockIcon,
  ChevronRightIcon
} from 'lucide-react';
import { Alert } from "@/components/analytics/AlertBanner";
import AlertBanner from "@/components/analytics/AlertBanner";
import KpiMetricCard from "@/components/analytics/KpiMetricCard";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Line, Line<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Axis } from "recharts";
import { cn } from "@/lib/utils";

// Simulated data - would be replaced with actual data fetching
const generateMockData = () => {
  return {
    netSales: 155700,
    orderCount: 124,
    averageCheck: 1250,
    cogs: 46710,
    laborCost: 31140,
    operatingExpenses: 15570,
    grossProfit: 108990,
    grossMargin: 70,
    netProfit: 62280,
    netProfitMargin: 40,
    cogsPercentage: 30,
    laborPercentage: 20,
    topItems: [
      { name: "Burger Deluxe", value: 24600 },
      { name: "Pizza Margherita", value: 18300 },
      { name: "Coca Cola", value: 9800 },
      { name: "Salade César", value: 7400 },
      { name: "Frites", value: 6500 }
    ],
    salesTrend: [
      { day: "L", sales: 18500 },
      { day: "M", sales: 15200 },
      { day: "M", sales: 21400 },
      { day: "J", sales: 19800 },
      { day: "V", sales: 24600 },
      { day: "S", sales: 35700 },
      { day: "D", sales: 20500 }
    ],
    expensesTrend: [
      { day: "L", expenses: 8200 },
      { day: "M", expenses: 7100 },
      { day: "M", expenses: 9300 },
      { day: "J", expenses: 8900 },
      { day: "V", expenses: 10200 },
      { day: "S", expenses: 12800 },
      { day: "D", expenses: 9600 }
    ]
  };
};

// Mock alerts
const getMockAlerts = (): Alert[] => [
  {
    type: 'warning',
    message: 'Marge bénéficiaire < 40% cette semaine',
    action: 'Voir Détails',
    link: '#profit'
  },
  {
    type: 'info',
    message: 'Les ventes ont augmenté de 15% par rapport à la semaine dernière',
    action: 'Analyser',
    link: '#sales'
  }
];

// Chart configuration
const chartConfig = {
  sales: {
    label: "Ventes",
    color: "var(--chart-1)",
  },
  expenses: {
    label: "Dépenses",
    color: "var(--chart-2)",
  },
  value: {
    label: "Ventes",
    color: "var(--chart-1)",
  }
};

interface DashboardTabProps {
  dateRange?: DateRange;
}

export default function DashboardTab({ dateRange }: DashboardTabProps) {
  const data = generateMockData();
  const alerts = getMockAlerts();

  // Format percentage for gauge
  const formatGaugePercentage = (value: number, type: 'cogs' | 'labor') => {
    const getColor = () => {
      if (type === 'cogs') {
        return value > 35 ? 'text-red-500' : value > 30 ? 'text-amber-500' : 'text-green-500';
      } else {
        return value > 25 ? 'text-red-500' : value > 20 ? 'text-amber-500' : 'text-green-500';
      }
    };

    return (
      <div className="flex items-center gap-1">
        <span className={cn("text-2xl font-bold", getColor())}>{value.toFixed(1)}%</span>
      </div>
    );
  };

  // Gauge component
  const Gauge = ({ percentage, type }: { percentage: number, type: 'cogs' | 'labor' }) => {
    const getSegmentColor = (segment: number) => {
      if (type === 'cogs') {
        // COGS thresholds
        if (segment <= 28) return 'bg-green-500';
        if (segment <= 32) return 'bg-amber-500';
        return 'bg-red-500';
      } else {
        // Labor thresholds
        if (segment <= 20) return 'bg-green-500';
        if (segment <= 25) return 'bg-amber-500';
        return 'bg-red-500';
      }
    };

    const maxSegments = 40;
    const activeSegments = Math.round((percentage / 100) * maxSegments);

    return (
      <div className="flex gap-0.5 mt-2">
        {Array.from({ length: maxSegments }).map((_, i) => (
          <div 
            key={i}
            className={cn(
              "h-1.5 flex-1 rounded-full",
              i < activeSegments 
                ? getSegmentColor(percentage) 
                : "bg-muted"
            )}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-3">
      {/* Alerts Banner */}
      <AlertBanner alerts={alerts} />
      
      <div className="grid grid-cols-12 gap-3">
        {/* Main Column - KPIs and Charts */}
        <div className="col-span-12 lg:col-span-8 space-y-3">
          {/* Primary KPIs */}
          <div className="grid grid-cols-3 gap-3">
            <KpiMetricCard
              title="Chiffre d'Affaires"
              value={data.netSales}
              icon={<DollarSignIcon className="h-4 w-4 text-green-600" />}
              trend="up"
              tooltipText="Ventes totales après remises/annulations"
              className="p-2"
            />
            
            <KpiMetricCard
              title="Bénéfice Net"
              value={data.netProfit}
              icon={<CircleDollarSignIcon className="h-4 w-4 text-emerald-600" />}
              trend="up"
              tooltipText="Bénéfice après toutes les dépenses"
              className="p-2"
            />
            
            <KpiMetricCard
              title="Bénéfice Brut"
              value={data.grossProfit}
              icon={<DollarSignIcon className="h-4 w-4 text-blue-600" />}
              trend="up"
              tooltipText="Ventes - Coût des Marchandises Vendues"
              className="p-2"
            />
          </div>

          {/* Secondary KPIs */}
          <div className="grid grid-cols-3 gap-3">
            <KpiMetricCard
              title="Commandes"
              value={data.orderCount}
              icon={<ReceiptIcon className="h-4 w-4 text-indigo-600" />}
              trend="up"
              tooltipText="Nombre total de commandes complétées"
              className="p-2"
            />
            
            <KpiMetricCard
              title="Ticket Moyen"
              value={data.averageCheck}
              icon={<TagIcon className="h-4 w-4 text-purple-600" />}
              tooltipText="Montant moyen dépensé par commande"
              className="p-2"
            />
            
            <KpiMetricCard
              title="Marge Nette"
              value={data.netProfitMargin}
              icon={<PercentIcon className="h-4 w-4 text-emerald-600" />}
              isPercentage={true}
              tooltipText="(Bénéfice Net ÷ Ventes Nettes) × 100"
              className="p-2"
            />
          </div>
          
          {/* Trend Charts */}
          <div className="grid grid-cols-2 gap-3">
            <Card className="shadow-sm">
              <CardHeader className="p-3 pb-0">
                <CardTitle className="text-sm font-medium flex items-center gap-1.5">
                  <BarChart2Icon className="h-3.5 w-3.5 text-muted-foreground" />
                  <span>Tendance Ventes (7j)</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-3 pt-2">
                <div className="h-[150px]">
                  <ChartContainer config={chartConfig} className="w-full h-full">
                    <LineChart data={data.salesTrend}>
                      <CartesianGrid vertical={false} strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="day" 
                        tick={{ fontSize: 11 }}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis 
                        width={40}
                        tickFormatter={(value) => formatCurrency(value)}
                        tick={{ fontSize: 10 }}
                        tickLine={false}
                        axisLine={false}
                      />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Line 
                        type="monotone" 
                        dataKey="sales" 
                        name="Ventes"
                        stroke="var(--chart-1)"
                        strokeWidth={2}
                        dot={{ r: 2 }}
                        activeDot={{ r: 4 }}
                      />
                    </LineChart>
                  </ChartContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-sm">
              <CardHeader className="p-3 pb-0">
                <CardTitle className="text-sm font-medium flex items-center gap-1.5">
                  <BarChart2Icon className="h-3.5 w-3.5 text-muted-foreground" />
                  <span>Tendance Dépenses (7j)</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-3 pt-2">
                <div className="h-[150px]">
                  <ChartContainer config={chartConfig} className="w-full h-full">
                    <LineChart data={data.expensesTrend}>
                      <CartesianGrid vertical={false} strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="day" 
                        tick={{ fontSize: 11 }}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis 
                        width={40}
                        tickFormatter={(value) => formatCurrency(value)}
                        tick={{ fontSize: 10 }}
                        tickLine={false}
                        axisLine={false}
                      />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Line 
                        type="monotone" 
                        dataKey="expenses" 
                        name="Dépenses"
                        stroke="var(--chart-2)"
                        strokeWidth={2}
                        dot={{ r: 2 }}
                        activeDot={{ r: 4 }}
                      />
                    </LineChart>
                  </ChartContainer>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Top Items */}
          <Card className="shadow-sm">
            <CardHeader className="p-3 pb-0">
              <CardTitle className="text-sm font-medium flex items-center gap-1.5">
                <BarChart2Icon className="h-3.5 w-3.5 text-muted-foreground" />
                <span>Meilleurs Produits</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3 pt-2">
              <div className="h-[200px]">
                <ChartContainer config={chartConfig} className="w-full h-full">
                  <BarChart 
                    data={data.topItems.map(item => ({ 
                      name: item.name.length > 12 ? `${item.name.substring(0, 11)}...` : item.name,
                      value: item.value
                    }))} 
                    layout="vertical" 
                    barCategoryGap={8}
                  >
                    <CartesianGrid horizontal={true} vertical={false} />
                    <XAxis 
                      type="number" 
                      tickFormatter={(value) => formatCurrency(value)}
                      tick={{ fontSize: 10 }}
                      tickLine={false}
                      axisLine={false}
                    />
                    <YAxis 
                      dataKey="name" 
                      type="category"
                      width={100}
                      tick={{ fontSize: 11 }}
                      tickLine={false}
                      axisLine={false}
                    />
                    <ChartTooltip 
                      content={<ChartTooltipContent 
                        formatter={(value) => formatCurrency(Number(value))} 
                      />}
                    />
                    <Bar 
                      dataKey="value" 
                      name="Ventes"
                      fill="var(--chart-1)" 
                      radius={4}
                    />
                  </BarChart>
                </ChartContainer>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Sidebar - Metrics and Insights */}
        <div className="col-span-12 lg:col-span-4 space-y-3">
          {/* Key Ratios / Gauges */}
          <div className="grid grid-cols-2 lg:grid-cols-1 gap-3">
            <Card className="shadow-sm">
              <CardContent className="p-3">
                <div className="flex items-center justify-between mb-1.5">
                  <div className="flex items-center gap-1.5">
                    <ScanIcon className="h-4 w-4 text-orange-600" />
                    <h3 className="text-xs font-medium">Coût Marchandises (%)</h3>
                  </div>
                  <div className="text-xl font-bold text-orange-600">{data.cogsPercentage}%</div>
                </div>
                <Gauge percentage={data.cogsPercentage} type="cogs" />
                <p className="mt-1.5 text-[10px] text-muted-foreground">
                  {data.cogsPercentage <= 28 
                    ? "Excellent: en-dessous de la cible de 30%" 
                    : data.cogsPercentage <= 32 
                    ? "Normal: proche de la cible de 30%" 
                    : "Attention: au-dessus de la cible de 30%"}
                </p>
              </CardContent>
            </Card>
            
            <Card className="shadow-sm">
              <CardContent className="p-3">
                <div className="flex items-center justify-between mb-1.5">
                  <div className="flex items-center gap-1.5">
                    <UsersIcon className="h-4 w-4 text-indigo-600" />
                    <h3 className="text-xs font-medium">Coût Personnel (%)</h3>
                  </div>
                  <div className="text-xl font-bold text-indigo-600">{data.laborPercentage}%</div>
                </div>
                <Gauge percentage={data.laborPercentage} type="labor" />
                <p className="mt-1.5 text-[10px] text-muted-foreground">
                  {data.laborPercentage <= 20 
                    ? "Excellent: en-dessous de la cible de 25%" 
                    : data.laborPercentage <= 25 
                    ? "Normal: proche de la cible de 25%" 
                    : "Attention: au-dessus de la cible de 25%"}
                </p>
              </CardContent>
            </Card>
          </div>
          
          {/* Actionable Insights */}
          <Card className="shadow-sm">
            <CardHeader className="p-3 pb-1">
              <CardTitle className="text-sm font-medium">Insights & Actions</CardTitle>
            </CardHeader>
            <CardContent className="p-3 pt-1">
              <div className="space-y-3">
                <div className="flex items-start gap-2.5">
                  <div className="bg-amber-100 dark:bg-amber-700/20 p-1.5 rounded-full mt-0.5">
                    <ClockIcon className="h-3.5 w-3.5 text-amber-600 dark:text-amber-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs font-medium">Heure de pointe: 13h-15h</p>
                    <p className="text-[10px] text-muted-foreground mt-0.5">28% des ventes quotidiennes</p>
                    <div className="mt-1 flex items-center">
                      <Button variant="link" size="sm" className="h-auto p-0 text-[10px] flex items-center gap-0.5">
                        <span>Voir détails</span>
                        <ChevronRightIcon className="h-2.5 w-2.5" />
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start gap-2.5">
                  <div className="bg-emerald-100 dark:bg-emerald-700/20 p-1.5 rounded-full mt-0.5">
                    <BarChart2Icon className="h-3.5 w-3.5 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs font-medium">Coût de gaspillage: 5 200 DA</p>
                    <p className="text-[10px] text-muted-foreground mt-0.5">3.2% du coût total des marchandises</p>
                    <div className="mt-1 flex items-center">
                      <Button variant="link" size="sm" className="h-auto p-0 text-[10px] flex items-center gap-0.5">
                        <span>Voir gaspillage</span>
                        <ChevronRightIcon className="h-2.5 w-2.5" />
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start gap-2.5">
                  <div className="bg-blue-100 dark:bg-blue-700/20 p-1.5 rounded-full mt-0.5">
                    <TrendingUpIcon className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs font-medium">Pizza Margherita: 40% de marge</p>
                    <p className="text-[10px] text-muted-foreground mt-0.5">Produit le plus rentable du top 5</p>
                    <div className="mt-1 flex items-center">
                      <Button variant="link" size="sm" className="h-auto p-0 text-[10px] flex items-center gap-0.5">
                        <span>Voir rentabilité</span>
                        <ChevronRightIcon className="h-2.5 w-2.5" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 