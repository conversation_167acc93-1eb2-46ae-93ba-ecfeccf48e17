"use client";

import React, { useState, useEffect } from 'react';
import { syncServiceV4 } from '@/lib/db/v4/core/sync-service';
import { databaseV4 } from '@/lib/db/v4/core/db-instance';

interface SyncMonitorProps {
  className?: string;
}

export const SyncMonitor: React.FC<SyncMonitorProps> = ({ className = '' }) => {
  const [syncState, setSyncState] = useState(syncServiceV4.getSyncState());
  const [localSyncStatus, setLocalSyncStatus] = useState<any>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const refreshStatus = async () => {
    setIsRefreshing(true);
    try {
      await syncServiceV4.refreshSync();
      setSyncState(syncServiceV4.getSyncState());
      
      if (syncState.isElectron) {
        const status = await syncServiceV4.getLocalSyncStatus();
        setLocalSyncStatus(status);
      }
    } catch (error) {
      console.error('Error refreshing sync status:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const forceSyncOnce = async () => {
    setIsRefreshing(true);
    try {
      const result = await syncServiceV4.forceSyncOnce();
      console.log('Force sync result:', result);
      await refreshStatus();
    } catch (error) {
      console.error('Error forcing sync:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    refreshStatus();
    
    // Refresh every 10 seconds
    const interval = setInterval(refreshStatus, 10000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
      case 'synced':
        return 'text-green-600';
      case 'connecting':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
      case 'synced':
        return '✅';
      case 'connecting':
        return '🔄';
      case 'error':
        return '❌';
      default:
        return '⚫';
    }
  };

  return (
    <div className={`p-4 bg-gray-50 border rounded-lg ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">🔄 Sync Monitor</h3>
        <div className="space-x-2">
          <button
            onClick={refreshStatus}
            disabled={isRefreshing}
            className="px-3 py-1 text-sm bg-blue-500 text-white rounded disabled:opacity-50"
          >
            {isRefreshing ? '⏳' : '🔄'} Refresh
          </button>
          {syncState.isElectron && (
            <button
              onClick={forceSyncOnce}
              disabled={isRefreshing}
              className="px-3 py-1 text-sm bg-green-500 text-white rounded disabled:opacity-50"
            >
              🚀 Force Sync
            </button>
          )}
        </div>
      </div>

      <div className="space-y-3">
        {/* Basic Status */}
        <div className="flex items-center space-x-2">
          <span className="text-lg">{getStatusIcon(syncState.status)}</span>
          <span className={`font-medium ${getStatusColor(syncState.status)}`}>
            {syncState.status.toUpperCase()}
          </span>
          <span className="text-sm text-gray-500">
            {syncState.isElectron ? '(Electron)' : '(Web)'}
          </span>
        </div>

        {/* Last Sync */}
        <div className="text-sm">
          <span className="text-gray-600">Last Sync: </span>
          <span className="font-mono">
            {syncState.lastSync ? syncState.lastSync.toLocaleString() : 'Never'}
          </span>
        </div>

        {/* Error */}
        {syncState.error && (
          <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
            <strong>Error:</strong> {syncState.error.message}
          </div>
        )}

        {/* Database Status */}
        <div className="text-sm">
          <span className="text-gray-600">Database: </span>
          <span className={databaseV4.isInitialized ? 'text-green-600' : 'text-red-600'}>
            {databaseV4.isInitialized ? '✅ Initialized' : '❌ Not Initialized'}
          </span>
          {databaseV4.getCurrentRestaurantId() && (
            <span className="text-gray-500 ml-2">
              ({databaseV4.getCurrentRestaurantId()})
            </span>
          )}
        </div>

        {/* Electron Sync Details */}
        {syncState.isElectron && localSyncStatus && (
          <div className="mt-4 p-3 bg-white rounded border">
            <h4 className="font-medium mb-2">📡 Local Sync Status</h4>
            <div className="text-sm space-y-1">
              <div>
                <span className="text-gray-600">Active: </span>
                <span className={localSyncStatus.active ? 'text-green-600' : 'text-red-600'}>
                  {localSyncStatus.active ? '✅ Yes' : '❌ No'}
                </span>
              </div>
              {localSyncStatus.details && localSyncStatus.details.length > 0 && (
                <div className="mt-2">
                  <span className="text-gray-600">Databases:</span>
                  <ul className="mt-1 space-y-1">
                    {localSyncStatus.details.map((db: any, index: number) => (
                      <li key={index} className="text-xs font-mono bg-gray-100 p-2 rounded">
                        <div><strong>{db.dbName}</strong></div>
                        <div>Remote: {db.isRemoteAvailable ? '✅' : '❌'}</div>
                        <div>Sync: {db.syncActive ? '✅' : '❌'}</div>
                        {db.lastActivity && (
                          <div>Last Activity: {new Date(db.lastActivity).toLocaleTimeString()}</div>
                        )}
                        {db.retryCount > 0 && (
                          <div className="text-orange-600">Retries: {db.retryCount}</div>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SyncMonitor; 