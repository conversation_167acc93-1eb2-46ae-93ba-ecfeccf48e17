// knowledge:edit-order-context:start
import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from 'react';

interface EditOrderState {
  editOrder: any;
  isEditMode: boolean;
  editSessionId: string | null;
  lastEditTimestamp: number | null;
}

interface EditOrderContextType {
  editOrder: any;
  isEditMode: boolean;
  editSessionId: string | null;
  setEditOrder: (order: any) => void;
  clearEditOrder: () => void;
  refreshEditSession: () => void;
}

const EditOrderContext = createContext<EditOrderContextType>({
  editOrder: null,
  isEditMode: false,
  editSessionId: null,
  setEditOrder: (order: any) => {},
  clearEditOrder: () => {},
  refreshEditSession: () => {},
});

export const EditOrderProvider = ({ children }: { children: React.ReactNode }) => {
  const [editState, setEditState] = useState<EditOrderState>({
    editOrder: null,
    isEditMode: false,
    editSessionId: null,
    lastEditTimestamp: null
  });
  
  // 🔄 Track tab switches and state persistence
  const tabSwitchCountRef = useRef(0);
  const lastTabSwitchRef = useRef<number>(0);
  
  // 🎯 Enhanced setEditOrder with session management
  const setEditOrder = useCallback((order: any) => {
    if (order) {
      const sessionId = `edit_${order.id || order._id}_${Date.now()}`;
      const timestamp = Date.now();
      
      // 📝 Store edit session in localStorage for persistence across tab switches
      const editSession = {
        order,
        sessionId,
        timestamp,
        tabSwitchCount: tabSwitchCountRef.current
      };
      
      try {
        localStorage.setItem('current_edit_session', JSON.stringify(editSession));
        console.log('💾 [EditOrderContext] Stored edit session:', sessionId);
      } catch (error) {
        console.warn('⚠️ [EditOrderContext] Failed to store edit session:', error);
      }
      
      setEditState({
        editOrder: order,
        isEditMode: true,
        editSessionId: sessionId,
        lastEditTimestamp: timestamp
      });
      
      console.log('✏️ [EditOrderContext] Edit mode activated for order:', order.id || order._id);
    } else {
      clearEditOrder();
    }
  }, []);
  
  // 🧹 Enhanced clearEditOrder with cleanup
  const clearEditOrder = useCallback(() => {
    console.log('🧹 [EditOrderContext] Clearing edit session');
    
    // Clear localStorage
    try {
      localStorage.removeItem('current_edit_session');
    } catch (error) {
      console.warn('⚠️ [EditOrderContext] Failed to clear edit session:', error);
    }
    
    setEditState({
      editOrder: null,
      isEditMode: false,
      editSessionId: null,
      lastEditTimestamp: null
    });
  }, []);
  
  // 🔄 Refresh edit session (useful after tab switches)
  const refreshEditSession = useCallback(() => {
    try {
      const storedSession = localStorage.getItem('current_edit_session');
      if (storedSession) {
        const session = JSON.parse(storedSession);
        const now = Date.now();
        
        // Check if session is still valid (within 30 minutes)
        if (now - session.timestamp < 30 * 60 * 1000) {
          console.log('🔄 [EditOrderContext] Restoring edit session:', session.sessionId);
          
          setEditState({
            editOrder: session.order,
            isEditMode: true,
            editSessionId: session.sessionId,
            lastEditTimestamp: session.timestamp
          });
          
          return true;
        } else {
          console.log('⏰ [EditOrderContext] Edit session expired, clearing');
          localStorage.removeItem('current_edit_session');
        }
      }
    } catch (error) {
      console.warn('⚠️ [EditOrderContext] Failed to restore edit session:', error);
      localStorage.removeItem('current_edit_session');
    }
    
    return false;
  }, []);
  
  // 🎯 Auto-restore edit session on mount
  useEffect(() => {
    refreshEditSession();
  }, [refreshEditSession]);
  
  // 🔍 Track tab visibility changes to handle tab switching
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && editState.isEditMode) {
        // Tab became visible and we're in edit mode
        tabSwitchCountRef.current += 1;
        lastTabSwitchRef.current = Date.now();
        
        console.log('👁️ [EditOrderContext] Tab became visible, checking edit session...');
        
        // Verify edit session is still valid
        const restored = refreshEditSession();
        if (!restored && editState.editOrder) {
          console.log('⚠️ [EditOrderContext] Edit session lost, clearing state');
          clearEditOrder();
        }
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [editState.isEditMode, editState.editOrder, refreshEditSession, clearEditOrder]);
  
  const contextValue: EditOrderContextType = {
    editOrder: editState.editOrder,
    isEditMode: editState.isEditMode,
    editSessionId: editState.editSessionId,
    setEditOrder,
    clearEditOrder,
    refreshEditSession
  };
  
  return (
    <EditOrderContext.Provider value={contextValue}>
      {children}
    </EditOrderContext.Provider>
  );
};

export const useEditOrder = () => useContext(EditOrderContext);
// knowledge:edit-order-context:end