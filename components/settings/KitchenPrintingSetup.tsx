"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  <PERSON>er, 
  <PERSON>an, 
  Users,
  Edit,
  Info,
  Plus,
  Trash2,
  Wifi,
  TestTube2,
  Search,
  Eye
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { kitchenPrintService, PrintJob } from '@/lib/services/kitchen-print-service';
import { PrintPreviewDialog } from '@/app/components/print/PrintPreviewDialog';
import { Order, OrderItem } from '@/lib/db/v4/schemas/order-schema';
import { v4 as uuidv4 } from 'uuid';
import { getMenu } from '@/lib/db/v4/operations/menu-ops';

// 🆕 Define the structure of an OS-level printer retrieved from Electron
interface OSPrinterInfo {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'unknown';
  rawStatus: number; // The raw numerical status code from the OS
  type: 'thermal' | 'inkjet' | 'laser' | string;
  description?: string;
  isDefault?: boolean;
  ipAddress?: string; // Add ipAddress as it's part of the returned data
}

// Types
interface PrintingSystem {
  id: 'single' | 'multi-station' | 'multi-barcode';
  name: string;
  emoji: string;
  description: string;
  recommendation: {
    score: number;
    emoji: string;
    reason: string;
  };
  features: string[];
  requirements: string[];
}

interface PrinterDevice {
  id: string;
  name: string;
  ipAddress?: string;
  status: 'online' | 'offline' | 'unknown';
  assignedCategories: string[];
  type: 'thermal' | 'inkjet' | 'laser';
  isReceiptPrinter?: boolean;
}

interface BarcodeScannerDevice {
  id: string;
  name: string;
  status: 'connected' | 'disconnected';
  batteryLevel?: number;
}

interface KitchenPrintingSetupProps {
  categories: Array<{
    id: string;
    name: string;
    emoji?: string;
  }>;
}

const PRINTING_SYSTEMS: PrintingSystem[] = [
  {
    id: 'single',
    name: 'Single Central Printer',
    emoji: '🖨️',
    description: 'One printer for all kitchen orders. Simple setup, manual coordination.',
    recommendation: {
      score: 2,
      emoji: '⭐⭐',
      reason: 'Good for small kitchens with 2-3 staff'
    },
    features: [
      'Single ticket per order',
      'Manual delegation by head cook',
      'Lowest cost setup',
      'Simple maintenance'
    ],
    requirements: [
      '1 thermal printer',
      'Basic POS integration',
      'Experienced head cook for coordination'
    ]
  },
  {
    id: 'multi-station',
    name: 'Multi-Station Smart Printers',
    emoji: '🖨️🖨️🖨️',
    description: 'Dedicated printers per station with smart coordination tickets.',
    recommendation: {
      score: 5,
      emoji: '⭐⭐⭐⭐⭐',
      reason: 'Perfect balance of coordination and simplicity'
    },
    features: [
      'Station-specific tickets',
      'Queue status awareness',
      'Coordination information',
      'Self-paced cooking'
    ],
    requirements: [
      '2-4 thermal printers',
      'Network setup',
      'Station assignment training'
    ]
  },
  {
    id: 'multi-barcode',
    name: 'Multi-Station + Barcode Scanning',
    emoji: '🖨️📱🔍',
    description: 'Advanced system with completion tracking and expo notifications.',
    recommendation: {
      score: 4,
      emoji: '⭐⭐⭐⭐',
      reason: 'Best for high-volume operations'
    },
    features: [
      'Real-time completion tracking',
      'Automatic expo notifications',
      'Performance analytics',
      'Perfect timing coordination'
    ],
    requirements: [
      '2-4 thermal printers',
      '2-3 barcode scanners',
      'Staff training on scanning',
      'Higher maintenance'
    ]
  }
];

export function KitchenPrintingSetup({ categories }: KitchenPrintingSetupProps) {
  const { toast } = useToast();
  const [selectedSystem, setSelectedSystem] = useState<string | null>(null);
  const [printers, setPrinters] = useState<PrinterDevice[]>([]);
  const [scanners, setScanners] = useState<BarcodeScannerDevice[]>([]);
  const [showSystemSelection, setShowSystemSelection] = useState(true);
  const [selectedPrinterIds, setSelectedPrinterIds] = useState<string[]>([]);
  const [selectedScannerIds, setSelectedScannerIds] = useState<string[]>([]);
  const [receiptPrinterId, setReceiptPrinterId] = useState<string | null>(null);
  
  // 🆕 OS-level printer discovery state
  const [isDiscoveringPrinters, setIsDiscoveringPrinters] = useState(false);
  
  // State for Quick Preview
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [sampleOrder, setSampleOrder] = useState<Order | null>(null);
  const [samplePrintJob, setSamplePrintJob] = useState<PrintJob | null>(null);
  
  useEffect(() => {
    const saved = localStorage.getItem('kitchen_printing_system');
    if (saved) {
      setSelectedSystem(saved);
      setShowSystemSelection(false);
    }
  }, []);

  // 🆕 Always fetch fresh printer data from OS
  useEffect(() => {
    const loadPrinters = async () => {
      try {
        console.log('🔄 Loading printers on component mount...');
        // Always discover fresh OS printers - no delete functionality
        await discoverOSPrinters();
      } catch (error) {
        console.warn('Failed to load printers:', error);
      }
    };

    loadPrinters();
  }, [selectedSystem]);

  // 🔄 Auto-refresh printers every 30 seconds to check status
  useEffect(() => {
    const interval = setInterval(async () => {
      await discoverOSPrinters();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, []);

  // 🆕 Detect USB/Bluetooth scanners (keep this functionality)
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    const fetchScanners = async () => {
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        try {
          const usbDevices = await (window as any).electronAPI.invoke('get-usb-devices');
          const scanners = usbDevices.filter((d: any) =>
            d.deviceClass === 3 && d.deviceName && (d.deviceName.toLowerCase().includes('scanner') || d.deviceName.toLowerCase().includes('barcode'))
          ).map((d: any, idx: number) => ({
            id: d.deviceAddress || d.deviceName || `scanner-${idx}`,
            name: d.deviceName || `Scanner ${idx+1}`,
            status: 'connected',
            batteryLevel: undefined,
          }));
          setScanners(scanners);
        } catch (err) {
          setScanners([]);
        }
      } else {
        setScanners([]);
      }
    };
    fetchScanners();
    interval = setInterval(fetchScanners, 2000);
    return () => { if (interval) clearInterval(interval); };
  }, [selectedSystem]);

  const handleSystemSelect = (systemId: string) => {
    setSelectedSystem(systemId);
    setShowSystemSelection(false);
    localStorage.setItem('kitchen_printing_system', systemId);
      toast({
        title: "System Selected! 🎉",
      description: `${PRINTING_SYSTEMS.find(s => s.id === systemId)?.name} is now active`,
    });
  };

  const resetSystemSelection = () => {
    setSelectedSystem(null);
    setShowSystemSelection(true);
    localStorage.removeItem('kitchen_printing_system');
    setPrinters([]);
    setScanners([]);
  };

  const isSystemOne = selectedSystem === 'single';

  const handlePrinterSelect = (printerId: string) => {
    if (isSystemOne) {
      setSelectedPrinterIds([printerId]);
    } else {
      setSelectedPrinterIds(prev => prev.includes(printerId) ? prev.filter(id => id !== printerId) : [...prev, printerId]);
    }
  };

  const handleScannerSelect = (scannerId: string) => {
    setSelectedScannerIds(prev => prev.includes(scannerId) ? prev.filter(id => id !== scannerId) : [...prev, scannerId]);
  };

  const assignCategoryToPrinter = (printerId: string, categoryId: string) => {
    setPrinters(prev => {
      const updatedPrinters = prev.map(printer => {
        if (printer.id === printerId) {
          const newCategories = printer.assignedCategories.includes(categoryId)
            ? printer.assignedCategories.filter(id => id !== categoryId)
            : [...printer.assignedCategories, categoryId];
          return { ...printer, assignedCategories: newCategories };
        }
        return printer;
      });
      
      // 🆕 Save to kitchen print service
      try {
        const { kitchenPrintService } = require('@/lib/services/kitchen-print-service');
        kitchenPrintService.setPrinters(updatedPrinters);
      } catch (error) {
        console.warn('Failed to save printer configuration:', error);
      }
      
      return updatedPrinters;
    });
  };

  const toggleReceiptPrinter = (printerId: string) => {
    setPrinters(prev => {
      const updatedPrinters = prev.map(printer => ({
        ...printer,
        isReceiptPrinter: printer.id === printerId ? !printer.isReceiptPrinter : false // Only one receipt printer allowed
      }));
      
      // 🆕 Save to kitchen print service
      try {
        const { kitchenPrintService } = require('@/lib/services/kitchen-print-service');
        kitchenPrintService.setPrinters(updatedPrinters);
      } catch (error) {
        console.warn('Failed to save printer configuration:', error);
      }
      
      return updatedPrinters;
    });
    
    // Update receipt printer selection
    const printer = printers.find(p => p.id === printerId);
    if (printer?.isReceiptPrinter) {
      setReceiptPrinterId(null);
    } else {
      setReceiptPrinterId(printerId);
    }
  };

  // 🆕 Discover OS-level printers
  const discoverOSPrinters = async () => {
    setIsDiscoveringPrinters(true);
    try {
      let osPrinters: any[] = [];
      
      // Try Electron API first
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        try {
          console.log('🔍 Discovering system printers...');
          
          // Call the real API
          const realPrinters = await (window as any).electronAPI.invoke('get-system-printers') as OSPrinterInfo[];
          if (realPrinters && realPrinters.length > 0) {
            osPrinters = realPrinters;
            console.log('✅ Real system printers discovered:', realPrinters);
          } else {
            console.log('📝 No system printers found on this device');

            // Show helpful message for macOS
            if (navigator.userAgent.includes('Mac')) {
              toast({
                title: "No Printers Found on macOS",
                description: "Make sure printers are added in System Preferences > Printers & Scanners and are online.",
              });
            }
          }

        } catch (error) {
          console.warn('Electron printer discovery failed:', error);
          toast({
            title: "Printer Discovery Error",
            description: `Failed to discover system printers: ${error instanceof Error ? error.message : 'Unknown error'}`,
            variant: "destructive"
          });
          osPrinters = [];
        }
      } else {
        console.log('📱 Running in browser mode - no system printer access');
        osPrinters = [];
      }
      
      // Log discovered printers
      console.log(`🔍 Discovered ${osPrinters.length} OS printers`);
      
      // Always update existing kitchen service printers (even if no OS printers found)
      const { kitchenPrintService } = await import('@/lib/services/kitchen-print-service');
      const existingPrinters = await kitchenPrintService.getPrinters();
      
      if (osPrinters.length > 0) {
        // Update existing printers with current status from OS discovery
        const updatedPrinters = existingPrinters.map(printer => {
          const osMatch = osPrinters.find(os => os.id === printer.id || os.name === printer.name);
          if (osMatch) {
            return {
              ...printer,
              status: osMatch.status,
              ipAddress: osMatch.ipAddress || printer.ipAddress
            };
          }
          return { ...printer, status: 'offline' as const }; // Mark as offline if not found
        });
        
        // Add new OS printers that aren't in the kitchen service yet
        const newOSPrinters = osPrinters.filter(osPrinter =>
          !existingPrinters.some(existing => existing.id === osPrinter.id || existing.name === osPrinter.name)
        ).map(osPrinter => ({
          id: osPrinter.id,
          name: osPrinter.name,
          ipAddress: osPrinter.ipAddress,
          status: osPrinter.status || 'offline' as const, // Show offline printers too
          assignedCategories: [] as string[],
          type: osPrinter.type || 'thermal' as const,
          simulated: false,
          isReceiptPrinter: false
        }));

        const allPrinters = [...updatedPrinters, ...newOSPrinters];

        // Always save and display all discovered printers (including offline ones)
        await kitchenPrintService.setPrinters(allPrinters);
        setPrinters(allPrinters);

        console.log(`🖨️ Updated printer status: ${allPrinters.length} total printers (including offline)`);
        console.log('Printers:', allPrinters.map(p => `${p.name} (${p.status})`));

        // Force UI update
        setTimeout(() => {
          console.log('🔄 Force updating UI state with printers:', allPrinters.length);
          setPrinters([...allPrinters]);
        }, 100);
      } else {
        // No OS printers found - just update UI with existing printers (may be empty in production)
        setPrinters(existingPrinters);
        console.log(`📋 No OS printers found. Showing ${existingPrinters.length} existing printers`);
        console.log('Existing printers:', existingPrinters);
      }

      // Debug: Log final printer state
      console.log('🔍 Final printer state in UI:', printers.length > 0 ? printers : 'No printers in state');
      
    } catch (error) {
      console.warn('OS printer discovery failed:', error);
      // Don't show error toast on every refresh, just log it
    } finally {
      setIsDiscoveringPrinters(false);
    }
  };

  // 🔄 Refresh printer status manually
  const handleRefreshPrinters = async () => {
    toast({
      title: "🔄 Refreshing Printers...",
      description: "Checking printer status and discovering new devices"
    });
    await discoverOSPrinters();
    toast({
      title: "✅ Printers Refreshed!",
      description: "Printer status updated"
    });
  };

  // 🧪 Create development printers for testing
  const createDevelopmentPrinters = async () => {
    try {
      const { kitchenPrintService } = await import('@/lib/services/kitchen-print-service');
      const mockPrinters = await kitchenPrintService.createDevelopmentPrinters();

      if (mockPrinters.length > 0) {
        setPrinters(mockPrinters);
        toast({
          title: "🧪 Development Printers Created!",
          description: `Created ${mockPrinters.length} mock printers based on your menu categories`
        });
      } else {
        toast({
          title: "No Categories Found",
          description: "Add some menu categories first, then create development printers",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error Creating Development Printers",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    }
  };

  // 🆕 Test printer connection
  const handleTestConnection = async (printerId: string) => {
    try {
      const { kitchenPrintService } = await import('@/lib/services/kitchen-print-service');
      const printer = printers.find(p => p.id === printerId);
      if (!printer || !printer.ipAddress) {
        toast({
          title: "❌ Test Failed",
          description: "Printer not found or missing IP address",
          variant: "destructive"
        });
        return;
      }

      toast({
        title: "🧪 Testing Connection...",
        description: `Sending test print to ${printer.name}`,
      });

      // Create a proper test Order object that matches the schema
      const testOrder = {
        _id: `order:test-${Date.now()}`,
        id: `order:test-${Date.now()}`,
        type: 'order_document' as const,
        schemaVersion: 'v4.0' as const,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tableId: 'TEST',
        status: 'pending' as const,
        orderType: 'dine-in' as const,
        items: [
          {
            id: 'test-item-1',
            menuItemId: 'test-menu-item',
            name: 'Connection Test',
            quantity: 1,
            price: 0,
            size: '',
            notes: 'Printer connectivity test',
            category: 'Test',
            addons: []
          }
        ],
        total: 0,
        notes: 'System test - printer connection verification',
        paymentStatus: 'paid' as const,
        paymentMethod: 'cash' as const
      };

      const testPayment = {
        method: 'TEST',
        received: 0,
        change: 0
      };

      // Send test print
      const result = await kitchenPrintService.printReceipt(testOrder, testPayment, {
        printerId: printer.id,
        fontSize: 'small'
      });
      
      if (result.success) {
        toast({
          title: "✅ Connection Test Successful!",
          description: `${printer.name} is working properly. Check printer for test receipt.`,
        });
      } else {
        toast({
          title: "❌ Connection Test Failed",
          description: result.error || `Could not print to ${printer.name}`,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "⚠️ Test Error",
        description: "Failed to test printer connection. Please check network settings.",
        variant: "destructive"
      });
    }
  };

  // 🆕 Generate Sample Order for Preview (using real menu data)
  const generateSampleOrder = async (): Promise<Order> => {
    const date = new Date();
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    // 🎯 FIX: Use new order ID format for preview consistency
    const dateStr = `${year}${month}${day}`;
    const orderId = `order:${dateStr}-001`; // Use new format: order:YYYYMMDD-XXX

    const sampleItems: OrderItem[] = [];
    let hasRealItems = false;

    try {
      const menu = await getMenu();
      const availableCategories = menu.categories.filter(cat => cat.items.length > 0);

      if (availableCategories.length >= 2) {
        // Pick one item from each of the first two categories
        for (let i = 0; i < 2; i++) {
          const category = availableCategories[i];
          const item = category.items[0]; // Take the first item from the category
          if (item) {
            sampleItems.push({
              id: uuidv4(),
              menuItemId: item.id,
              name: item.name,
              quantity: i % 2 === 0 ? 1 : 2, // Vary quantity
              price: item.prices[Object.keys(item.prices)[0]] || 0, // Take first available price
              size: Object.keys(item.prices)[0] || 'Regular',
              notes: i % 2 === 0 ? 'Extra notes for item' : '',
              categoryId: category.id,
              addons: i % 2 !== 0 ? [{ id: uuidv4(), name: 'Sample Addon', price: 5 }] : []
            });
            hasRealItems = true;
          }
        }
      } else if (availableCategories.length === 1) {
        // If only one category, take one item from it
        const category = availableCategories[0];
        const item = category.items[0];
        if (item) {
          sampleItems.push({
            id: uuidv4(),
            menuItemId: item.id,
            name: item.name,
            quantity: 1,
            price: item.prices[Object.keys(item.prices)[0]] || 0,
            size: Object.keys(item.prices)[0] || 'Regular',
            notes: 'Single category item',
            categoryId: category.id,
            addons: []
          });
          hasRealItems = true;
        }
      }
    } catch (error) {
      console.warn('Failed to fetch real menu data for sample order:', error);
      // Fallback to dummy data if menu fetching fails
      hasRealItems = false;
    }

    if (!hasRealItems || sampleItems.length === 0) {
      // Fallback if no categories or items are loaded, or if fetching failed
      sampleItems.push({
        id: uuidv4(),
        menuItemId: 'dummy-item-1',
        name: 'Sample Mock Item 1',
        quantity: 1,
        price: 120,
        size: 'Medium',
        notes: 'No onions',
        categoryId: 'Uncategorized',
        addons: []
      });
      sampleItems.push({
        id: uuidv4(),
        menuItemId: 'dummy-item-2',
        name: 'Sample Mock Item 2',
        quantity: 2,
        price: 80,
        size: 'Small',
        notes: '',
        categoryId: 'Uncategorized',
        addons: [{ id: uuidv4(), name: 'Extra Sauce', price: 5 }]
      });
    }

    const total = sampleItems.reduce((sum, item) => {
      const itemPrice = item.price + (item.addons?.reduce((addonSum, addon) => addonSum + addon.price, 0) || 0);
      return sum + itemPrice * item.quantity;
    }, 0);

    return {
      _id: orderId,
      id: orderId,
      type: "order_document",
      schemaVersion: "v4.0",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tableId: "TEST-TABLE",
      status: "pending",
      orderType: "dine-in",
      items: sampleItems,
      total: total,
      notes: "This is a sample order for printer preview testing using real/mock data.",
      paymentStatus: "paid",
      paymentMethod: "cash"
    };
  };

  const handleQuickPreview = async () => {
    const order = await generateSampleOrder(); // Await the async function
    
    try {
      const result = await kitchenPrintService.printKitchenOrder(order, order.tableId, { fontSize: 'medium' });
      
      if (result.success) {
        // Select the appropriate print job for the dialog
        const jobToPreview = result.printJob || (result.printJobs && result.printJobs[0]);
        if (jobToPreview) {
          setSamplePrintJob(jobToPreview);
          setShowPreviewDialog(true);
        } else {
          toast({
            title: "No print job generated",
            description: "The selected system did not generate a printable job.",
            variant: "destructive"
          });
        }
      } else {
        toast({
          title: "Preview Generation Failed",
          description: result.error || "An unknown error occurred during preview generation.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error during quick preview:', error);
      toast({
        title: "Error",
        description: "Failed to generate quick preview. Check console for details.",
        variant: "destructive"
      });
    }
  };

  if (showSystemSelection) {
    return (
      <TooltipProvider>
        <div className="flex gap-2 md:gap-4 justify-center items-center w-full overflow-x-auto pb-2">
            {PRINTING_SYSTEMS.map((system) => (
              <Tooltip key={system.id}>
                <TooltipTrigger asChild>
                  <Button
                  variant={"outline"}
                  className="flex flex-col items-center justify-center min-w-[110px] h-[110px] px-2 py-2 gap-1 text-xs border-2 hover:border-primary/60 transition-all"
                    onClick={() => handleSystemSelect(system.id)}
                  >
                  <span className="text-2xl mb-1">{system.emoji}</span>
                  <span className="font-semibold text-xs leading-tight">{system.name}</span>
                    <span className="text-lg">{system.recommendation.emoji}</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="max-w-xs text-xs">
                  <div className="font-semibold mb-1">{system.name}</div>
                <div className="mb-1 text-muted-foreground">{system.description}</div>
                <div className="mb-1"><b>Features:</b> {system.features.join(", ")}</div>
                <div><b>Best for:</b> {system.recommendation.reason}</div>
                </TooltipContent>
              </Tooltip>
            ))}
        </div>
      </TooltipProvider>
    );
  }

  const currentSystem = PRINTING_SYSTEMS.find(s => s.id === selectedSystem);
    
    return (
    <div className="space-y-2 max-w-2xl mx-auto">
      <div className="flex items-center justify-between gap-2 mb-1">
          <div className="flex items-center gap-2">
          <span className="text-xl">{currentSystem?.emoji}</span>
          <span className="font-semibold text-base leading-tight">{currentSystem?.name}</span>
          <span className="text-xs text-muted-foreground">{currentSystem?.recommendation.emoji}</span>
        </div>
        <Button variant="ghost" size="icon" onClick={resetSystemSelection} className="h-7 w-7 p-0"><Edit className="h-4 w-4" /></Button>
      </div>
      <div className="flex items-center gap-4 text-xs mb-1">
        {printers.length > 0 && (
          <span className="flex items-center gap-1 text-green-700"><Printer className="h-4 w-4" />{printers.length} printers</span>
        )}
        {scanners.length > 0 && (
          <span className="flex items-center gap-1 text-purple-700"><Scan className="h-4 w-4" />{scanners.length} scanners</span>
        )}
        <Button
          variant="outline"
          size="sm"
          onClick={handleQuickPreview}
          className="ml-auto flex items-center gap-1"
        >
          <Eye className="h-4 w-4" />
          Quick Preview
        </Button>
      </div>
      {/* Helper text above printers */}
      <div className="mb-1 text-xs text-muted-foreground flex items-center justify-between">
        <div className="flex items-center gap-2">
          {isSystemOne ? (
            <>Select your main printer</>
          ) : (
            <>
              Select printers and assign categories (stations)
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-3 w-3 ml-1 text-muted-foreground cursor-pointer" />
                  </TooltipTrigger>
                  <TooltipContent side="top" className="text-xs max-w-xs">
                    Assign categories (stations) to each selected printer. Only selected printers can be assigned.
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </>
          )}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="h-6 px-2 text-xs"
            onClick={handleRefreshPrinters}
            disabled={isDiscoveringPrinters}
          >
            <Search className="h-3 w-3 mr-1" />
            {isDiscoveringPrinters ? 'Discovering...' : 'Refresh'}
          </Button>

          {process.env.NODE_ENV === 'development' && (
            <Button
              variant="outline"
              size="sm"
              className="h-6 px-2 text-xs bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100"
              onClick={createDevelopmentPrinters}
            >
              <TestTube2 className="h-3 w-3 mr-1" />
              Dev Printers
            </Button>
          )}
        </div>
      </div>

        
        {printers.length > 0 ? (
          <div className="border rounded-md divide-y bg-muted/30">
            {printers.map((printer) => {
              const selected = selectedPrinterIds.includes(printer.id);
              return (
                <div
                  key={printer.id}
                  className={cn(
                    "flex items-center gap-2 px-2 py-1 text-xs transition-colors relative",
                    selected ? "bg-primary/10" : ""
                  )}
                >
                  <input
                    type={isSystemOne ? "radio" : "checkbox"}
                    checked={selectedPrinterIds.includes(printer.id)}
                    onChange={() => handlePrinterSelect(printer.id)}
                    className="accent-primary h-4 w-4"
                    name="printer-select"
                  />
                  <Printer className="h-4 w-4 mr-1" />
                  <span className="font-medium truncate max-w-[120px] flex items-center gap-1">
                    {printer.name}
                    {printer.isReceiptPrinter && (
                      <Badge variant="default" className="ml-1 px-1 py-0 text-[10px] bg-green-600">Receipt</Badge>
                    )}
                  </span>
                  {printer.ipAddress && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Wifi className="h-3 w-3" />
                      <span>{printer.ipAddress}</span>
                    </div>
                  )}
                  <div className="ml-auto mr-2 flex items-center gap-1">
                    <div className={cn(
                      "w-2 h-2 rounded-full",
                      printer.status === 'online' ? "bg-green-500" : 
                      printer.status === 'offline' ? "bg-red-500" : "bg-gray-500"
                    )} />
                    <span className={cn(
                      "text-xs font-medium",
                      printer.status === 'online' ? "text-green-700" : 
                      printer.status === 'offline' ? "text-red-700" : "text-gray-700"
                    )}>
                      {printer.status === 'online' ? '🟢 Online' : 
                       printer.status === 'offline' ? '🔴 Offline' : '⚪ Unknown'}
                    </span>
                  </div>
                  
                  {/* Actions moved to corner */}
                  <div className="absolute top-1 right-1 flex items-center gap-1">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className={cn(
                              "h-6 px-2 text-xs border",
                              printer.isReceiptPrinter ? "bg-green-100 border-green-300 text-green-800" : "border-muted-foreground/30 text-muted-foreground",
                              !selected && "opacity-50 pointer-events-none cursor-not-allowed"
                            )}
                            onClick={() => selected && toggleReceiptPrinter(printer.id)}
                            disabled={!selected}
                          >
                            {printer.isReceiptPrinter ? "Receipt Printer ✓" : "Set as Receipt"}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="top" className="text-xs max-w-xs">
                          {printer.isReceiptPrinter ? 'Currently set as receipt printer - click to remove' : 'Set this printer to handle customer receipts'}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className={cn(
                              "h-6 px-2 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50 gap-1",
                              !selected && "opacity-50 pointer-events-none cursor-not-allowed"
                            )}
                            onClick={() => selected && handleTestConnection(printer.id)}
                            disabled={!selected}
                          >
                            <TestTube2 className="h-3 w-3" />
                            Test
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="top" className="text-xs">
                          Test connection & print
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-6 w-6 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            onClick={handleRefreshPrinters}
                            disabled={isDiscoveringPrinters}
                          >
                            <Search className="h-3 w-3" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="top" className="text-xs">
                          Refresh printer status
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  
                  {/* Category assignment only for system 2/3 */}
                  {!isSystemOne && (
                    <div className="flex flex-wrap gap-1 mt-6 items-center w-full">
                      {categories.map((category) => {
                        const isAssigned = printer.assignedCategories.includes(category.id);
                        return (
                          <Button
                            key={category.id}
                            variant={isAssigned ? "default" : "outline"}
                            size="sm"
                            className={cn(
                              "h-6 px-2 text-xs",
                              isAssigned ? "bg-primary/80" : "",
                              !selected && "opacity-50 pointer-events-none cursor-not-allowed"
                            )}
                            onClick={() => selected && assignCategoryToPrinter(printer.id, category.id)}
                            disabled={!selected}
                          >
                            <span className="mr-1">{category.emoji || '🍽️'}</span>{category.name}
                          </Button>
                        );
                      })}
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-3 w-3 ml-1 text-muted-foreground cursor-pointer" />
                          </TooltipTrigger>
                          <TooltipContent side="top" className="text-xs max-w-xs">
                            Assign categories (stations) to this printer. Only selected printers can be assigned.
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 gap-2 text-center border rounded-md bg-muted/30">
            <Printer className="h-8 w-8 text-muted-foreground mb-1" />
            <div className="text-sm text-muted-foreground font-medium">No printers found</div>
            <div className="text-xs text-muted-foreground max-w-sm">
              📡 Click "Refresh" to discover network printers or install a thermal printer driver and restart the app
            </div>
            <div className="flex gap-2 mt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshPrinters}
                disabled={isDiscoveringPrinters}
                className="flex items-center gap-2"
              >
                <Search className="h-4 w-4" />
                {isDiscoveringPrinters ? 'Scanning...' : 'Scan for Printers'}
              </Button>

              {process.env.NODE_ENV === 'development' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={createDevelopmentPrinters}
                  className="flex items-center gap-2 bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100"
                >
                  <TestTube2 className="h-4 w-4" />
                  Create Development Printers
                </Button>
              )}
            </div>
          </div>
        )}
        

        
        {scanners.length > 0 ? (
          <div className="border rounded-md divide-y bg-muted/30 mt-1">
            {scanners.map((scanner) => (
              <div key={scanner.id} className="flex items-center gap-2 px-2 py-1 text-xs">
                <input
                  type="checkbox"
                  checked={selectedScannerIds.includes(scanner.id)}
                  onChange={() => handleScannerSelect(scanner.id)}
                  className="accent-primary h-4 w-4"
                  name="scanner-select"
                />
                <Scan className="h-4 w-4 mr-1" />
                <span className="font-medium truncate max-w-[120px]">{scanner.name}</span>
                <span className="ml-auto text-muted-foreground">{scanner.status}</span>
                </div>
              ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 gap-2 text-center border rounded-md bg-muted/30 mt-1">
            <Scan className="h-8 w-8 text-muted-foreground mb-1" />
            <div className="text-sm text-muted-foreground font-medium">No USB barcode scanners found</div>
            <div className="text-xs text-muted-foreground">Connect a scanner to get started.</div>
          </div>
        )}
      {(printers.length > 0 || scanners.length > 0) && (
        <div className="flex gap-4 mt-1 text-xs justify-center">
          <span className="flex items-center gap-1 text-green-700">
            <Printer className="h-4 w-4" />
            {printers.filter(p => p.status === 'online').length}/{printers.length} online
          </span>
          {printers.filter(p => p.status === 'offline').length > 0 && (
            <span className="flex items-center gap-1 text-red-700">
              🔴 {printers.filter(p => p.status === 'offline').length} offline
            </span>
          )}
          {selectedSystem !== 'single' && printers.length > 0 && (
            <span className="flex items-center gap-1 text-blue-700">
              <Users className="h-4 w-4" />
              {printers.reduce((acc, p) => acc + p.assignedCategories.length, 0)} assignments
            </span>
          )}
        </div>
      )}
      {samplePrintJob && (
        <PrintPreviewDialog
          open={showPreviewDialog}
          onOpenChange={setShowPreviewDialog}
          printJob={samplePrintJob}
          onPrint={() => toast({ title: "Simulated print initiated." })}
        />
      )}
    </div>
  );
}