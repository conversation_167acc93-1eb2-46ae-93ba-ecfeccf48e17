"use client";

import React, { useState, useMemo, useEffect } from 'react';
import { PurchaseLog } from '@/types/stock';
import { Supplier } from '@/types/suppliers';
import { StockItem } from '@/types/stock';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from '@/components/ui/input';
import { Calendar, Package, Truck, ChevronDown, ChevronRight, Search, ShoppingCart, Edit } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ReceiptImageViewer } from './ReceiptImageViewer';

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('fr-DZ', {
    style: 'currency',
    currency: 'DZD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

export interface PurchaseBatch {
  id: string;
  date: string;
  supplier: Supplier | null;
  items: Array<{
    purchase: PurchaseLog;
    stockItem: StockItem | null;
  }>;
  totalCost: number;
}

interface BatchedPurchaseHistoryProps {
  purchases: PurchaseLog[];
  stockItems: StockItem[];
  suppliers: Supplier[];
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onEditPurchase?: (batch: PurchaseBatch) => void;
  isMobile?: boolean;
}

export function BatchedPurchaseHistory({
  purchases,
  stockItems,
  suppliers,
  searchTerm,
  onSearchChange,
  onEditPurchase,
  isMobile = false
}: BatchedPurchaseHistoryProps) {
  const [expandedBatch, setExpandedBatch] = useState<string | null>(null);

  // Debug: Log purchases with images
  useEffect(() => {
    const purchasesWithImages = purchases.filter(p => p.receiptImage || p.receiptImageLocal);
    console.log('📊 BatchedPurchaseHistory Debug:', {
      totalPurchases: purchases.length,
      purchasesWithImages: purchasesWithImages.length,
      imageData: purchasesWithImages.map(p => ({
        id: p.id,
        receiptImage: p.receiptImage,
        receiptImageLocal: p.receiptImageLocal
      }))
    });
  }, [purchases]);

  // Group purchases by date and supplier
  const batchedPurchases = useMemo(() => {
    // First, filter purchases based on search term
    const filteredPurchases = purchases.filter(purchase => {
      const item = stockItems.find(item => item.id === purchase.stockItemId);
      const supplier = suppliers.find(s => s.id === purchase.supplierId);
      
      // Search in item name, supplier name and notes
      return (
        !searchTerm ||
        (item && item.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (supplier && supplier.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (purchase.notes && purchase.notes.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    });

    // Group purchases by date (YYYY-MM-DD) and supplierId
    const purchasesByDateAndSupplier: Record<string, PurchaseBatch> = {};

    filteredPurchases.forEach(purchase => {
      const dateKey = purchase.date.split('T')[0];
      const supplierKey = purchase.supplierId || 'no-supplier';
      const batchKey = `${dateKey}_${supplierKey}`;
      
      if (!purchasesByDateAndSupplier[batchKey]) {
        const supplier = purchase.supplierId 
          ? suppliers.find(s => s.id === purchase.supplierId) || null
          : null;
          
        purchasesByDateAndSupplier[batchKey] = {
          id: batchKey,
          date: dateKey,
          supplier,
          items: [],
          totalCost: 0
        };
      }
      
      const stockItem = stockItems.find(item => item.id === purchase.stockItemId) || null;
      
      purchasesByDateAndSupplier[batchKey].items.push({
        purchase,
        stockItem
      });
      
      purchasesByDateAndSupplier[batchKey].totalCost += purchase.totalCost;
    });

    // Convert to array and sort by date (newest first)
    return Object.values(purchasesByDateAndSupplier)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [purchases, stockItems, suppliers, searchTerm]);

  // Get category icon for an item
  const getCategoryIcon = (category: string | undefined) => {
    if (!category) return <Package className="h-3.5 w-3.5" />;
    
    switch (category) {
      case 'Ingrédients': return <Package className="h-3.5 w-3.5" />;
      case 'Boissons': return <Package className="h-3.5 w-3.5" />;
      case 'Emballages': return <Package className="h-3.5 w-3.5" />;
      default: return <Package className="h-3.5 w-3.5" />;
    }
  };

  return (
    <div className="space-y-2">
      {/* Search bar */}
      <div className="relative mb-3">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Rechercher des achats..."
          className="pl-8 h-9"
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
        />
      </div>

      {batchedPurchases.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground border rounded-md">
          <ShoppingCart className="h-8 w-8 mx-auto mb-2 opacity-40" />
          <p>
            {searchTerm ? "Aucun achat correspondant à votre recherche." : "Aucun historique d'achat disponible."}
          </p>
        </div>
      ) : isMobile ? (
        <div className="space-y-2">
          {batchedPurchases.map((batch) => (
            <div key={batch.id} className="border rounded-md overflow-hidden">
              <div 
                className={cn(
                  "cursor-pointer p-3 transition-colors", 
                  expandedBatch === batch.id ? "bg-muted/30" : ""
                )}
                onClick={() => setExpandedBatch(expandedBatch === batch.id ? null : batch.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {expandedBatch === batch.id ? 
                      <ChevronDown className="h-4 w-4 text-muted-foreground shrink-0" /> : 
                      <ChevronRight className="h-4 w-4 text-muted-foreground shrink-0" />
                    }
                    <div>
                      <div className="font-medium flex items-center gap-1.5">
                        <Calendar className="h-3.5 w-3.5 text-primary" />
                        <span>{format(new Date(batch.date), 'dd MMM yyyy', { locale: fr })}</span>
                      </div>
                      <div className="text-xs text-muted-foreground flex items-center">
                        {batch.supplier ? (
                          <>
                            <Truck className="h-3 w-3 mr-1" /> 
                            <span className="truncate max-w-[150px]">{batch.supplier.name}</span>
                          </>
                        ) : (
                          "Fournisseur non spécifié"
                        )}
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-right">
                      {formatCurrency(batch.totalCost)}
                    </div>
                    <div className="text-xs text-muted-foreground text-right">
                      {batch.items.length} {batch.items.length > 1 ? 'articles' : 'article'}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Expanded Details for Mobile */}
              {expandedBatch === batch.id && (
                <div className="px-3 pb-2 pt-0 bg-muted/10 border-t">
                  {/* Edit Button and Receipt Image for the entire batch */}
                  <div className="mb-2 pb-2 border-b border-muted/40 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {batch.items.some(({ purchase }) => purchase.receiptImage || purchase.receiptImageLocal) && (
                        <>
                          <span className="text-xs font-medium text-muted-foreground">Receipt:</span>
                          <ReceiptImageViewer
                            receiptImage={batch.items.find(({ purchase }) => purchase.receiptImage)?.purchase.receiptImage}
                            receiptImageLocal={batch.items.find(({ purchase }) => purchase.receiptImageLocal)?.purchase.receiptImageLocal}
                            size="md"
                            isMobile={true}
                          />
                        </>
                      )}
                    </div>
                    {onEditPurchase && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-7 px-2 text-xs"
                        onClick={() => onEditPurchase(batch)}
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Modifier
                      </Button>
                    )}
                  </div>
                  {batch.items.map(({ purchase, stockItem }) => (
                    <div key={purchase.id} className="py-2 border-b border-muted/40 last:border-0">
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center gap-1.5">
                          {stockItem && getCategoryIcon(stockItem.category)}
                          <span className="text-sm font-medium">{stockItem ? stockItem.name : 'Article inconnu'}</span>
                        </div>
                        <span className="text-sm font-medium">
                          {formatCurrency(purchase.totalCost)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>
                          {purchase.quantity} {purchase.unit} × {purchase.costPerUnit ? formatCurrency(purchase.costPerUnit) : '-'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="rounded-md border overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date / Fournisseur</TableHead>
                <TableHead className="text-right">Montant</TableHead>
                <TableHead className="text-center w-20">Receipt</TableHead>
                {onEditPurchase && <TableHead className="text-center w-20">Actions</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {batchedPurchases.map((batch) => (
                <React.Fragment key={batch.id}>
                  <TableRow 
                    className={cn(
                      "cursor-pointer hover:bg-muted/50 transition-colors", 
                      expandedBatch === batch.id ? "bg-muted/30" : ""
                    )}
                    onClick={() => setExpandedBatch(expandedBatch === batch.id ? null : batch.id)}
                  >
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {expandedBatch === batch.id ? 
                          <ChevronDown className="h-4 w-4 text-muted-foreground shrink-0" /> : 
                          <ChevronRight className="h-4 w-4 text-muted-foreground shrink-0" />
                        }
                        <div>
                          <div className="font-medium flex items-center gap-1.5">
                            <Calendar className="h-3.5 w-3.5 text-primary" />
                            <span>{format(new Date(batch.date), 'dd MMM yyyy', { locale: fr })}</span>
                          </div>
                          <div className="text-xs text-muted-foreground flex items-center">
                            {batch.supplier ? (
                              <>
                                <Truck className="h-3 w-3 mr-1" /> 
                                <span className="truncate max-w-[200px]">{batch.supplier.name}</span>
                              </>
                            ) : (
                              "Fournisseur non spécifié"
                            )}
                            <span className="mx-1.5">•</span>
                            <span>{batch.items.length} {batch.items.length > 1 ? 'articles' : 'article'}</span>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {formatCurrency(batch.totalCost)}
                    </TableCell>
                    <TableCell className="text-center">
                      {batch.items.some(({ purchase }) => purchase.receiptImage || purchase.receiptImageLocal) && (
                        <ReceiptImageViewer
                          receiptImage={batch.items.find(({ purchase }) => purchase.receiptImage)?.purchase.receiptImage}
                          receiptImageLocal={batch.items.find(({ purchase }) => purchase.receiptImageLocal)?.purchase.receiptImageLocal}
                          size="sm"
                          isMobile={false}
                        />
                      )}
                    </TableCell>
                    {onEditPurchase && (
                      <TableCell className="text-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            onEditPurchase(batch);
                          }}
                          title="Modifier cette transaction"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    )}
                  </TableRow>
                  
                  {/* Expanded Row */}
                  {expandedBatch === batch.id && (
                    <TableRow className="hover:bg-transparent">
                      <TableCell colSpan={onEditPurchase ? 4 : 3} className="p-0 border-t-0">
                        <div className="bg-muted/10 px-10 pb-2">
                          <Table>
                            <TableHeader className="border-b border-muted">
                              <TableRow className="hover:bg-transparent">
                                <TableHead className="py-1.5 text-xs font-medium">Article</TableHead>
                                <TableHead className="py-1.5 text-xs font-medium">Quantité</TableHead>
                                <TableHead className="py-1.5 text-xs font-medium text-right">Prix unitaire</TableHead>
                                <TableHead className="py-1.5 text-xs font-medium text-right">Total</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {batch.items.map(({ purchase, stockItem }) => (
                                <TableRow key={purchase.id} className="hover:bg-muted/20 border-0">
                                  <TableCell className="py-1.5 pl-0">
                                    <div className="flex items-center gap-1.5">
                                      {stockItem && getCategoryIcon(stockItem.category)}
                                      <span className="text-sm">{stockItem ? stockItem.name : 'Article inconnu'}</span>
                                    </div>
                                  </TableCell>
                                  <TableCell className="py-1.5 text-sm">
                                    {purchase.quantity} {purchase.unit}
                                  </TableCell>
                                  <TableCell className="py-1.5 text-sm text-right">
                                    {purchase.costPerUnit ? formatCurrency(purchase.costPerUnit) : '-'}
                                  </TableCell>
                                  <TableCell className="py-1.5 text-sm font-medium text-right">
                                    {formatCurrency(purchase.totalCost)}
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
} 