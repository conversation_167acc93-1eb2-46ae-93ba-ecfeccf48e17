"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useGoogleDriveUpload } from '@/lib/hooks/use-google-drive-upload';
import { Image as ImageIcon, Eye, Download, X, Loader2, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ReceiptImageViewerProps {
  receiptImage?: string; // Google Drive File ID
  receiptImageLocal?: string; // Local path for Electron
  className?: string;
  showBadge?: boolean;
  size?: 'sm' | 'md' | 'lg';
  isMobile?: boolean;
}

// Check if we're in Electron environment
const isElectron = typeof window !== 'undefined' && (window as any).electronAPI;

export function ReceiptImageViewer({
  receiptImage,
  receiptImageLocal,
  className,
  showBadge = false,
  size = 'md',
  isMobile = false
}: ReceiptImageViewerProps) {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const loadingRef = useRef<string | null>(null);
  
  const { getDownloadUrl } = useGoogleDriveUpload();

  // Size configurations
  const sizeConfig = {
    sm: { container: 'h-8 w-8', icon: 'h-4 w-4', badge: 'text-xs' },
    md: { container: 'h-12 w-12', icon: 'h-6 w-6', badge: 'text-sm' },
    lg: { container: 'h-16 w-16', icon: 'h-8 w-8', badge: 'text-base' },
  };

  // Load image URL with proper dependency management
  useEffect(() => {
    const loadImage = async () => {
      if (!receiptImage && !receiptImageLocal) {
        console.log('🖼️ No image data provided');
        setImageUrl(null);
        setIsLoading(false);
        setError(null);
        loadingRef.current = null;
        return;
      }

      // Prevent loading the same image multiple times
      const currentKey = receiptImage || receiptImageLocal || '';
      if (loadingRef.current === currentKey) {
        console.log('🖼️ Already loading this image, skipping');
        return;
      }

      console.log('🖼️ Loading image...', { receiptImage, receiptImageLocal });
      setIsLoading(true);
      setError(null);
      loadingRef.current = currentKey;

      try {
        // Try Google Drive first if online and we have a file ID
        if (receiptImage && typeof navigator !== 'undefined' && navigator.onLine) {
          console.log('🖼️ Attempting Google Drive download for file ID:', receiptImage);
          try {
            const url = await getDownloadUrl(receiptImage);
            console.log('🖼️ Google Drive download result:', { 
              url: url ? 'success' : 'failed', 
              success: !!url,
              actualUrl: url 
            });
            if (url && loadingRef.current === currentKey) {
              setImageUrl(url);
              setIsLoading(false);
              return;
            }
          } catch (driveError) {
            console.warn('🖼️ Failed to load from Google Drive:', driveError);
          }
        }

        // Fallback to local path for Electron
        if (isElectron && receiptImageLocal && loadingRef.current === currentKey) {
          console.log('🖼️ Using Electron local path:', receiptImageLocal);
          try {
            // TODO: Implement Electron file reading via IPC
            // const localUrl = await window.electronAPI.getLocalFileUrl(receiptImageLocal);
            // setImageUrl(localUrl);
            // For now, just indicate that local image exists
            setImageUrl('local-image-placeholder');
            setIsLoading(false);
            return;
          } catch (localError) {
            console.warn('🖼️ Failed to load local image:', localError);
          }
        }

        // If we have a file ID but are offline, show placeholder
        if (receiptImage && typeof navigator !== 'undefined' && !navigator.onLine && loadingRef.current === currentKey) {
          console.log('🖼️ Offline mode - showing placeholder for Google Drive image');
          setImageUrl('offline-image-placeholder');
          setError('Image available when online');
          setIsLoading(false);
          return;
        }

        // No image available
        if (loadingRef.current === currentKey) {
          console.log('🖼️ No image could be loaded');
          setImageUrl(null);
          setError('Image not available');
        }
      } catch (error) {
        console.error('🖼️ Error loading image:', error);
        if (loadingRef.current === currentKey) {
          setError('Failed to load image');
          setImageUrl(null);
        }
      } finally {
        if (loadingRef.current === currentKey) {
          setIsLoading(false);
          loadingRef.current = null;
        }
      }
    };

    loadImage();
  }, [receiptImage, receiptImageLocal, getDownloadUrl]);

  // Don't render anything if no image data
  if (!receiptImage && !receiptImageLocal) {
    return null;
  }

  const config = sizeConfig[size];

  // Download image handler
  const handleDownload = async () => {
    if (!receiptImage) return;
    
    try {
      const url = await getDownloadUrl(receiptImage);
      if (url) {
        const link = document.createElement('a');
        link.href = url;
        link.download = `receipt-${receiptImage}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('Failed to download image:', error);
    }
  };

  return (
    <>
      <div className={cn("relative", className)}>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "border-dashed transition-all duration-200",
            config.container,
            isLoading && "animate-pulse",
            error && "border-red-300 bg-red-50",
            imageUrl && !error && "border-green-300 bg-green-50 hover:bg-green-100"
          )}
          onClick={() => setIsDialogOpen(true)}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className={cn("animate-spin text-muted-foreground", config.icon)} />
          ) : error ? (
            <AlertCircle className={cn("text-red-500", config.icon)} />
          ) : imageUrl === 'offline-image-placeholder' ? (
            <ImageIcon className={cn("text-orange-500", config.icon)} />
          ) : imageUrl === 'local-image-placeholder' ? (
            <ImageIcon className={cn("text-blue-500", config.icon)} />
          ) : imageUrl ? (
            <Eye className={cn("text-green-600", config.icon)} />
          ) : (
            <ImageIcon className={cn("text-muted-foreground", config.icon)} />
          )}
        </Button>

        {showBadge && (
          <Badge 
            variant={error ? "destructive" : imageUrl ? "default" : "secondary"}
            className={cn("absolute -top-2 -right-2", config.badge)}
          >
            {error ? "Error" : imageUrl ? "📁" : "No Image"}
          </Badge>
        )}
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Receipt Image</span>
              <div className="flex gap-2">
                {receiptImage && imageUrl && imageUrl !== 'offline-image-placeholder' && imageUrl !== 'local-image-placeholder' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownload}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsDialogOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {isLoading ? (
              <div className="flex flex-col items-center justify-center py-12 space-y-3">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                <p className="text-sm text-muted-foreground">Loading image from Google Drive...</p>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center py-12 space-y-3">
                <AlertCircle className="h-8 w-8 text-red-500" />
                <p className="text-sm text-red-600">{error}</p>
                <p className="text-xs text-muted-foreground">
                  {receiptImage ? `Google Drive File ID: ${receiptImage}` : 'No image data available'}
                </p>
              </div>
            ) : imageUrl === 'offline-image-placeholder' ? (
              <div className="flex flex-col items-center justify-center py-12 space-y-3">
                <ImageIcon className="h-8 w-8 text-orange-500" />
                <p className="text-sm text-orange-600">Image available when online</p>
                <p className="text-xs text-muted-foreground">
                  Stored in Google Drive - connect to internet to view
                </p>
              </div>
            ) : imageUrl === 'local-image-placeholder' ? (
              <div className="flex flex-col items-center justify-center py-12 space-y-3">
                <ImageIcon className="h-8 w-8 text-blue-500" />
                <p className="text-sm text-blue-600">Local image file</p>
                <p className="text-xs text-muted-foreground">
                  {receiptImageLocal}
                </p>
              </div>
            ) : imageUrl ? (
              <div className="space-y-2">
                <img
                  src={imageUrl}
                  alt="Receipt"
                  className="w-full h-auto rounded-lg border"
                  onError={() => setError('Failed to load image')}
                />
                <p className="text-xs text-muted-foreground text-center">
                  📁 Stored in Google Drive {receiptImage && `(File ID: ${receiptImage})`}
                </p>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 space-y-3">
                <ImageIcon className="h-8 w-8 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">No image available</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
} 