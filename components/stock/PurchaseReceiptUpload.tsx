"use client";

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { useGoogleDriveUpload } from '@/lib/hooks/use-google-drive-upload';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { Camera, Upload, X, Image as ImageIcon, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PurchaseReceiptUploadProps {
  onImageUploaded: (imageData: { fileId: string; localPath?: string; previewUrl: string }) => void;
  onImageRemoved: () => void;
  currentImage?: { fileId: string; localPath?: string; previewUrl: string } | null;
  disabled?: boolean;
  isMobile?: boolean;
}

// Image compression utility
const compressImage = (file: File, maxWidth: number = 1024, quality: number = 0.8): Promise<File> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // Calculate new dimensions
      const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
      canvas.width = img.width * ratio;
      canvas.height = img.height * ratio;
      
      // Draw and compress
      ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);
      
      canvas.toBlob((blob) => {
        if (blob) {
          const compressedFile = new File([blob], file.name, {
            type: 'image/webp',
            lastModified: Date.now(),
          });
          resolve(compressedFile);
        } else {
          resolve(file);
        }
      }, 'image/webp', quality);
    };
    
    img.src = URL.createObjectURL(file);
  });
};

// Check if we're in Electron environment
const isElectron = typeof window !== 'undefined' && (window as any).electronAPI;

export function PurchaseReceiptUpload({
  onImageUploaded,
  onImageRemoved,
  currentImage,
  disabled = false,
  isMobile = false
}: PurchaseReceiptUploadProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const { uploadFile, uploadProgress, isUploading } = useGoogleDriveUpload();

  // Generate file path for local storage (Electron only)
  const generateLocalPath = useCallback((filename: string, purchaseId?: string) => {
    if (!isElectron || !user?.restaurantId) return undefined;
    
    const sanitizedRestaurantId = user.restaurantId.replace(/[^a-zA-Z0-9]/g, '_');
    const sanitizedPurchaseId = purchaseId || `temp_${Date.now()}`;
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
    
    return `/userData/images/${sanitizedRestaurantId}/purchases/${sanitizedPurchaseId}/${sanitizedFilename}`;
  }, [user?.restaurantId]);

  // Handle file processing and upload
  const processFile = useCallback(async (file: File, purchaseId?: string) => {
    if (!user?.restaurantId) {
      toast({
        title: '❌ Error',
        description: 'Restaurant ID not found. Please log in again.',
        variant: 'destructive'
      });
      return;
    }

    setIsProcessing(true);
    
    try {
      // Compress image
      const compressedFile = await compressImage(file);
      
      // Upload to Google Drive with restaurant-specific path
      const timestamp = Date.now();
      const uploadResult = await uploadFile(compressedFile, {
        transactionId: purchaseId || `temp_${timestamp}`,
        fileType: 'receipt'
      });
      
      if (uploadResult.success && uploadResult.fileId) {
        // Generate local path for Electron
        const fileExtension = compressedFile.name.split('.').pop() || 'webp';
        const localPath = generateLocalPath(`${timestamp}.${fileExtension}`, purchaseId);
        
        // Save local copy in Electron
        if (isElectron && localPath) {
          try {
            // TODO: Implement Electron file saving via IPC
            // await window.electronAPI.saveFile(localPath, compressedFile);
          } catch (error) {
            console.warn('Failed to save local copy:', error);
          }
        }
        
        // Create preview URL
        const previewUrl = URL.createObjectURL(compressedFile);
        
        onImageUploaded({
          fileId: uploadResult.fileId,
          localPath,
          previewUrl
        });
        
        toast({
          title: '✅ Success',
          description: 'Receipt image uploaded successfully to Google Drive'
        });
      } else {
        throw new Error(uploadResult.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Error processing image:', error);
      toast({
        title: '❌ Upload Error',
        description: error instanceof Error ? error.message : 'Failed to upload image',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  }, [user?.restaurantId, uploadFile, generateLocalPath, onImageUploaded, toast]);

  // Handle file input change
  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      processFile(file);
    }
    // Reset input
    event.target.value = '';
  }, [processFile]);

  // Handle drag and drop
  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
    
    const file = event.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      processFile(file);
    } else {
      toast({
        title: '❌ Invalid File',
        description: 'Please select an image file',
        variant: 'destructive'
      });
    }
  }, [processFile, toast]);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
  }, []);

  // Handle remove image
  const handleRemoveImage = useCallback(() => {
    if (currentImage?.previewUrl) {
      URL.revokeObjectURL(currentImage.previewUrl);
    }
    onImageRemoved();
  }, [currentImage, onImageRemoved]);

  const isLoading = isUploading || isProcessing;

  return (
    <div className="space-y-2">
      {/* Hidden file inputs */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled || isLoading}
      />
      <input
        ref={cameraInputRef}
        type="file"
        accept="image/*"
        capture="environment"
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled || isLoading}
      />

      {currentImage?.previewUrl ? (
        // Image preview
        <Card className="relative">
          <CardContent className="p-2">
            <div className="relative">
              <img
                src={currentImage.previewUrl}
                alt="Receipt preview"
                className={cn(
                  "w-full rounded-md object-cover",
                  isMobile ? "h-32" : "h-40"
                )}
              />
              <Button
                type="button"
                variant="destructive"
                size="icon"
                className="absolute top-1 right-1 h-6 w-6"
                onClick={handleRemoveImage}
                disabled={disabled}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-1 text-center">
              Receipt image attached (Google Drive)
            </p>
          </CardContent>
        </Card>
      ) : (
        // Upload area
        <div
          className={cn(
            "relative group flex flex-col items-center justify-center border-2 border-dashed rounded-md cursor-pointer transition-colors hover:border-primary/50 bg-muted/20",
            isDragging && "border-primary",
            disabled && "cursor-not-allowed opacity-50",
            isMobile ? "min-h-[80px] p-2" : "min-h-[80px] p-3"
          )}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={() => !isLoading && fileInputRef.current?.click()}
        >
          {isLoading ? (
            <div className="flex flex-col items-center gap-1">
              <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
              <p className="text-xs text-muted-foreground">Chargement...</p>
            </div>
          ) : (
            <div className="flex flex-col items-center gap-1 text-center">
              <ImageIcon className="h-5 w-5 text-muted-foreground transition-transform group-hover:scale-110" />
              <p className="text-[11px] text-muted-foreground">Ajouter un reçu</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
} 