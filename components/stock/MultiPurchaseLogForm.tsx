"use client";

import React, { useState, useEffect } from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import * as z from "zod";
import { StockItem } from '@/types/stock';
import { Supplier } from '@/types/suppliers';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { CalendarIcon, Plus, Trash2, ShoppingCart, DollarSign, PlusCircle, CircleDashed, Calculator, Package } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { SearchableSupplierSelect } from './SearchableSupplierSelect';
import { SearchableStockItemSelect } from './SearchableStockItemSelect';
import { ScrollArea } from '@/components/ui/scroll-area';
import { PurchaseReceiptUpload } from './PurchaseReceiptUpload';
import { Badge } from '@/components/ui/badge';

// Schema for a single purchase item
const purchaseItemSchema = z.object({
  stockItemId: z.string().min(1, { message: "Requis" }),
  quantity: z.coerce.number().min(0.01, { message: "Requis" }),
  purchaseUnitId: z.string().optional(),
  costPerUnit: z.coerce.number().optional(),
  totalCost: z.coerce.number().optional(),
});

// Schema for the entire purchase form
const multiPurchaseSchema = z.object({
  date: z.date({ required_error: "La date est requise" }),
  supplierId: z.string().optional()
    .transform(val => val === "none" ? undefined : val),
  notes: z.string().optional(),
  amountPaid: z.coerce.number().min(0).optional(),
  items: z.array(purchaseItemSchema)
    .min(1, { message: "Ajoutez au moins un article" })
    .refine(items => 
      items.every(item => 
        item.costPerUnit !== undefined || item.totalCost !== undefined
      ),
      {
        message: "Le coût unitaire ou le coût total doit être fourni pour tous les articles",
      }
    )
});

interface MultiPurchaseLogFormProps {
  onSubmit: (data: any[]) => Promise<boolean | void>;
  stockItems: StockItem[];
  suppliers: Supplier[];
  selectedItemId?: string;
  suggestedSupplierId?: string;
  linkWithSupplier?: boolean;
  onCreateSupplier?: (supplier: Partial<Supplier>) => Promise<Supplier>;
  onCreateStockItem?: (data: any) => Promise<StockItem>;
  editingBatch?: {
    date: string;
    supplierId?: string;
    notes?: string;
    items: Array<{
      stockItemId: string;
      quantity: number;
      purchaseUnitId?: string;
      costPerUnit?: number;
      totalCost: number;
    }>;
    amountPaid?: number;
  };
  isMobile?: boolean;
}

export function MultiPurchaseLogForm({
  onSubmit,
  stockItems,
  suppliers,
  selectedItemId,
  suggestedSupplierId,
  linkWithSupplier = true,
  onCreateSupplier,
  onCreateStockItem,
  editingBatch,
  isMobile
}: MultiPurchaseLogFormProps) {
  const [selectedItems, setSelectedItems] = useState<Record<string, StockItem>>({});
  const form = useForm<z.infer<typeof multiPurchaseSchema>>({
    resolver: zodResolver(multiPurchaseSchema),
    defaultValues: editingBatch ? {
      date: new Date(editingBatch.date),
      supplierId: editingBatch.supplierId || 'none',
      notes: editingBatch.notes || '',
      amountPaid: editingBatch.amountPaid || 0,
      items: editingBatch.items.map(item => ({
        stockItemId: item.stockItemId,
        quantity: item.quantity,
        purchaseUnitId: item.purchaseUnitId,
        costPerUnit: item.costPerUnit,
        totalCost: item.totalCost
      }))
    } : {
      date: new Date(),
      supplierId: suggestedSupplierId || 'none',
      notes: '',
      amountPaid: 0,
      items: selectedItemId 
        ? [{ 
            stockItemId: selectedItemId, 
            quantity: 1,
            purchaseUnitId: undefined,
            costPerUnit: undefined,
            totalCost: undefined 
          }] 
        : []
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [amountPaid, setAmountPaid] = useState<number>(editingBatch?.amountPaid || form.getValues('amountPaid') || 0);

  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: "items",
  });
  
  // Start with one empty item row for better UX (unless editing)
  useEffect(() => {
    if (fields.length === 0 && !editingBatch) {
      addItem();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editingBatch]);
  
  // Receipt image state
  const [receiptImage, setReceiptImage] = useState<{
    fileId: string;
    localPath?: string;
    previewUrl: string;
  } | null>(null);

  // Initialize selected items for editing mode
  useEffect(() => {
    if (editingBatch) {
      const newSelectedItems: Record<string, StockItem> = {};
      editingBatch.items.forEach((item, index) => {
        const stockItem = stockItems.find(si => si.id === item.stockItemId);
        if (stockItem) {
          newSelectedItems[index] = stockItem;
        }
      });
      setSelectedItems(newSelectedItems);
    }
  }, [editingBatch, stockItems]);

  // Update selected items when they change
  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      if (name?.startsWith('items.') && name.endsWith('.stockItemId')) {
        const newSelectedItems: Record<string, StockItem> = {};
        
        value.items?.forEach((item: any, index: number) => {
          if (item?.stockItemId) {
            const stockItem = stockItems.find(si => si.id === item.stockItemId);
            if (stockItem) {
              newSelectedItems[index] = stockItem;
            }
          }
        });
        
        setSelectedItems(newSelectedItems);
      }
    });
    
    return () => subscription.unsubscribe();
  }, [form, stockItems]);

  // Add a new item row - Enhanced with multiple options
  const addItem = (count: number = 1) => {
    for (let i = 0; i < count; i++) {
      append({ 
        stockItemId: '', 
        quantity: 1,
        purchaseUnitId: undefined,
        costPerUnit: undefined,
        totalCost: undefined 
      });
    }
  };

  // Handle item selection change
  const handleItemChange = (value: string, index: number) => {
    console.log('🔍 handleItemChange called:', { value, index });
    
    // Parse the value - it could be "stockItemId" or "stockItemId:purchaseUnitId"
    const [stockItemId, purchaseUnitId] = value.includes(':') ? value.split(':') : [value, undefined];
    console.log('📝 Parsed values:', { stockItemId, purchaseUnitId });
    
    form.setValue(`items.${index}.stockItemId`, stockItemId);
    
    // Find the stock item
    const stockItem = stockItems.find(si => si.id === stockItemId);
    console.log('🔍 Found stock item:', stockItem?.name, 'with purchase units:', stockItem?.purchaseUnits?.length);
    
    if (stockItem) {
      // Update selectedItems immediately
      setSelectedItems(prev => ({
        ...prev,
        [index]: stockItem
      }));
      
      // Set the purchase unit if provided from enhanced selection, otherwise use default
      if (purchaseUnitId) {
        console.log('✅ Setting purchase unit from selection:', purchaseUnitId);
        form.setValue(`items.${index}.purchaseUnitId`, purchaseUnitId);
      } else {
        const availableUnits = getAvailablePurchaseUnits(stockItem);
        const defaultUnit = availableUnits.find(u => u.isDefault) || availableUnits[0];
        console.log('🔧 Setting default purchase unit:', defaultUnit.id);
        form.setValue(`items.${index}.purchaseUnitId`, defaultUnit.id);
      }
      
      // Find suggested price from stock item's cost per unit if available
      if (stockItem.costPerUnit) {
        form.setValue(`items.${index}.costPerUnit`, stockItem.costPerUnit);
      }
    } else {
      console.log('❌ Stock item not found for ID:', stockItemId);
      // Remove from selectedItems if no stock item
      setSelectedItems(prev => {
        const newItems = { ...prev };
        delete newItems[index];
        return newItems;
      });
    }
    
    // Reset values when item changes
    form.setValue(`items.${index}.quantity`, 1);
    if (!stockItem?.costPerUnit) {
      form.setValue(`items.${index}.costPerUnit`, undefined);
      form.setValue(`items.${index}.totalCost`, undefined);
    } else {
      calculateCosts(index);
    }
  };

  // Auto-calculate costs for an item - Enhanced tri-directional calculation
  const calculateCosts = (index: number) => {
    const quantity = parseFloat(form.watch(`items.${index}.quantity`)) || 0;
    const costPerUnit = parseFloat(form.watch(`items.${index}.costPerUnit`)) || 0;
    const totalCost = parseFloat(form.watch(`items.${index}.totalCost`)) || 0;

    // Skip calculation if quantity is 0
    if (quantity === 0) return;

    // Calculate based on which fields are filled
    if (quantity > 0 && costPerUnit > 0 && totalCost === 0) {
      // Calculate total from quantity * unit price
      form.setValue(`items.${index}.totalCost` as any, parseFloat((quantity * costPerUnit).toFixed(2)).toString());
    } else if (quantity > 0 && totalCost > 0 && costPerUnit === 0) {
      // Calculate unit price from total / quantity
      form.setValue(`items.${index}.costPerUnit` as any, parseFloat((totalCost / quantity).toFixed(4)).toString());
    } else if (costPerUnit > 0 && totalCost > 0 && quantity === 1) {
      // Calculate quantity from total / unit price (when quantity is default 1)
      const calculatedQuantity = totalCost / costPerUnit;
      form.setValue(`items.${index}.quantity` as any, parseFloat(calculatedQuantity.toFixed(3)).toString());
    }
  };

  // Calculate total cost of all items
  const totalCost = form.watch('items').reduce((sum, item) => {
    const quantity = parseFloat(item.quantity) || 0;
    const costPerUnit = parseFloat(item.costPerUnit) || 0;
    const totalCost = parseFloat(item.totalCost) || 0;
    
    // Use total cost if available, otherwise calculate from quantity * unit price
    const itemTotal = totalCost > 0 ? totalCost : (quantity * costPerUnit);
    return sum + itemTotal;
  }, 0);

  // Set pay full amount
  const handlePayFull = () => {
    form.setValue('amountPaid', totalCost);
    setAmountPaid(totalCost);
  };

  // Handle creating a new supplier
  const handleCreateSupplier = async (supplierData: Partial<Supplier>): Promise<Supplier> => {
    if (!onCreateSupplier) {
      throw new Error("onCreateSupplier is not defined");
    }
    // Use all supplied data from the official SupplierForm
    return await onCreateSupplier(supplierData);
  };

  // Handle form submission
  const handleSubmit = async (formData: z.infer<typeof multiPurchaseSchema>) => {
    try {
      setIsSubmitting(true);
      console.log('Form data being submitted:', formData);
      // Transform data into an array of purchase logs
      const purchaseLogs = formData.items.map(item => {
        const stockItem = stockItems.find(si => si.id === item.stockItemId);
        const itemTotalCost = item.totalCost || (item.quantity * (item.costPerUnit || 0));
        
        // Calculate base quantity if using purchase units
        const purchaseUnit = stockItem ? getAvailablePurchaseUnits(stockItem).find(u => u.id === item.purchaseUnitId) : null;
        const baseQuantity = purchaseUnit ? item.quantity * purchaseUnit.conversionToBase : item.quantity;
        const costPerBaseUnit = purchaseUnit && item.costPerUnit ? item.costPerUnit / purchaseUnit.conversionToBase : (item.costPerUnit || undefined);
        
        return {
          stockItemId: item.stockItemId,
          date: formData.date,
          quantity: item.quantity,
          purchaseUnitId: item.purchaseUnitId === 'base' ? undefined : item.purchaseUnitId,
          baseQuantity: baseQuantity,
          unit: stockItem?.unit || 'pcs',
          costPerUnit: item.costPerUnit,
          costPerBaseUnit: costPerBaseUnit,
          totalCost: itemTotalCost,
          supplierId: formData.supplierId,
          notes: formData.notes,
          // Include receipt image data
          receiptImage: receiptImage?.fileId,
          receiptImageLocal: receiptImage?.localPath,
          // Include supplier linking properties
          linkToSupplier: linkWithSupplier && formData.supplierId && formData.supplierId !== 'none',
          amountPaid: formData.amountPaid || 0
        };
      });
      
      const result = await onSubmit(purchaseLogs);
      
      // Only reset if submission was successful
      if (result !== false) {
        form.reset({
          date: new Date(),
          supplierId: suggestedSupplierId || 'none',
          notes: '',
          amountPaid: 0,
          items: []
        });
        setAmountPaid(0);
        // Clear receipt image
        if (receiptImage?.previewUrl) {
          URL.revokeObjectURL(receiptImage.previewUrl);
        }
        setReceiptImage(null);
      }
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des achats:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle quantity change with smart calculation
  const handleQuantityChange = (event: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newQuantity = parseFloat(event.target.value) || 0;
    form.setValue(`items.${index}.quantity` as any, newQuantity.toString());
    
    // Auto-calculate based on available data
    const costPerUnit = parseFloat(form.watch(`items.${index}.costPerUnit`)) || 0;
    const totalCost = parseFloat(form.watch(`items.${index}.totalCost`)) || 0;
    
    if (newQuantity > 0) {
      if (costPerUnit > 0) {
        // Recalculate total from quantity * unit price
        form.setValue(`items.${index}.totalCost` as any, parseFloat((newQuantity * costPerUnit).toFixed(2)).toString());
      } else if (totalCost > 0) {
        // Recalculate unit price from total / quantity
        form.setValue(`items.${index}.costPerUnit` as any, parseFloat((totalCost / newQuantity).toFixed(4)).toString());
      }
    }
  };

  // Handle cost per unit change with smart calculation
  const handleCostPerUnitChange = (event: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newCostPerUnit = parseFloat(event.target.value) || 0;
    form.setValue(`items.${index}.costPerUnit` as any, newCostPerUnit.toString());
    
    // Auto-calculate based on available data
    const quantity = parseFloat(form.watch(`items.${index}.quantity`)) || 0;
    const totalCost = parseFloat(form.watch(`items.${index}.totalCost`)) || 0;
    
    if (newCostPerUnit > 0) {
      if (quantity > 0) {
        // Recalculate total from quantity * unit price
        form.setValue(`items.${index}.totalCost` as any, parseFloat((quantity * newCostPerUnit).toFixed(2)).toString());
      } else if (totalCost > 0) {
        // Recalculate quantity from total / unit price
        form.setValue(`items.${index}.quantity` as any, parseFloat((totalCost / newCostPerUnit).toFixed(3)).toString());
      }
    }
  };

  // Handle total cost change with smart calculation
  const handleTotalCostChange = (event: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newTotalCost = parseFloat(event.target.value) || 0;
    form.setValue(`items.${index}.totalCost` as any, newTotalCost.toString());
    
    // Auto-calculate based on available data
    const quantity = parseFloat(form.watch(`items.${index}.quantity`)) || 0;
    const costPerUnit = parseFloat(form.watch(`items.${index}.costPerUnit`)) || 0;
    
    if (newTotalCost > 0) {
      if (quantity > 0) {
        // Recalculate unit price from total / quantity
        form.setValue(`items.${index}.costPerUnit` as any, parseFloat((newTotalCost / quantity).toFixed(4)).toString());
      } else if (costPerUnit > 0) {
        // Recalculate quantity from total / unit price
        form.setValue(`items.${index}.quantity` as any, parseFloat((newTotalCost / costPerUnit).toFixed(3)).toString());
      }
    }
  };

  // Duplicate an item row
  const duplicateItem = (index: number) => {
    const item = form.getValues(`items.${index}`);
    append({ ...item });
  };

  // Helper function to get available purchase units for an item
  const getAvailablePurchaseUnits = (stockItem: StockItem) => {
    return [
      // Base unit (always available)
      {
        id: 'base',
        name: stockItem.unit,
        conversionToBase: 1,
        isDefault: !stockItem.purchaseUnits?.some(u => u.isDefault)
      },
      // Additional purchase units
      ...(stockItem.purchaseUnits || [])
    ];
  };

  // Helper function to get selected purchase unit for an item
  const getSelectedPurchaseUnit = (index: number) => {
    const stockItem = selectedItems[index];
    if (!stockItem) return null;
    
    const purchaseUnitId = form.watch(`items.${index}.purchaseUnitId`);
    const availableUnits = getAvailablePurchaseUnits(stockItem);
    
    return availableUnits.find(u => u.id === purchaseUnitId) || availableUnits[0];
  };

  // Helper function to calculate base quantity
  const calculateBaseQuantity = (index: number) => {
    const quantity = form.watch(`items.${index}.quantity`);
    const purchaseUnit = getSelectedPurchaseUnit(index);
    
    if (!purchaseUnit || !quantity) return 0;
    return quantity * purchaseUnit.conversionToBase;
  };

  // Helper function to calculate cost per base unit
  const calculateCostPerBaseUnit = (index: number) => {
    const costPerUnit = form.watch(`items.${index}.costPerUnit`);
    const purchaseUnit = getSelectedPurchaseUnit(index);
    
    if (!purchaseUnit || !costPerUnit) return 0;
    return costPerUnit / purchaseUnit.conversionToBase;
  };

  // Custom remove function that cleans up selectedItems
  const removeItem = (index: number) => {
    remove(index);
    // Clean up selectedItems
    setSelectedItems(prev => {
      const newItems = { ...prev };
      delete newItems[index];
      // Reindex remaining items
      const reindexed: Record<string, StockItem> = {};
      Object.entries(newItems).forEach(([key, value]) => {
        const oldIndex = parseInt(key);
        if (oldIndex > index) {
          reindexed[oldIndex - 1] = value;
        } else {
          reindexed[key] = value;
        }
      });
      return reindexed;
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-3">
        {/* Ultra-Compact Header */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 items-start">
          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel className="text-xs text-muted-foreground mb-1">Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal h-8 text-xs",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP", { locale: fr })
                        ) : (
                          <span>Choisir une date</span>
                        )}
                        <CalendarIcon className="ml-auto h-3.5 w-3.5 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                      initialFocus
                      locale={fr}
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="supplierId"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel className="text-xs text-muted-foreground mb-1">Fournisseur</FormLabel>
                <SearchableSupplierSelect 
                  value={field.value || ''} 
                  onChange={field.onChange}
                  suppliers={suppliers}
                  onCreateSupplier={handleCreateSupplier}
                  compact={true}
                />
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Denser Items Section */}
        <div className="space-y-1.5">
          <h3 className="font-medium flex items-center gap-1.5 text-sm">
            <ShoppingCart className="h-4 w-4" />
            Articles
          </h3>

          <div className="border rounded-md">
            {/* Header */}
            {!isMobile && (
              <div className="grid grid-cols-11 gap-2 items-center bg-muted/50 text-xs font-semibold px-2 py-1 border-b">
                <div className="col-span-5">Article</div>
                <div className="col-span-2 text-center">Quantité</div>
                <div className="col-span-2 text-center">Prix Unit.</div>
                <div className="col-span-2 text-center">Total</div>
              </div>
            )}
            
            {/* Items */}
            <div className="divide-y">
              {fields.map((field, index) => (
                <div key={field.id} className="px-2 py-1 group hover:bg-muted/30">
                  {isMobile ? (
                    <div className="pb-2">
                      {/* ... mobile layout code ... */}
                    </div>
                  ) : (
                    <div className="grid grid-cols-11 gap-2 items-start">
                      <div className="col-span-5 flex items-center gap-2">
                        <SearchableStockItemSelect
                          value={(() => {
                            const stockItemId = form.watch(`items.${index}.stockItemId`);
                            const purchaseUnitId = form.watch(`items.${index}.purchaseUnitId`);
                            if (stockItemId && purchaseUnitId && purchaseUnitId !== 'base') {
                              return `${stockItemId}:${purchaseUnitId}`;
                            }
                            return stockItemId || '';
                          })()}
                          onChange={(val: string) => handleItemChange(val, index)}
                          stockItems={stockItems}
                          onCreateStockItem={onCreateStockItem}
                          placeholder="Sélectionner un article"
                          compact={true}
                          showPurchaseUnits={true}
                        />
                        <Button 
                          type="button" 
                          variant="ghost" 
                          size="icon" 
                          className="h-5 w-5 text-destructive opacity-40 group-hover:opacity-100 flex-shrink-0" 
                          onClick={() => removeItem(index)} 
                          title="Supprimer"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                      
                      <div className="col-span-2">
                        <Input
                          type="number"
                          min="0.01" step="0.01"
                          value={form.watch(`items.${index}.quantity`) || ''}
                          onChange={(e) => handleQuantityChange(e, index)}
                          className="h-7 text-xs text-center" placeholder="1"
                        />
                      </div>
                      
                      <div className="col-span-2">
                        <Input
                          type="number"
                          min="0" step="0.01"
                          value={form.watch(`items.${index}.costPerUnit`) || ''}
                          onChange={(e) => handleCostPerUnitChange(e, index)}
                          className="h-7 text-xs text-center" placeholder="0.00"
                        />
                      </div>
                      
                      <div className="col-span-2">
                        <Input
                          type="number"
                          min="0" step="0.01"
                          value={form.watch(`items.${index}.totalCost`) || ''}
                          onChange={(e) => handleTotalCostChange(e, index)}
                          className="h-7 text-xs text-center font-medium" 
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                  )}
                  
                  {/* Conversion Info */}
                  {selectedItems[index] && (() => {
                    const purchaseUnit = getSelectedPurchaseUnit(index);
                    const qty = form.watch(`items.${index}.quantity`);
                    const cpu = form.watch(`items.${index}.costPerUnit`) ?? 0;
                    return purchaseUnit?.conversionToBase !== 1 && qty > 0 && (
                      <div className="text-[10px] text-muted-foreground mt-0.5 px-2">
                        = {calculateBaseQuantity(index).toFixed(2)} {selectedItems[index]?.unit || 'u'}
                        {cpu > 0 && <span className="ml-2">{calculateCostPerBaseUnit(index).toFixed(3)}€/{selectedItems[index]?.unit}</span>}
                      </div>
                    );
                  })()}
                </div>
              ))}
            </div>

            <div className="p-1 bg-muted/50 border-t">
              <div className="flex gap-1">
                <Button 
                  type="button" 
                  onClick={() => addItem(1)} 
                  variant="outline" 
                  className="flex-1 border-dashed h-7 text-xs"
                  title="Ajouter 1 article"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  1 article
                </Button>
                <Button 
                  type="button" 
                  onClick={() => addItem(5)} 
                  variant="outline" 
                  className="flex-1 border-dashed h-7 text-xs"
                  title="Ajouter 5 articles"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  5 articles
                </Button>
                <Button 
                  type="button" 
                  onClick={() => addItem(10)} 
                  variant="outline" 
                  className="flex-1 border-dashed h-7 text-xs"
                  title="Ajouter 10 articles"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  10 articles
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Hyper-Compact Notes & Receipt Section */}
        <div className="grid grid-cols-3 gap-3">
          <FormField
            control={form.control}
            name="notes"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel className="text-xs text-muted-foreground">Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Notes..."
                    className="resize-none min-h-[80px] text-xs"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="space-y-1.5">
            <label className="text-xs font-medium text-muted-foreground">Reçu</label>
            <PurchaseReceiptUpload
              onImageUploaded={(imageData) => setReceiptImage(imageData)}
              onImageRemoved={() => setReceiptImage(null)}
              currentImage={receiptImage}
              disabled={isSubmitting}
              isMobile={isMobile}
            />
          </div>
        </div>
              
        {/* Fully Rebuilt Submit & Total Section */}
        <div className="sticky bottom-0 bg-background/95 backdrop-blur-sm py-2 px-3 -mx-4 border-t">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Total:</span>
              <span className="text-lg font-bold">{(totalCost || 0).toFixed(2)} DA</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1.5">
                <FormLabel className="text-xs text-muted-foreground">Payé:</FormLabel>
                <Input
                  type="number" min="0" step="0.01"
                  value={form.watch('amountPaid') || amountPaid}
                  onChange={(e) => {
                    const value = parseFloat(e.target.value) || 0;
                    form.setValue('amountPaid', value);
                    setAmountPaid(value);
                  }}
                  className="h-7 text-xs text-right font-semibold w-24"
                  placeholder="0.00"
                />
              </div>
              <Button 
                type="submit" 
                className="h-9 px-4 text-sm"
                disabled={isSubmitting || fields.length === 0}
              >
                {isSubmitting ? (
                  <CircleDashed className="animate-spin h-4 w-4" />
                ) : editingBatch ? (
                  <><ShoppingCart className="h-4 w-4 mr-2" /> Mettre à jour</>
                ) : (
                  <><ShoppingCart className="h-4 w-4 mr-2" /> Enregistrer</>
                )}
              </Button>
            </div>
          </div>
        </div>
      </form>
    </Form>
  );
} 