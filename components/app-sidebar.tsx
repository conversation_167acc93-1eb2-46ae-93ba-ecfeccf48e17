"use client"

import {
  BanknoteIcon,
  BarChart4Icon,
  ChefHat,
  ClipboardList,
  Home,
  Menu,
  Package,
  Package2,
  Settings,
  Truck,
  User,
  UserCog,
  Users,
  WifiOff,
  Crown,
  Server,
  Database,
  Network,
  Smartphone,
  Wifi,
  QrCode,
  TestTube,
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar"

import { useAuth } from "@/lib/context/multi-user-auth-provider"
import { usePermissions } from "@/lib/hooks/use-permissions"
import { useSettings } from '@/lib/context/settings-context'
import { isAdmin as checkIsAdmin } from "@/lib/auth/role-utils"
import { usePathname } from "next/navigation"
import { NavMain } from "./nav-main"
import { NavUser } from "./nav-user"
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import React from 'react';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { getCurrentRestaurantId as getCurrentRestaurantIdUtil } from '@/lib/db/v4/utils/restaurant-id';
import { NavItem } from './nav-main';
import { RefreshCw } from "lucide-react"; // Import RefreshCw for loading indicator
import { SimpleNetworkIndicator } from './simple-network-indicator';

export function AppSidebar() {
  const currentPathname = usePathname() || ""
  const { user } = useAuth()
  const { hasPageAccess, isOwner, isLoading } = usePermissions()
  const { isCogsEnabled } = useSettings()
  const { isMobile } = useSidebar()

  // Debug COGS status
  console.log('[AppSidebar] COGS enabled:', isCogsEnabled);

  // Wait for permissions to be ready
  if (isLoading) {
    return (
      <Sidebar variant="sidebar" collapsible="icon" className="border-r" side="left">
        <SidebarHeader className="border-b flex items-center justify-between px-4 py-2">
          <div className="flex items-center justify-between w-full">
            <SidebarTrigger className={isMobile ? "hidden" : "block"}>
              <Menu className="h-4 w-4" />
            </SidebarTrigger>
            <div className="text-lg font-medium md:hidden">Loading...</div>
          </div>
        </SidebarHeader>
        <SidebarContent className="pt-4 flex items-center justify-center">
           <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
        </SidebarContent>
        <SidebarFooter className="border-t">
          {/* Optional: Add a basic footer even while loading */}
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
    );
  }

  // Use the utility functions directly with the user object
  const isAdmin = checkIsAdmin(user)

  // Define our navigation items structure with direct links
  const createMainNavItems = () => {
    const items = []

    // Home route - everyone can access
    // items.push({
    //   title: "Home",
    //   url: "/",
    //   icon: Home,
    //   isActive: currentPathname === "/",
    // })

    // Menu
    if (hasPageAccess('menu')) {
      items.push({
        title: "Menu",
        url: "/menu",
        icon: Package,
        isActive: currentPathname === "/menu",
      })
    }

    // Orders
    if (hasPageAccess('orders')) {
      items.push({
        title: "Orders",
        url: "/ordering",
        icon: ClipboardList,
        isActive: currentPathname === "/ordering",
      });

      // Orders V4
      if (process.env.NODE_ENV === 'development') {
        items.push({
          title: "Orders V4",
          url: "/orders-v4",
          icon: ClipboardList,
          isActive: currentPathname === "/orders-v4",
        });
      }
    }

    // Finance
    if (hasPageAccess('finance')) {
      items.push({
        title: "Finance",
        url: "/finance",
        icon: BanknoteIcon,
        isActive: currentPathname === "/finance" || currentPathname.startsWith("/finance/"),
      })
    }

    // Analytics
    if (hasPageAccess('analytics')) {
      items.push({
        title: "Analytics",
        url: "/analytics",
        icon: BarChart4Icon,
        isActive: currentPathname === "/analytics" || currentPathname.startsWith("/analytics/"),
      })
    }

    // Inventory
    if (hasPageAccess('inventory')) {
      items.push({
        title: "Inventory",
        url: "/inventory",
        icon: Package2,
        isActive: currentPathname === "/inventory",
      })
    }

    // Suppliers
    if (hasPageAccess('suppliers')) {
      items.push({
        title: "Suppliers",
        url: "/suppliers",
        icon: Truck,
        isActive: currentPathname === "/suppliers",
      })
    }

    // Staff
    if (hasPageAccess('staff')) {
      items.push({
        title: "Staff",
        url: "/staff",
        icon: UserCog,
        isActive: currentPathname === "/staff",
      })
    }

    // Settings
    if (hasPageAccess('settings')) {
      items.push({
        title: "Settings",
        url: "/settings",
        icon: Settings,
        isActive: currentPathname === "/settings",
      })
    }

    // Admin routes
    if (isAdmin) {
      items.push({
        title: "Users",
        url: "/admin/users",
        icon: Users,
        isActive: currentPathname === "/admin/users",
      })
    }

    // Development routes - only in development mode
    if (process.env.NODE_ENV === 'development') {
      // Offline Test
      if (hasPageAccess('settings')) {
        items.push({
          title: "Offline Test",
          url: "/offline-test",
          icon: WifiOff,
          isActive: currentPathname === "/offline-test",
        })
      }

      // Kitchen Printer Test Suite
      if (hasPageAccess('settings')) {
        items.push({
          title: "🧪 Printer Test",
          url: "/debug/kitchen-printer-test",
          icon: TestTube,
          isActive: currentPathname === "/debug/kitchen-printer-test",
        })
      }
      
      // MongoDB Debug page removed

      // Add P2P Sync page
      items.push({
        title: "P2P Sync",
        url: "/p2p-sync",
        icon: Network,
        isActive: currentPathname === "/p2p-sync",
      })

      // Add Connect Devices page
      items.push({
        title: "Connect Devices",
        url: "/connect",
        icon: Smartphone,
        isActive: currentPathname === "/connect",
      })
    }

    return items
  }

  // Organize items into sections
  const allItems = createMainNavItems();
  const frontOfHouse = allItems.filter(item => [
    "Home", "Menu", "Orders"
  ].includes(item.title));
  const backOfHouse = allItems.filter(item => [
    "Inventory", "Suppliers", "Staff"
  ].includes(item.title));
  const dataSection = allItems.filter(item => [
    "Finance", "Analytics"
  ].includes(item.title));
  const miscSection = allItems.filter(item => [
    "Settings", "Users", "Offline Test", "🧪 Printer Test"
  ].includes(item.title));
  const groupedNavItems = [frontOfHouse, backOfHouse, dataSection, miscSection].filter(group => group.length > 0);

  // Log the groupedNavItems to inspect its contents
  console.log('[AppSidebar] groupedNavItems:', groupedNavItems);

  // Create mock user data for NavUser component
  const userData = {
    name: user?.name || "User",
    email: user?.email || "<EMAIL>",
    avatar: ""
  }

  // Debug tools for user menu links (for quick access)
  const userMenuLinks = [
    {
      title: 'P2P Sync',
      url: '/p2p-sync',
      icon: Network,
    },
    {
      title: 'mDNS Browser',
      url: '/mdns-browser',
      icon: Wifi,
    },
    {
      title: 'Connect Devices',
      url: '/connect',
      icon: Smartphone,
    },
    {
      title: 'QR Scanner',
      url: '/qr-scanner',
      icon: QrCode,
      mobileOnly: true, // Only show on mobile
    },
    // ... you can add more user menu links here if needed
  ];

  return (
    <Sidebar 
      variant="sidebar" 
      collapsible="icon" 
      className="border-r"
      side="left"
    >
      <SidebarHeader className="border-b flex items-center justify-between px-4 py-2">
        <div className="flex items-center justify-between w-full">
          <SidebarTrigger className={isMobile ? "hidden" : "block"}>
            <Menu className="h-4 w-4" />
          </SidebarTrigger>
          <div className="text-lg font-medium md:hidden">Restaurant Manager</div>
        </div>
      </SidebarHeader>
      <SidebarContent className="pt-4">
        <NavMain groupedItems={groupedNavItems} />
      </SidebarContent>
      <SidebarFooter className="border-t">
        <SimpleNetworkIndicator className="mb-2" />
        <NavUser user={userData} extraLinks={userMenuLinks} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}