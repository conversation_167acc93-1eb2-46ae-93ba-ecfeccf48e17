"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "./ui/button"
import { useAuth } from "@/lib/context/multi-user-auth-provider"
import { usePermissions } from "@/lib/hooks/use-permissions"
import { useSettings } from '@/lib/context/settings-context'
import {
  LayoutGrid,
  Menu,
  Home,
  Package,
  ClipboardList,
  Users,
  User,
  Package2,
  Truck,
  UserCog,
  Settings,
  WifiOff,
  ChefHat,
  BanknoteIcon,
  BarChart4Icon,
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuGroup, DropdownMenuTrigger } from "./ui/dropdown-menu"
import { SyncIndicator } from "./SyncIndicator"
import { P2PStatus } from "./ui/p2p-status"
import { UserSwitcher } from "@/app/components/UserSwitcher"
import { isAdmin as checkIsAdmin, isOwner as checkIsOwner, canManageStaff as checkCanManageStaff } from "@/lib/auth/role-utils"
import { useStaticNavigation, isStaticMode } from "@/lib/utils/navigation"

export function MainNav({
  className,
  ...props
}: React.HTMLAttributes<HTMLElement>) {
  const pathname = usePathname()
  const { isAuthenticated, user, logout } = useAuth()
  const { hasPageAccess, isOwner } = usePermissions()
  const [isOpen, setIsOpen] = React.useState(false)
  const { navigate } = useStaticNavigation()

  // Use the utility functions directly with the user object
  const isAdmin = checkIsAdmin(user)
  const canManageStaff = checkCanManageStaff(user)

  console.log('MainNav - User roles:', {
    isAdmin,
    isOwner,
    role: user?.role,
    canManageStaff
  });

  // 🏠 Home route - everyone can access
  const homeRoute = {
    href: "/",
    label: "Home",
    active: pathname === "/",
    icon: <Home className="h-4 w-4 mr-2" />
  }

  // 🔗 Main navigation items with permission checks
  const navItems = [
    {
      href: "/menu",
      label: "Menu",
      permission: "menu" as const,
      icon: <Package className="h-4 w-4 mr-2" />,
      active: pathname === "/menu",
    },
    {
      href: "/ordering",
      label: "Orders",
      permission: "orders" as const,
      icon: <ClipboardList className="h-4 w-4 mr-2" />,
      active: pathname === "/ordering" || pathname.startsWith("/ordering/"),
    },
    {
      href: "/finance",
      label: "Finance",
      permission: "finance" as const,
      icon: <BanknoteIcon className="h-4 w-4 mr-2" />,
      active: pathname === "/finance" || pathname.startsWith("/finance/"),
    },
    {
      href: "/inventory",
      label: "Inventory",
      permission: "inventory" as const,
      icon: <Package2 className="h-4 w-4 mr-2" />,
      active: pathname === "/inventory",
    },
    {
      href: "/staff",
      label: "Staff",
      permission: "staff" as const,
      icon: <UserCog className="h-4 w-4 mr-2" />,
      active: pathname === "/staff",
    },
    {
      href: "/suppliers",
      label: "Suppliers",
      permission: "suppliers" as const,
      icon: <Truck className="h-4 w-4 mr-2" />,
      active: pathname === "/suppliers",
    },
    {
      href: "/settings",
      label: "Settings",
      permission: "settings" as const,
      icon: <Settings className="h-4 w-4 mr-2" />,
      active: pathname === "/settings",
    },
  ]

  // 🔍 Filter items based on permissions
  const visibleNavItems = navItems.filter(item => hasPageAccess(item.permission))

  // 👑 Admin-only routes
  const adminRoutes = isAdmin ? [
    {
      href: "/admin/users",
      label: "Users",
      active: pathname === "/admin/users",
      icon: <Users className="h-4 w-4 mr-2" />
    }
  ] : []

  // 🛠️ Development routes - only in development mode
  const devRoutes = process.env.NODE_ENV === 'development' ? [
    {
      href: "/offline-test",
      label: "Offline Test",
      active: pathname === "/offline-test",
      icon: <WifiOff className="h-4 w-4 mr-2" />
    },
    {
      href: "/debug/kitchen-printer-test",
      label: "🧪 Printer Test",
      active: pathname === "/debug/kitchen-printer-test",
      icon: <Package className="h-4 w-4 mr-2" />
    }
  ] : [];

  // 📋 Combine all routes
  const allRoutes = [
    homeRoute,
    ...visibleNavItems,
    ...adminRoutes,
    ...devRoutes
  ]

  // Handle navigation click
  const handleNavClick = (href: string, e: React.MouseEvent) => {
    if (isStaticMode()) {
      e.preventDefault()
      const cleanPath = href.replace(/^\//, '')
      navigate(cleanPath)
    }
    // In dynamic mode, let Link handle it normally
  }

  return (
    <div className="border-b">
      <div className="flex h-14 items-center px-3">
        {/* 📱 Mobile menu */}
        <div className="md:hidden">
          <Button
            variant="ghost"
            size="icon"
            className="mr-2"
            onClick={() => setIsOpen(!isOpen)}
          >
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle menu</span>
          </Button>
          <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
            <DropdownMenuContent align="start" className="w-52">
              <DropdownMenuLabel>Navigation</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                {allRoutes.map((route) => (
                  <DropdownMenuItem key={route.href} asChild>
                    <Link
                      href={route.href}
                      className={cn(
                        "flex items-center",
                        route.active ? "text-primary font-medium" : "text-muted-foreground"
                      )}
                      onClick={(e) => {
                        handleNavClick(route.href, e)
                        setIsOpen(false)
                      }}
                    >
                      {route.icon}
                      {route.label}
                    </Link>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* 🏠 Logo/Home link */}
        <Link href="/" className="mr-4 flex items-center">
          {/* Bistro App name removed for minimalistic design */}
        </Link>

        {/* 🖥️ Desktop navigation */}
        <nav
          className={cn("hidden md:flex items-center space-x-3 lg:space-x-4", className)}
          {...props}
        >
          {allRoutes.map((route) => (
            <Link
              key={route.href}
              href={route.href}
              onClick={(e) => handleNavClick(route.href, e)}
              className={cn(
                "text-sm font-medium transition-colors hover:text-primary",
                route.active
                  ? "text-black"
                  : "text-muted-foreground"
              )}
            >
              {route.label}
            </Link>
          ))}
        </nav>

        {/* 🔄 Right side components */}
        <div className="ml-auto flex items-center space-x-2">
          <SyncIndicator />
          <P2PStatus />
          {isAuthenticated && <UserSwitcher />}
        </div>
      </div>
    </div>
  )
}