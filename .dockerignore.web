# Dependency directories
node_modules/
.pnpm/
.npm/

# Next.js build output
.next/
out/

# Testing
coverage/

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env
# .env.local - Commented out to include in build
.env.development.local
.env.test.local
.env.production.local

# Git and version control
.git/
.github/
.gitignore

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Temp directories
temp/
tmp/

# Docker files
Dockerfile
docker-compose.yml
.dockerignore

# Electron specific (exclude for web builds)
electron/
build/
dist/

# Mobile specific (exclude for web builds) 
android/
ios/
capacitor.config.ts
CAPACITOR_*.md

# Development and build scripts not needed in web
scripts/db-debug/
scripts/deploy-*.sh
scripts/upload-to-r2.js
scripts/capacitor-*.js
scripts/copy-out-to-electron.js

# Documentation and guides
*.md
docs/

# Build artifacts
*.log
*.tgz
*.tar.gz

# PouchDB data (not needed for web)
electron/pouchdb-data/

# Native build tools
.python-version
*.gyp
*.vcxproj

# Package files for other targets
package.json 