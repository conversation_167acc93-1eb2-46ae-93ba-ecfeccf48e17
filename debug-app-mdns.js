const bonjour = require('bonjour')();

console.log('🔍 Looking for PouchDB app services...\n');

// Browse for HTTP services (matching the app's simplified config)
const browser = bonjour.find({ type: 'http' });

browser.on('up', (service) => {
  console.log(`📢 Found HTTP service: ${service.name}`);
  console.log(`   Type: ${service.type}`);
  console.log(`   Port: ${service.port}`);
  console.log(`   Host: ${service.host}`);
  
  if (service.txt) {
    console.log(`   TXT: ${JSON.stringify(service.txt)}`);
  }
  
  // Check if this is our PouchDB app
  if (service.name.includes('PouchDB')) {
    console.log('🎉 FOUND THE APP\'S POUCHDB SERVICE!');
    console.log(`   Device ID: ${service.txt?.id}`);
    console.log(`   Platform: ${service.txt?.platform}`);
  }
  
  console.log('---');
});

browser.on('down', (service) => {
  if (service.name.includes('PouchDB')) {
    console.log(`📴 PouchDB service went down: ${service.name}`);
  }
});

console.log('👀 Searching for 30 seconds...');
console.log('If you see "🎉 FOUND THE APP\'S POUCHDB SERVICE!" then it\'s working!\n');

setTimeout(() => {
  console.log('🔚 Search complete.');
  browser.stop();
  bonjour.destroy();
  process.exit(0);
}, 30000);

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping search...');
  browser.stop();
  bonjour.destroy();
  process.exit(0);
}); 