// Turbopack configuration
module.exports = {
  // Disable sourcemaps in production to prevent source code leakage
  sourceMaps: process.env.NODE_ENV === 'production' ? false : 'inline-source-map',
  
  // Apply the rule to all JavaScript/TypeScript files
  rules: [
    {
      test: /\.(js|ts|tsx|jsx)$/,
      // Only generate sourcemaps in development
      options: {
        sourceMap: process.env.NODE_ENV !== 'production',
      },
    },
  ],
}; 