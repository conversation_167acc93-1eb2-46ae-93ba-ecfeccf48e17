# 🚀 Multi-Platform Build System

This project supports multiple build targets with smart online/offline capabilities! 🎉

## 🎯 How It Actually Works

### **Development** 🔧
- **All platforms** use local development server (`localhost:3000`)
- Live reloading and hot module replacement
- Full server-side features available

### **Production** 🚀

#### **Web Builds** 🌐
- Uses server-side NextJS with API routes
- Full SSR/SSG capabilities
- MongoDB integration

#### **Static Builds (Mobile/Electron)** 📱🖥️
- **ONLINE**: Calls **remote hosted server** APIs (`https://your-app.com/api/*`)
- **OFFLINE**: Falls back to PouchDB/local storage
- **Smart switching**: Automatically detects connectivity and switches modes

## 📱 Available Targets

- **`web`**: Server-side NextJS with API routes (default)
- **`static`**: Static export for offline apps (Electron/Mobile)
- **`electron`**: Static build + Electron packaging  
- **`mobile`**: Static build for Capacitor mobile apps

## 🛠️ Build Commands

```bash
# Development (all platforms use local dev server)
npm run dev                    # Web development
npm run cap:dev:android        # Mobile development (connects to localhost:3000)
npm run electron:dev           # Electron development

# Production builds
npm run build:web              # Web production (server-side)
npm run build:mobile           # Mobile production (static + remote API)
npm run build:electron         # Electron production (static + remote API)

# Mobile-specific production
npm run cap:prod:android       # Complete mobile production build
```

## ✨ Environment Configuration

Set your remote server URL for production static builds:

```bash
# In your environment or .env file
NEXT_PUBLIC_REMOTE_SERVER_URL=https://your-hosted-app.com
```

## 🔄 Smart API Behavior

The system automatically handles API calls based on environment:

```tsx
import { useOfflineApi } from '@/lib/api/offline-api-client';

function MyComponent() {
  const offlineApi = useOfflineApi();
  
  // This automatically:
  // 1. Tries remote server API when online (production static)
  // 2. Falls back to local PouchDB when offline
  // 3. Uses local dev server in development
  const result = await offlineApi.getStaff('restaurant123');
  
  console.log('Data source:', result.source); // 'remote' or 'local'
}
```

## 📦 Build Target Features

### **Web Builds** 🌐
- ✅ NextJS API routes (`/api/*`)
- ✅ Server-side authentication  
- ✅ MongoDB integration
- ✅ SSR/SSG

### **Static Builds (Mobile/Electron)** 📱🖥️

#### When Online 🌐
- ✅ Calls remote server APIs (`https://your-app.com/api/*`)
- ✅ Full authentication with hosted server
- ✅ Real-time data sync
- ✅ Caches responses for offline use

#### When Offline 📱  
- ✅ PouchDB local storage
- ✅ Offline authentication (cached credentials)
- ✅ Local data persistence
- ✅ Graceful degradation

#### Platform-Specific
- ✅ **Electron**: Direct CouchDB connection for better performance
- ✅ **Mobile**: IndexedDB for browser compatibility
- ✅ **Capacitor**: Native mobile integration

## 🚨 Key Difference from Before

### ❌ **Previous (Incorrect) Approach**
- Mobile/Electron apps were truly offline-only
- No way to authenticate with server when online
- Couldn't sync fresh data from hosted app

### ✅ **Current (Correct) Approach**  
- **Development**: All platforms use local dev server
- **Production Static**: Online → Remote server, Offline → Local storage
- **Production Web**: Always uses local server APIs

## 🎯 Migration Guide

### Replace Direct API Calls

```tsx
// ❌ OLD: Direct fetch (breaks in mobile)
const response = await fetch('/api/staff/auth', {
  method: 'POST',
  body: JSON.stringify(credentials)
});

// ✅ NEW: Smart API client  
import { offlineApi } from '@/lib/api/offline-api-client';
const result = await offlineApi.authenticateStaff(credentials);
// Automatically uses remote server when online, local when offline
```

### Environment Detection

```tsx
import { BUILD_CONFIG } from '@/lib/build-config';

// Check what environment you're in
console.log('Build target:', BUILD_CONFIG.isStatic ? 'static' : 'server');
console.log('Platform:', BUILD_CONFIG.isElectron ? 'electron' : 'mobile/web');
console.log('Environment:', BUILD_CONFIG.isDevelopment ? 'dev' : 'prod');
console.log('Remote server:', BUILD_CONFIG.remoteServerUrl);
```

## 🚀 Quick Start

1. **Set your remote server URL**:
   ```bash
   export NEXT_PUBLIC_REMOTE_SERVER_URL=https://your-hosted-app.com
   ```

2. **Development**:
   ```bash
   npm run dev                 # Start web dev server
   npm run cap:dev:android     # Mobile dev (connects to local server)
   ```

3. **Production**:
   ```bash
   npm run cap:prod:android    # Mobile production (uses remote server when online)
   ```

4. **Test offline**: Turn off WiFi and verify the mobile app still works with cached data!

This system gives you the best of both worlds: **server integration when online** and **offline capability when needed**! 🎉 