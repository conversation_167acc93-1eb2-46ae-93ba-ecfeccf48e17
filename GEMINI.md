dont run builds or the app tell me do so i verify builds manually
# Application Summary

This document provides an overview of the "shop" application, detailing its architecture, core technologies, and key development conventions.

## 1. Introduction

The "shop" application is a hybrid platform designed for restaurant management, leveraging a combination of Next.js for its frontend, Electron for desktop functionality, and Capacitor for mobile deployments. It operates with an offline-first philosophy, ensuring robust performance even without constant internet connectivity.

## 2. Core Technologies

### 2.1 Frontend

*   **Framework**: Next.js (utilizing the App Router for modern routing and server components).
*   **UI Library**: Shadcn UI, with a strong emphasis on:
    *   **Minimalism & Compactness**: Designs are space-efficient and straightforward.
    *   **Vercel Outline Convention**: Preference for outline styling over card-like designs to create integrated, continuous layouts.
    *   **Color Theming**: Uses OKLCH color format with CSS variables for color values, wrapped with `oklch(var(--variable))` in Tailwind config.
    *   **Font Size**: Font sizes are kept reasonable, avoiding overly small text.
    *   **Layout**: Interfaces are well-organized with intuitive element positioning, focusing on visual hierarchy and removing unnecessary "fluff."

### 2.2 Backend & Database (Offline-First)

*   **Database Convention**: Exclusive use of PouchDB-CouchDB.
*   **Local Data Storage (Electron)**: PouchDB connects directly to a bundled CouchDB server. **Note**: The LevelDB implementation has been completely removed.
*   **Local Data Storage (Web/Mobile)**: PouchDB uses IndexedDB via Capacitor.
*   **Offline API Client**: Features intelligent connectivity checks, falling back to locally cached data (PouchDB) if the remote server is unreachable.

### 2.3 Desktop (Electron)

*   **Main Process**: Manages the main window, handles inter-process communication (IPC) with the Next.js renderer process, and integrates local database management.
*   **Auto-Updates**: Incorporates `electron-updater` for seamless application updates.
*   **P2P Sync**: Includes a peer-to-peer synchronization system.
*   **Peripherals**: Capable of USB device detection, likely for printers or other hardware.
*   **Builds**: Electron static builds pre-render all pages as `.html` files. Remote server URLs are hardcoded in these builds, as environment variables are not available at runtime. The app runs in pure static mode (`npm run electron:start`), without dev-server fallbacks.

### 2.4 Mobile (Capacitor)

*   Builds for iOS and Android, leveraging IndexedDB for PouchDB.

## 3. Key Conventions & Practices

### 3.1 Authentication

*   **Online Exclusive**: Authentication primarily occurs with a remote server (`bistro.icu`).
*   **Offline Access**: Only previously logged-in accounts can be used in offline mode; no automatic creation or use of demo accounts.
*   **Redirection**: In static builds, non-authenticated users are redirected to the authentication page.

### 3.2 Builds & Releases

*   **Web Builds**: Exclusively for the landing page; do not include restaurant-specific functionality (printing, barcode generation, kitchen operations). Build errors related to native dependencies are irrelevant for web builds.
*   **Production Builds**: Full production builds, configured to connect to `bistro.icu` for authentication and services.
*   **macOS Releases**: DMG-only builds are preferred, skipping ZIP archives and auto-updater metadata to reduce build time.
*   **Build Cleanup**: Old build directories (`out`, `electron/dist`, `electron/app`) must be deleted before running Electron builds to avoid stale files.
*   **Packaging**: Applications must use existing bundles and package them properly with Electron, without downloading external resources during packaging.
*   **R2 Uploads**: Default key for R2 uploads is `bistro.exe`.
*   **API Endpoints**: `http://localhost:3000` is the primary API endpoint for static client-side builds, with `https://bistro.icu` as a fallback when localhost is unreachable.

### 3.3 Financial & Operational Logic

*   **Cash Register (Caisse)**:
    *   **Automatic Session Flow**: After initial setup, sessions transition automatically (close session → count money → new session opens with remaining amount).
    *   **Collection Logic**: Delivery money is collected directly by drivers and goes to the restaurant (never through the cashier's drawer). Cashier collection is based on opening drawer + non-delivery sales + manual deposits - manual withdrawals (excluding delivery sales).
    *   **UI Labels**: "Entrées/Sorties" renamed to "Dépôt Caisse/Retrait Caisse"; "Collectes par Personnel" to "Personnel Caisse"; "Validation des Collectes" to "Collectes Livraisons".
    *   **Waste Integration**: Waste is tracked as lost revenue (selling price of wasted items, not cost) and recorded as an 'expense' in the caisse with full metadata.
*   **Kitchen Printing System**:
    *   **Barcode Uniqueness**: Global item indexing ensures each item has a unique barcode across the entire order, regardless of the station.
    *   **Paper Efficiency**: Reduced item block size, compact barcodes, tighter spacing, and smaller fonts for a more space-efficient layout.
*   **Order ID System**:
    *   **Format**: New order IDs are generated in `order:YYYYMMDD-XXX` format (e.g., `order:************`).
    *   **Daily Reset**: The sequence resets daily (e.g., 001, 002, etc.).
    *   **Kitchen Display**: Kitchen prints and UI show only the daily sequence (e.g., "001") for simplicity.
    *   **Business Day Logic**: Orders placed before 5 AM belong to the previous business day.
*   **Delivery System**:
    *   Deliveries are considered successful by default unless manually marked as failed.
    *   Supports full failure (all items wasted) or partial failure (select wasted items, collection for delivered items only).
    *   Only failed deliveries require manual intervention.

### 3.4 Development Workflow

*   **Testing**: Do not create test files.
*   **Build/Run Commands**: Avoid running or building the application via terminal commands unless explicitly requested.
*   **Documentation**: Use MCP for documentation lookup when encountering issues or seeking best practices.
*   **Code Simplicity**: Prefer simple solutions and minimal helper scripts, avoiding over-complication.
*   **Copywriting**: Focus on delivering high-quality, compelling copy for any promotional or marketing scripts, without scene directions or sound cues.

## 4. Key Takeaways

This application emphasizes a robust offline-first experience, a clean and minimalistic UI/UX guided by Shadcn's principles, and a well-defined development process with specific guidelines for builds, data handling, and feature implementation. 



 plan out (when it feels like it and dont start coding)and tell me what are the best way to go ab the shit im after dont yap too much and dont be technical unless necessary 

 aggressively read files related read everything dont be lazy gather as much context