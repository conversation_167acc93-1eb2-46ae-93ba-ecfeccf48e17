# 🎯 Packaging Consumption System - Implementation Plan

## 📋 Project Overview

Implementation of a comprehensive packaging consumption system that tracks and consumes packaging materials (napkins, containers, bags, etc.) based on:
- **Table-specific consumption** for dine-in orders
- **Menu category + size consumption** for all order types
- **Order type differentiation** (dine-in vs takeaway/delivery)

---

## 🏗️ System Architecture

**Selected Approach: Dedicated Packaging System**
- Clean separation from existing supplement system
- Purpose-built for packaging consumption logic
- Reuses existing consumption infrastructure patterns
- Integrates with current stock management system

---

## 📍 Configuration Integration

**Integration Points:**
- **Tables Tab**: Add packaging configuration to individual tables
- **Menu Categories**: Add packaging configuration in sizes creation/management UI
- **Leverages existing UI patterns** for consistency and familiarity

---

## 🍽️ Table-Level Consumption Logic

### The Problem: Different Tables, Different Consumption

**Real-world scenarios:**
- **Small tables (2 seats)**: 2 napkins, 1 cleaning wipe per order
- **Large tables (8 seats)**: 4 napkins, 2 cleaning wipes per order  
- **Outdoor tables**: Extra napkins due to wind, special cleaning supplies
- **VIP tables**: Premium napkins, additional amenities
- **Bar seating**: Different napkin type, coasters, bar towels
- **Private dining rooms**: Special table settings, extra cleaning supplies

### Solution: Per-Table Packaging Configuration

**Each table can have unique packaging consumption rules:**

```
Table "Terrasse-1" (4 seats, outdoor):
├── Napkin × 3 (wind factor)
├── Wet wipe × 2 (outdoor cleaning)
└── Coaster × 4

Table "VIP-Suite" (6 seats, private):
├── Premium napkin × 6
├── Table cloth × 1
├── Cleaning spray × 1
└── Air freshener × 1

Table "Bar-Stool-3" (1 seat, bar):
├── Bar napkin × 2
├── Coaster × 1
└── Bar towel × 0.5
```

### Consumption Trigger
- **When**: Order payment is processed (same trigger as existing COGS)
- **Condition**: Only for `dine-in` orders
- **Logic**: Consume exactly what's configured for that specific table, regardless of order contents

---

## 🍕 Menu-Level Consumption Logic

### Category + Size Based Rules

**Each category can have size-specific packaging rules with order type differentiation:**

```
Pizza Category:
├── Small Size:
│   ├── Dine-in: Small plate × 1, Napkin × 1
│   └── Takeaway: Small box × 1, Napkin × 1, Paper bag × 1
├── Medium Size:
│   ├── Dine-in: Medium plate × 1, Napkin × 2
│   └── Takeaway: Medium box × 1, Napkin × 2, Paper bag × 1
└── Large Size:
    ├── Dine-in: Large plate × 1, Napkin × 2
    └── Takeaway: Large box × 1, Napkin × 2, Large bag × 1

Beverage Category:
├── Small Size:
│   ├── Dine-in: Glass × 1
│   └── Takeaway: Small cup × 1, Lid × 1, Straw × 1
└── Large Size:
    ├── Dine-in: Large glass × 1
    └── Takeaway: Large cup × 1, Lid × 1, Straw × 1
```

### Consumption Logic
- **When**: Order payment is processed
- **Condition**: For ALL order types (dine-in, takeaway, delivery)
- **Logic**: For each order item, consume packaging based on its category, size, and order type
- **Quantity**: Base packaging quantity × order item quantity

---

## 🔄 Complete Consumption Flow

### Example Scenario: Large Table Order

**Order Details:**
- Table: "Terrasse-1" (4 seats, outdoor)
- Order Type: `dine-in`
- Items: 2× Pizza Small, 1× Pizza Large, 3× Beverage Small

**Consumption Calculation:**

**1. Table-Level Consumption** (because order type = dine-in):
```
Terrasse-1 configuration:
├── Napkin × 3
├── Wet wipe × 2
└── Coaster × 4
```

**2. Menu-Level Consumption** (for each item):
```
2× Pizza Small (dine-in):
├── Small plate × 2
└── Napkin × 2

1× Pizza Large (dine-in):
├── Large plate × 1
└── Napkin × 2

3× Beverage Small (dine-in):
└── Glass × 3
```

**3. Total Consumption:**
```
├── Napkin × 7 (3 table + 2 small pizza + 2 large pizza)
├── Wet wipe × 2 (table only)
├── Coaster × 4 (table only)
├── Small plate × 2 (menu items)
├── Large plate × 1 (menu items)
└── Glass × 3 (menu items)
```

---

## 🚀 Implementation Phases

### Phase 1: Foundation (Table-Level)
**Deliverables:**
- Extend table data schema with packaging configuration
- Create packaging consumption processing function
- Integrate with existing payment flow
- Basic UI for configuring table packaging
- Testing with simple scenarios

**Success Criteria:**
- Tables can have unique packaging rules
- Dine-in orders consume table packaging on payment
- Takeaway orders skip table packaging
- Stock levels update correctly

### Phase 2: Menu Integration
**Deliverables:**
- Extend category data schema with size-based packaging rules
- Enhanced sizes UI with packaging configuration
- Order type differentiation logic
- Menu-level consumption processing

**Success Criteria:**
- Each category size can have different dine-in vs takeaway packaging
- Order items consume appropriate packaging based on size and order type
- Combined table + menu consumption works correctly

### Phase 3: Polish & Optimization
**Deliverables:**
- Enhanced UX for packaging configuration
- Comprehensive validation and error handling
- Integration with existing reports and analytics
- Performance optimization
- Documentation and training materials

**Success Criteria:**
- Intuitive configuration experience
- Robust error handling
- Seamless integration with existing workflows
- Performance meets requirements

---

## 🎨 User Interface Integration

### Tables Tab Enhancement

**Current State:**
- Simple table list with name, seats, edit/delete buttons

**Enhanced State:**
- Table cards show packaging configuration status
- Edit form includes packaging section
- Easy add/remove packaging items
- Stock item picker (filtered to "Emballages" category)
- Quantity input with validation

**Visual Indicators:**
- Tables with packaging: "📦 3 items configured"
- Tables without packaging: "📦 No packaging set"
- Packaging configuration status in table list

### Menu Categories - Sizes Enhancement

**Current State:**
- Size list with add/edit capabilities

**Enhanced State:**
- Each size shows packaging configuration status
- Edit size includes packaging section for both order types
- Side-by-side configuration: Dine-in | Takeaway
- Visual differentiation between order types
- Copy/paste functionality between sizes

**Visual Indicators:**
- Configured sizes: "Small 📦 (2 dine-in, 4 takeaway)"
- Unconfigured sizes: "Medium ⚠️ (no packaging)"

---

## 🔧 Technical Architecture

### Data Storage Strategy
- **Tables**: Extend existing table documents with packaging array
- **Categories**: Extend existing category documents with packaging rules object
- **Stock Integration**: Reuse existing stock item references and validation
- **Consumption Logs**: Leverage existing consumption logging system

### Processing Integration
- **Trigger Point**: Existing `processOrderPayment` function
- **New Function**: `processPackagingConsumption`
- **Error Handling**: Consistent with existing consumption logic
- **Performance**: Batch processing for multiple items

### Validation Strategy
- **Stock Validation**: Ensure packaging items exist and are available
- **Configuration Validation**: Prevent invalid configurations during setup
- **Runtime Validation**: Handle insufficient stock gracefully
- **Data Integrity**: Maintain consistency between packaging rules and stock items

---

## 📊 Business Logic Rules

### Table Consumption Rules
1. **Only applies to dine-in orders**
2. **Consumed once per order** (not per item or per person)
3. **Independent of order contents** (same consumption whether 1 item or 10 items)
4. **Customizable per table** (different tables can have different rules)

### Menu Consumption Rules
1. **Applies to all order types** (dine-in, takeaway, delivery)
2. **Consumed per order item** (quantity matters)
3. **Depends on category, size, and order type**
4. **Consistent within category/size** (all small pizzas use same packaging)

### Combined Logic
- **Dine-in orders**: Table consumption + Menu consumption
- **Takeaway/Delivery orders**: Menu consumption only
- **No double-counting**: Table and menu consumption are additive, not overlapping

---

## 🚨 Edge Cases & Considerations

### User Experience
- **Configuration complexity**: Balance power with simplicity
- **Visual feedback**: Clear indication of packaging configuration status






*This plan provides a comprehensive roadmap for implementing packaging consumption that addresses real-world restaurant needs while integrating seamlessly with existing systems.* 