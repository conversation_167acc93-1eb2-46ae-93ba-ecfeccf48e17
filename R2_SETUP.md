# 🚀 Cloudflare R2 Setup for Windows Releases

This guide shows you how to set up Cloudflare R2 storage for hosting your Windows app releases and enabling the download functionality on your landing page.

## 📋 Environment Variables

Create a `.env` file in your project root with these values:

```bash
# Cloudflare R2 Storage (for Windows releases)
R2_ACCESS_KEY_ID=your_r2_access_key_here
R2_SECRET_ACCESS_KEY=your_r2_secret_key_here
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_BUCKET_NAME=shop-releases
R2_REGION=auto
```

## 🔧 How to Get These Values

### 1. Create R2 Bucket
1. **Go to Cloudflare Dashboard** → **R2 Object Storage**
2. **Create bucket** named `shop-releases` (or your preferred name)
3. **Set permissions** to allow public read access for downloads

### 2. Create API Token
1. **R2 → Manage API Tokens** → **Create API Token**
2. **Configure Token:**
   - **Name**: `shop-releases-token`
   - **Permissions**: `Object Read & Write`
   - **Apply to**: Select your `shop-releases` bucket only
   - **TTL**: Set to never expire (or your preference)
3. **Save the Access Key ID and Secret Key** (you won't see them again!)

### 3. Get R2 Endpoint
1. **Go to your bucket** → **Settings** tab
2. **Copy the S3 API endpoint** (looks like `https://abc123.r2.cloudflarestorage.com`)
3. **This is your R2_ENDPOINT value**

### 4. Update .env File
```bash
R2_ACCESS_KEY_ID=abc123def456ghi789        # From step 2
R2_SECRET_ACCESS_KEY=your_secret_key_here  # From step 2  
R2_ENDPOINT=https://abc123.r2.cloudflarestorage.com  # From step 3
R2_BUCKET_NAME=shop-releases               # From step 1
R2_REGION=auto                             # Keep as 'auto'
```

## 🚀 Release Process

### Dual Upload System 🎯
The system now supports uploading both versioned and latest files automatically:
- **Versioned file**: `bistro-1.0.0.exe` (keeps version history)
- **Latest file**: `bistro-latest.exe` (always points to newest version)

### Complete Deployment (Build + Upload)
```bash
npm run deploy:windows:r2
```
This will:
1. 🧹 Clean previous builds
2. 📦 Install dependencies  
3. 🏗️ Build Windows executable
4. 📤 Upload to R2 securely
5. ✅ Verify upload
6. 🎉 Provide success confirmation

### Manual Upload Options
If you already have a built `.exe` file:

**Single Upload (latest only):**
```bash
npm run upload:r2 path/to/your/file.exe
```

**Dual Upload (versioned + latest):** 🎯
```bash
npm run upload:r2:both path/to/your/file.exe
```

Or directly with scripts:
```bash
# Single upload
node scripts/upload-to-r2.js "./electron/release/bistro-1.0.0.exe"

# Dual upload (recommended)
node scripts/upload-both-to-r2.js "./electron/release/bistro-1.0.0.exe"
```

## 🌐 Landing Page Integration

The landing page download buttons now automatically:
- ✅ Fetch the latest release from `/api/releases/latest`
- ✅ Show loading states with emojis 
- ✅ Handle errors gracefully
- ✅ Display file size information
- ✅ Trigger secure downloads

## 🎯 What This Enables

- ✅ **Professional Downloads**: Clean, secure download experience
- ✅ **Landing Page Integration**: Download buttons work automatically  
- ✅ **Fast Global CDN**: Cloudflare's edge network for fast downloads
- ✅ **No Git Bloat**: Keep large .exe files out of your repository
- ✅ **Easy Updates**: Simple upload process for new releases
- ✅ **Cost Effective**: R2 is cheaper than S3 for storage and bandwidth

## 🛠️ Troubleshooting

### 1. "No release available" Error
**Problem**: The API returns 404 when trying to download
**Solutions**:
- Run `npm run deploy:windows:r2` to build and upload a release
- Check that your `.env` file has correct R2 credentials
- Verify the bucket name matches your configuration
- Ensure the file `bistro-latest.exe` exists in your bucket

### 2. "Invalid credentials" Error  
**Problem**: Upload fails with authentication errors
**Solutions**:
- Double-check your `R2_ACCESS_KEY_ID` and `R2_SECRET_ACCESS_KEY`
- Verify the API token has `Object Read & Write` permissions
- Ensure the token is applied to the correct bucket
- Check that the token hasn't expired

### 3. "Upload failed" Error
**Problem**: File upload to R2 fails
**Solutions**:
- Verify your internet connection
- Check that the bucket exists and is accessible
- Ensure you have sufficient R2 storage quota
- Try running the upload script directly: `node scripts/upload-to-r2.js path/to/file.exe`

### 4. Build Errors
**Problem**: Windows build fails before upload
**Solutions**:
- Ensure you're on a system that can build Windows apps (Windows/WSL)
- Check that all dependencies are installed: `npm install && cd electron && npm install`
- Verify Electron build tools are properly configured
- Try building manually: `cd electron && npm run electron:build:win`

### 5. Download Button Issues
**Problem**: Landing page download doesn't work
**Solutions**:
- Check browser console for JavaScript errors
- Verify the `/api/releases/latest` endpoint is accessible
- Ensure your .env file is properly loaded in production
- Test the API endpoint directly: `curl https://yoursite.com/api/releases/latest`

## 📊 Monitoring & Maintenance

### Check Upload Status
```bash
# Test if file exists
curl https://your-endpoint.r2.cloudflarestorage.com/shop-releases/bistro-latest.exe -I

# Check API response
curl https://yoursite.com/api/releases/latest
```

### Update Release
```bash
# Build and upload new version
npm run deploy:windows:r2

# Or upload existing file
npm run upload:r2 path/to/new-version.exe
```

### Monitor Usage
- Check R2 dashboard for storage usage and bandwidth
- Monitor download analytics through Cloudflare dashboard
- Set up alerts for quota limits if needed

## 🔗 Integration Points

1. **Deployment Script**: `scripts/deploy-windows-r2.sh`
2. **Upload Script**: `scripts/upload-to-r2.js`  
3. **Backend Service**: `lib/services/r2-service.ts`
4. **API Endpoint**: `app/api/releases/latest/route.ts`
5. **Landing Page**: `app/landing/page.tsx` (download buttons)

This secure system ensures your Windows app deployment is safe, efficient, and user-friendly! 🚀