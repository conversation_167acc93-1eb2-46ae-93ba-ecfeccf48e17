#!/usr/bin/env node

/**
 * 🔍 Database Flow Test Script
 * 
 * Tests the complete database flow in Electron static mode
 * Helps diagnose issues with CouchDB connectivity and PouchDB initialization
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

function log(message, color = '\x1b[36m') {
  console.log(`${color}🔍 [DB Test] ${message}\x1b[0m`);
}

function logSuccess(message) {
  console.log(`\x1b[32m✅ [DB Test] ${message}\x1b[0m`);
}

function logError(message) {
  console.log(`\x1b[31m❌ [DB Test] ${message}\x1b[0m`);
}

function logWarning(message) {
  console.log(`\x1b[33m⚠️ [DB Test] ${message}\x1b[0m`);
}

async function testCouchDBConnection() {
  log('Testing CouchDB connection...');
  
  // Check if CouchDB log files exist
  const logPath = path.join(__dirname, '..', 'electron', 'pouchdb-data', 'couchdb-stderr.log');
  if (!fs.existsSync(logPath)) {
    logError('CouchDB log file not found - CouchDB may not have started');
    return false;
  }
  
  // Read the last few lines to get the current port
  const logContent = fs.readFileSync(logPath, 'utf8');
  const lines = logContent.split('\n');
  
  let currentPort = null;
  for (let i = lines.length - 1; i >= 0; i--) {
    const line = lines[i];
    const portMatch = line.match(/Apache CouchDB has started on http:\/\/0\.0\.0\.0:(\d+)\//);
    if (portMatch) {
      currentPort = parseInt(portMatch[1]);
      break;
    }
  }
  
  if (!currentPort) {
    logError('Could not find CouchDB port in logs');
    return false;
  }
  
  logSuccess(`Found CouchDB running on port: ${currentPort}`);
  
  // Test connection
  try {
    const response = await fetch(`http://localhost:${currentPort}/`);
    if (response.ok) {
      const data = await response.json();
      logSuccess(`CouchDB connection successful: ${data.couchdb} version ${data.version}`);
      return currentPort;
    } else {
      logError(`CouchDB connection failed: ${response.status} ${response.statusText}`);
      return false;
    }
  } catch (error) {
    logError(`CouchDB connection error: ${error.message}`);
    return false;
  }
}

async function testDatabaseCreation(port) {
  log('Testing database creation...');
  
  try {
    // Test admin access
    const adminUrl = `****************************:${port}`;
    
    // List databases
    const dbListResponse = await fetch(`${adminUrl}/_all_dbs`);
    if (!dbListResponse.ok) {
      logError(`Failed to list databases: ${dbListResponse.status}`);
      return false;
    }
    
    const databases = await dbListResponse.json();
    logSuccess(`Found ${databases.length} databases: ${databases.slice(0, 5).join(', ')}${databases.length > 5 ? '...' : ''}`);
    
    // Test creating a test database
    const testDbName = 'test-db-flow';
    const createResponse = await fetch(`${adminUrl}/${testDbName}`, {
      method: 'PUT'
    });
    
    if (createResponse.ok || createResponse.status === 412) { // 412 = already exists
      logSuccess(`Test database '${testDbName}' ready`);
      
      // Test document operations
      const testDoc = {
        _id: 'test-doc',
        type: 'test',
        timestamp: new Date().toISOString(),
        message: 'Database flow test'
      };
      
      const docResponse = await fetch(`${adminUrl}/${testDbName}/test-doc`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testDoc)
      });
      
      if (docResponse.ok) {
        logSuccess('Document creation successful');
        
        // Test document retrieval
        const getResponse = await fetch(`${adminUrl}/${testDbName}/test-doc`);
        if (getResponse.ok) {
          const retrievedDoc = await getResponse.json();
          logSuccess(`Document retrieval successful: ${retrievedDoc.message}`);
          return true;
        } else {
          logError('Document retrieval failed');
          return false;
        }
      } else {
        logError(`Document creation failed: ${docResponse.status}`);
        return false;
      }
    } else {
      logError(`Database creation failed: ${createResponse.status}`);
      return false;
    }
  } catch (error) {
    logError(`Database test error: ${error.message}`);
    return false;
  }
}

async function testElectronStaticMode() {
  log('Testing Electron static mode...');
  
  return new Promise((resolve) => {
    // Start Electron in static mode
    const electronProcess = spawn('npm', ['run', 'electron:static'], {
      cwd: path.join(__dirname, '..'),
      stdio: 'pipe'
    });
    
    let output = '';
    let hasStarted = false;
    
    electronProcess.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      console.log(`[Electron] ${text.trim()}`);
      
      // Look for successful startup
      if (text.includes('Electron app started') || 
          text.includes('Window created') ||
          text.includes('ready-to-show')) {
        hasStarted = true;
        logSuccess('Electron app started successfully');
        
        // Give it a moment to initialize
        setTimeout(() => {
          electronProcess.kill('SIGTERM');
          resolve(true);
        }, 5000);
      }
    });
    
    electronProcess.stderr.on('data', (data) => {
      const text = data.toString();
      output += text;
      console.log(`[Electron Error] ${text.trim()}`);
    });
    
    electronProcess.on('close', (code) => {
      if (!hasStarted) {
        logError('Electron failed to start properly');
        console.log('Output:', output.substring(0, 1000) + '...');
        resolve(false);
      }
    });
    
    electronProcess.on('error', (error) => {
      logError(`Electron process error: ${error.message}`);
      resolve(false);
    });
    
    // Timeout after 30 seconds
    setTimeout(() => {
      if (!hasStarted) {
        electronProcess.kill('SIGTERM');
        logError('Electron startup timed out');
        resolve(false);
      }
    }, 30000);
  });
}

async function main() {
  try {
    log('🚀 Starting comprehensive database flow test...');
    log('');
    
    // Test 1: CouchDB Connection
    const port = await testCouchDBConnection();
    if (!port) {
      logError('CouchDB connection test failed');
      process.exit(1);
    }
    log('');
    
    // Test 2: Database Operations
    const dbTest = await testDatabaseCreation(port);
    if (!dbTest) {
      logError('Database operations test failed');
      process.exit(1);
    }
    log('');
    
    // Test 3: Electron Static Mode
    log('Testing Electron static mode (this may take a moment)...');
    const electronTest = await testElectronStaticMode();
    if (!electronTest) {
      logWarning('Electron static mode test had issues - check the output above');
    }
    log('');
    
    // Summary
    logSuccess('🎉 Database flow test completed!');
    logSuccess('');
    logSuccess('✅ CouchDB is running and accessible');
    logSuccess('✅ Database operations work correctly');
    logSuccess(`✅ CouchDB is running on port: ${port}`);
    
    if (electronTest) {
      logSuccess('✅ Electron static mode started successfully');
    } else {
      logWarning('⚠️ Electron static mode needs investigation');
    }
    
    log('');
    log('🔧 Next steps:');
    log('1. If Electron static mode failed, check the renderer console for errors');
    log('2. Verify that the database initialization code is working in the renderer');
    log('3. Check that IPC communication between main and renderer is working');
    
  } catch (error) {
    logError(`Test failed: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { testCouchDBConnection, testDatabaseCreation }; 