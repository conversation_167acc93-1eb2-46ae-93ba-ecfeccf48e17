#!/bin/bash

# 📱 Android R2 Deployment Script
# Builds, signs, and deploys Android APK to R2 storage with update metadata

set -e

echo "📱 Android R2 Deployment Pipeline"
echo "=================================="

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
ANDROID_DIR="$PROJECT_ROOT/android"
APK_OUTPUT_DIR="$ANDROID_DIR/app/build/outputs/apk/release"
KEYSTORE_PROPERTIES="$ANDROID_DIR/keystore.properties"

# Version management
VERSION_FILE="$PROJECT_ROOT/package.json"
if [ -f "$VERSION_FILE" ]; then
    VERSION=$(node -p "require('$VERSION_FILE').version")
else
    VERSION="1.0.0"
fi

echo "📦 Version: $VERSION"

# Check if keystore is configured
if [ ! -f "$KEYSTORE_PROPERTIES" ]; then
    echo "⚠️  No keystore configuration found!"
    echo "📝 Run: npm run android:setup-signing to set up APK signing"
    echo "🔄 Continuing with unsigned APK..."
    SIGNED=false
else
    echo "🔐 Keystore configured - will create signed APK"
    SIGNED=true
fi

# Step 1: Build the app
echo ""
echo "🏗️  Step 1: Building Android release..."
cd "$PROJECT_ROOT"

# Build static export and APK
npm run build:mobile
npx cap sync android

# Set Java environment
export JAVA_HOME="/Applications/Android Studio.app/Contents/jbr/Contents/Home"

# Build APK
cd "$ANDROID_DIR"
./gradlew assembleRelease

# Step 2: Determine APK file
cd "$PROJECT_ROOT"
if [ "$SIGNED" = true ] && [ -f "$APK_OUTPUT_DIR/app-release.apk" ]; then
    APK_FILE="$APK_OUTPUT_DIR/app-release.apk"
    APK_STATUS="signed"
    echo "✅ Signed APK ready: $APK_FILE"
elif [ -f "$APK_OUTPUT_DIR/app-release-unsigned.apk" ]; then
    APK_FILE="$APK_OUTPUT_DIR/app-release-unsigned.apk"
    APK_STATUS="unsigned"
    echo "⚠️  Unsigned APK ready: $APK_FILE"
else
    echo "❌ No APK found! Build may have failed."
    exit 1
fi

# Get APK size
APK_SIZE=$(du -h "$APK_FILE" | cut -f1)
echo "📦 APK size: $APK_SIZE"

# Step 3: Create versioned filename
VERSIONED_APK="bistro-android-$VERSION.apk"
VERSIONED_PATH="/tmp/$VERSIONED_APK"
cp "$APK_FILE" "$VERSIONED_PATH"

echo "📁 Created versioned APK: $VERSIONED_APK"

# Step 4: Create update metadata JSON
UPDATE_JSON="/tmp/android-update.json"
cat > "$UPDATE_JSON" << EOF
{
  "version": "$VERSION",
  "url": "https://your-r2-bucket.com/$VERSIONED_APK",
  "releaseNotes": "Latest version of Bistro Restaurant POS",
  "mandatory": false,
  "size": "$APK_SIZE",
  "status": "$APK_STATUS",
  "buildDate": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
}
EOF

echo "📄 Created update metadata: android-update.json"

# Step 5: Upload to R2
echo ""
echo "☁️  Step 5: Uploading to R2..."

# Upload versioned APK
echo "📤 Uploading APK..."
node "$SCRIPT_DIR/upload-to-r2.js" "$VERSIONED_PATH" "$VERSIONED_APK"

# Upload update metadata
echo "📤 Uploading update metadata..."
node "$SCRIPT_DIR/upload-to-r2.js" "$UPDATE_JSON" "android-update.json"

# Step 6: Cleanup
rm -f "$VERSIONED_PATH" "$UPDATE_JSON"

# Step 7: Summary
echo ""
echo "🎉 Android deployment completed!"
echo ""
echo "📋 Deployment Summary:"
echo "  📱 Platform: Android"
echo "  📦 Version: $VERSION"
echo "  📁 APK: $VERSIONED_APK"
echo "  📊 Size: $APK_SIZE"
echo "  🔐 Status: $APK_STATUS"
echo ""
echo "🔗 Download URLs:"
echo "  📱 APK: https://your-r2-bucket.com/$VERSIONED_APK"
echo "  📄 Update Info: https://your-r2-bucket.com/android-update.json"
echo ""

if [ "$SIGNED" = false ]; then
    echo "⚠️  Note: APK is unsigned - for testing only!"
    echo "💡 To create signed APK: npm run android:setup-signing"
    echo ""
fi

echo "✅ Users can now download and install the latest version!"
echo "🔄 The app will automatically check for updates on startup."
