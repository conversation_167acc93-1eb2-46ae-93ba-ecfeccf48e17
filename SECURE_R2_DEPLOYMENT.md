# 🚀 Secure R2 Deployment Guide

This guide explains how to use the secure Windows app deployment system that uploads directly to Cloudflare R2 without exposing credentials through API endpoints.

## 🔒 Security Benefits

- **No credential exposure**: R2 keys stay in your local `.env` file
- **Direct upload**: No web server or API endpoints required
- **Secure authentication**: Uses AWS SDK with proper credential handling
- **Terminal-only process**: No browser or admin interface needed

## 📋 Prerequisites

1. **Cloudflare R2 bucket** set up (see `R2_SETUP.md`)
2. **Environment variables** configured in `.env`:
   ```env
   R2_ACCESS_KEY_ID=your_access_key
   R2_SECRET_ACCESS_KEY=your_secret_key
   R2_ENDPOINT=https://your-account.r2.cloudflarestorage.com
   R2_BUCKET_NAME=shop-releases
   R2_REGION=auto
   ```

## 🚀 Usage

### Complete Deployment (Recommended)
Build and upload everything in one command:
```bash
npm run deploy:windows:r2
```

This will:
1. 🧹 Clean previous builds
2. 📦 Install dependencies
3. 🏗️ Build Windows executable
4. 📤 Upload to R2 securely
5. ✅ Verify upload
6. 🎉 Provide success confirmation

### Manual Upload Only
If you already have a built `.exe` file:
```bash
npm run upload:r2 path/to/your/file.exe
```

Or directly with the script:
```bash
node scripts/upload-to-r2.js "./electron/release/bistro-1.0.0.exe"
```

## 📊 Terminal Output

The script provides detailed feedback with:
- 🎨 **Colored output** for easy reading
- 📋 **Step-by-step progress** tracking
- 📊 **File size information**
- ✅ **Success confirmations**
- ❌ **Error handling** with troubleshooting tips

Example output:
```
🚀 Starting secure Windows R2 deployment process...

🔧 SECURE R2 DEPLOYMENT
📋 Configuration:
  Release Directory: electron/release
  Upload Script: scripts/upload-to-r2.js
  Credentials: From .env file (secure)

🔍 Validating environment...
  ✅ Environment validated

🏗️ Building Electron app for Windows...
  ✅ Windows build completed

🔍 Locating built executable...
  ✅ Found executable: bistro-1.0.0.exe
  📊 File size: 156 MB

📤 Uploading to Cloudflare R2 (secure)...
  Using direct credential authentication

🚀 UPLOAD: Uploading bistro-1.0.0.exe to R2...
File: bistro-1.0.0.exe
  Size: 156.8 MB
  Key: bistro-latest.exe
⏳ Uploading... please wait
✅ File uploaded successfully!

🚀 VERIFY: Verifying upload...
✅ Upload verified successfully!

🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!
```

## 🔧 Configuration Files

### `scripts/upload-to-r2.js`
- Direct R2 upload script using AWS SDK
- Handles authentication, upload, and verification
- Provides detailed terminal feedback
- Includes error handling and troubleshooting

### `scripts/deploy-windows-r2.sh`
- Complete deployment pipeline
- Builds app and uploads in one command
- Validates environment before starting
- Comprehensive error handling

## 🛠️ Troubleshooting

### Common Issues

1. **Missing .env file**
   ```
   ❌ .env file not found!
   ```
   **Solution**: Create `.env` file with R2 credentials (see `R2_SETUP.md`)

2. **Invalid credentials**
   ```
   ❌ Upload failed: The request signature we calculated does not match
   ```
   **Solution**: Check your R2 access keys are correct

3. **Bucket not found**
   ```
   ❌ Upload failed: NoSuchBucket
   ```
   **Solution**: Verify bucket name and region in `.env`

4. **No .exe file found**
   ```
   ❌ No .exe file found in release directory!
   ```
   **Solution**: Ensure Electron build completed successfully

### Debug Steps

1. **Verify environment**:
   ```bash
   cat .env | grep R2_
   ```

2. **Check bucket access**:
   ```bash
   node scripts/upload-to-r2.js --help
   ```

3. **Manual file check**:
   ```bash
   ls -la electron/release/
   ```

## 🔗 Integration

The uploaded file integrates with your landing page through:
- **Download API**: `/api/releases/latest` fetches download URL
- **Landing page**: Download button triggers secure download
- **File key**: Always uploads as `bistro-latest.exe`

## 📚 Related Documentation

- `R2_SETUP.md` - Setting up Cloudflare R2 bucket
- `components/new-landing/download-section.tsx` - Landing page integration
- `app/api/releases/latest/route.ts` - Download API endpoint

## 🎯 Best Practices

1. **Keep credentials secure**: Never commit `.env` to version control
2. **Test locally first**: Verify upload works before deployment
3. **Monitor file sizes**: Large files take longer to upload
4. **Use consistent naming**: Stick to `bistro-latest.exe` for reliability
5. **Verify downloads**: Test the landing page download after deployment

This secure system ensures your Windows app deployment is both safe and efficient! 🚀