'use client';
import React from 'react';
import MDNSBrowserComponent from '../components/MDNSBrowserComponent';
import { PageHeader } from '@/components/ui/page-header';
import { Wifi, AlertCircle, ArrowLeft } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert } from '@/components/ui/alert';
import { useRouter } from 'next/navigation';

export default function MDNSBrowserPage() {
  const router = useRouter();
  return (
    <div className="container mx-auto py-6 space-y-8">
      <button
        onClick={() => router.back()}
        className="flex items-center gap-2 text-sm text-blue-600 hover:underline mb-4"
      >
        <ArrowLeft className="h-4 w-4" />
        Back
      </button>
      <PageHeader 
        heading="mDNS Service Browser" 
        description="Discover and browse mDNS services on your local network" 
        icon={<Wifi className="h-6 w-6" />}
      />

      <Card>
        <CardHeader>
          <CardTitle>About mDNS Discovery</CardTitle>
          <CardDescription>
            This tool helps you discover services broadcasting on your local network using mDNS/Bonjour.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="info" className="mb-4">
            <AlertCircle className="h-4 w-4 mr-2" />
            <span>
              If you can see your PouchDB service in this browser but not with the <code>dns-sd</code> command,
              it's likely because Node.js mDNS implementations like <code>bonjour-service</code> don't always interoperate 
              perfectly with Apple's native Bonjour tools.
            </span>
          </Alert>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="border rounded-lg p-4">
              <h2 className="text-lg font-semibold mb-4">HTTP Services (_http._tcp)</h2>
              <MDNSBrowserComponent 
                serviceType="_http._tcp" 
                onServiceSelected={(service) => {
                  console.log('Selected HTTP service:', service);
                  
                  // Check if we have addresses
                  if (service.addresses && service.addresses.length > 0) {
                    // Try to open the first address in a new tab
                    const address = service.addresses[0];
                    const url = `http://${address}:${service.port}`;
                    window.open(url, '_blank');
                  }
                }}
              />
            </div>

            <div className="border rounded-lg p-4">
              <h2 className="text-lg font-semibold mb-4">HTTPS Services (_https._tcp)</h2>
              <MDNSBrowserComponent 
                serviceType="_https._tcp" 
                onServiceSelected={(service) => {
                  console.log('Selected HTTPS service:', service);
                  
                  // Check if we have addresses
                  if (service.addresses && service.addresses.length > 0) {
                    // Try to open the first address in a new tab
                    const address = service.addresses[0];
                    const url = `https://${address}:${service.port}`;
                    window.open(url, '_blank');
                  }
                }}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Discovery Troubleshooting</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            If services aren't being discovered, try the following:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>Ensure you're on the same network as the services you're trying to discover.</li>
            <li>Check if your firewall is allowing UDP traffic on port 5353 (used by mDNS).</li>
            <li>Some VPNs may block multicast traffic needed for mDNS to work.</li>
            <li>Try restarting the browser page.</li>
            <li>
              If using <code>dns-sd</code> command alongside this browser, note that sometimes 
              Node.js mDNS implementations don't always show up in Apple's native Bonjour tools.
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
} 