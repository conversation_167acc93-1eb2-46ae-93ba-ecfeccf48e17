import { NextRequest, NextResponse } from "next/server";
import { verifyJwtAuth } from "@/lib/api-middleware";
import { z } from "zod";
// import { hash } from 'bcryptjs'; // No longer needed here, hashing is in mongo-auth-ops
// No longer need CouchDB constants
// import { NEXT_PUBLIC_COUCHDB_URL, USER_DB, authHeader } from "@/lib/server/couchdb-constants"; 
// import { serverGetStaff } from "@/lib/db/server-db"; // Not used in POST or PUT anymore directly
import { addAuthToStaff } from "@/lib/staff/staff-creator"; 
import { updateMongoUserCredentials, getMongoUserById } from "@/lib/auth/mongo-auth-ops";

// Schema for adding auth credentials
const addAuthSchema = z.object({
  username: z.string().min(3, "Username must be at least 3 characters"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  // role: z.string().optional() // Role is typically part of the staff document, not set here directly for auth creation
});

// Schema for updating auth credentials
const updateAuthSchema = z.object({
  username: z.string().min(3, "Username must be at least 3 characters").optional(), // Make username optional for PUT
  password: z.string().min(8, "Password must be at least 8 characters").optional()
});

// POST - Create auth credentials for a staff member by calling addAuthToStaff
export async function POST(
  request: NextRequest,
  context: any
) {
  try {
    const authResult = await verifyJwtAuth(request);
    if (!authResult.success || !authResult.user) { // ensure authResult.user is checked
      return NextResponse.json({ error: authResult.error || "Authentication failed" }, { status: 401 });
    }

    const { staffId } = context.params;
    if (!staffId) {
      return NextResponse.json({ error: "Staff ID is required" }, { status: 400 });
    }

    const { user: requestingUser } = authResult; // Renamed to avoid conflict
    const restaurantId = requestingUser?.restaurantId;
    if (!restaurantId) {
      return NextResponse.json({ error: "Restaurant ID is missing from the token" }, { status: 400 });
    }

    const body = await request.json();
    const validation = addAuthSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid request body", details: validation.error.format() },
        { status: 400 }
      );
    }

    const { username, password } = validation.data;

    // Optional: Verify staffId exists in PouchDB before attempting to add auth
    // This is already done inside addAuthToStaff, but an early check can be useful.
    // const staffList = await serverGetStaff(restaurantId);
    // const staffMemberExists = staffList.members.find((m: any) => m.id === staffId || m._id === staffId);
    // if (!staffMemberExists) {
    //   return NextResponse.json({ error: `Staff member with ID ${staffId} not found` }, { status: 404 });
    // }

    // Call the refactored addAuthToStaff function
    const updatedStaffMember = await addAuthToStaff(staffId, { username, password }, restaurantId);

    return NextResponse.json({
      message: "Auth credentials created successfully for staff member via MongoDB",
      staffMember: updatedStaffMember // This will contain the PouchDB doc info with updated auth linkage
    });

  } catch (error) {
    console.error("Error creating auth credentials for staff:", error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json(
      { error: `Failed to create auth credentials: ${errorMessage}` },
      { status: 500 }
    );
  }
}

// PUT - Update auth credentials for a staff member in MongoDB
export async function PUT(
  request: NextRequest,
  context: any
) {
  try {
    const authResult = await verifyJwtAuth(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: authResult.error || "Authentication failed" }, { status: 401 });
    }

    const { staffId } = context.params;
    if (!staffId) {
      return NextResponse.json({ error: "Staff ID (User ID) is required" }, { status: 400 });
    }

    const { user: requestingUser } = authResult;
    const restaurantIdFromToken = requestingUser?.restaurantId;
    if (!restaurantIdFromToken) {
      return NextResponse.json({ error: "Restaurant ID is missing from the token" }, { status: 400 });
    }

    const body = await request.json();
    const validation = updateAuthSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid request body", details: validation.error.format() },
        { status: 400 }
      );
    }

    const { username, password } = validation.data;

    if (!username && !password) {
      return NextResponse.json({ error: "Nothing to update. Provide username or password." }, { status: 400 });
    }

    // Verify the staff user exists and belongs to the same restaurant (important security check)
    const staffUserToUpdate = await getMongoUserById(staffId);
    if (!staffUserToUpdate) {
      return NextResponse.json({ error: "Staff user not found." }, { status: 404 });
    }
    if (staffUserToUpdate.restaurantId !== restaurantIdFromToken) {
      return NextResponse.json(
        { error: "Forbidden: You do not have permission to update this staff member's credentials." }, 
        { status: 403 }
      );
    }

    // Call the MongoDB update function
    const updateResult = await updateMongoUserCredentials(staffId, {
      username: username,
      plaintextPassword: password
    });

    if (!updateResult.success) {
      return NextResponse.json(
        { error: `Failed to update auth credentials: ${updateResult.error}` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "Auth credentials updated successfully in MongoDB",
      updatedFields: { username } // Indicate what was passed for update; password is not returned
    });
  } catch (error) {
    console.error("Error updating auth credentials:", error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json(
      { error: `Failed to update auth credentials: ${errorMessage}` },
      { status: 500 }
    );
  }
} 