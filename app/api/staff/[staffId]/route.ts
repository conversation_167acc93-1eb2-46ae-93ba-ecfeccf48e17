import { NextRequest, NextResponse } from "next/server";
import { verifyJwtAuth } from "@/lib/api-middleware";
import { cleanRestaurantId } from "@/lib/db/db-utils";
import { staffService } from "@/lib/services/staff-service";

// Fix for NextJS warning about using params synchronously
export async function GET(
  request: NextRequest,
  { params }: { params: { staffId: string } }
) {
  // Fix for NextJS warning about using params synchronously
  const staffId = params.staffId;
  console.log("API: /api/staff/[staffId] - GET request received", { staffId });
  
  try {
    // Verify JWT authentication
    console.log("API: Verifying JWT authentication");
    const authResult = await verifyJwtAuth(request);
    
    if (!authResult.success || !authResult.user) {
      console.error("API: JWT verification failed", { 
        success: authResult.success,
        error: authResult.error,
        hasUser: !!authResult.user
      });
      return NextResponse.json(
        { error: authResult.error || "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Log the authenticated user
    console.log("API: User authenticated", { 
      id: authResult.user.id,
      role: authResult.user.role,
      restaurantId: authResult.user.restaurantId
    });
    
    const { user } = authResult;
    
    if (!user.restaurantId) {
      return NextResponse.json(
        { error: "Missing restaurant ID" },
        { status: 400 }
      );
    }
    
    // Clean the restaurant ID
    const cleanedRestaurantId = cleanRestaurantId(user.restaurantId);
    
    console.log(`API: Getting staff ${staffId} from restaurant ${cleanedRestaurantId}`);
    
    try {
      // Get staff document without using restaurantDb.initialize
      const staffMember = await staffService.getStaff(staffId, cleanedRestaurantId);
      
      if (!staffMember) {
        return NextResponse.json(
          { error: "Staff member not found" },
          { status: 404 }
        );
      }
      
      return NextResponse.json(staffMember);
    } catch (error) {
      console.error(`API: Error getting staff member ${staffId}:`, error);
      
      return NextResponse.json(
        { error: error instanceof Error ? error.message : "Failed to get staff member" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("API: Unhandled error in staff request", error);
    
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
} 