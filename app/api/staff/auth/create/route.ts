import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { createMongoUser } from "@/lib/auth/mongo-auth-ops";
import { cleanRestaurantId } from "@/lib/db/db-utils";

// Validation schema for creating staff auth
const createAuthSchema = z.object({
  staffId: z.string().min(1, "Staff ID is required").uuid("Staff ID must be a valid UUID"),
  username: z.string()
    .min(3, "Username must be at least 3 characters")
    .max(50, "Username must be at most 50 characters")
    .regex(/^[a-zA-Z0-9_]+$/, "Username can only contain letters, numbers, and underscores"),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .max(100, "Password must be at most 100 characters"),
  name: z.string().optional(),
  email: z.string().email("Invalid email").optional().or(z.literal('')),
  role: z.string().optional(),
  restaurantId: z.string().min(1, "Restaurant ID is required"),
});

/**
 * Creates authentication for a staff member
 *
 * This endpoint creates a MongoDB auth user and links it to an existing staff member.
 */
export async function POST(request: NextRequest) {
  console.log('[API] MONGODB_URI (masked):', process.env.MONGODB_URI ? process.env.MONGODB_URI.replace(/\/\/.*:.*@/, '//USER:PASSWORD@') : 'NOT SET');
  try {
    console.log("API: /api/staff/auth/create - Request received");

    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 }
      );
    }

    // Log the request body with sensitive data redacted
    console.log("API: Request body:", {
      ...body,
      password: body.password ? '[REDACTED]' : undefined
    });

    // Validate the request body
    console.log("API: Validating request body");
    const validation = createAuthSchema.safeParse(body);
    if (!validation.success) {
      console.error("API: Validation failed:", validation.error.formErrors.fieldErrors);
      return NextResponse.json(
        {
          error: "Validation failed",
          details: validation.error.formErrors.fieldErrors
        },
        { status: 400 }
      );
    }
    console.log("API: Validation successful");

    // Extract validated data
    const { staffId, username, password, name, email, role, restaurantId } = validation.data;

    // Clean the restaurant ID
    const cleanedRestaurantId = cleanRestaurantId(restaurantId);
    console.log("API: Cleaned restaurant ID:", cleanedRestaurantId);

    console.log(`API: Creating MongoDB user for staff ${staffId} with username ${username}`);

    // Check MongoDB connection before attempting to create user
    try {
      // Import the isMongoDBReachable function
      const { isMongoDBReachable } = await import('@/lib/mongodb');
      const mongoStatus = await isMongoDBReachable();

      if (!mongoStatus.reachable) {
        console.error(`API: MongoDB is not reachable: ${mongoStatus.error}`);
        return NextResponse.json(
          {
            error: "Database connection error",
            details: "The authentication database is currently unavailable. Please try again later.",
            offlineError: true
          },
          { status: 503 } // Service Unavailable
        );
      }

      // Normalize role to a valid AuthUser role
      let normalizedRole: 'owner' | 'admin' | 'manager' | 'staff' | 'user' = 'staff';

      if (role) {
        const lowerRole = role.toLowerCase();
        if (lowerRole === 'owner' || lowerRole === 'admin' || lowerRole === 'manager' || lowerRole === 'user') {
          normalizedRole = lowerRole as 'owner' | 'admin' | 'manager' | 'user';
        } else {
          // Map other roles to 'staff'
          normalizedRole = 'staff';
        }
      }

      console.log(`API: Normalized role from '${role}' to '${normalizedRole}'`);

      // Create the MongoDB user
      console.log(`API: Creating MongoDB user for staff ${staffId}`);
      const mongoUserResult = await createMongoUser({
        name: name || '', // Use provided name or empty string as fallback
        username,
        plaintextPassword: password,
        role: normalizedRole, // Use the normalized role
        restaurantId: cleanedRestaurantId,
        staffIdToLink: staffId,
        email: email || '', // Use provided email or empty string as fallback
        metadata: {
          originalRole: role // Store the original role for reference
        }
      });

      if (!mongoUserResult.success || !mongoUserResult.userId) {
        console.error("API: MongoDB user creation failed:", mongoUserResult.error);
        return NextResponse.json(
          {
            error: mongoUserResult.error || "Failed to create auth user",
            details: "The authentication database could not create the user. Please check the provided information and try again."
          },
          { status: 500 }
        );
      }

      console.log(`API: MongoDB user created successfully with ID ${mongoUserResult.userId}`);
      return NextResponse.json({
        success: true,
        userId: mongoUserResult.userId,
        message: "Auth user created successfully"
      });

    } catch (dbError) {
      console.error("API: Error checking MongoDB or creating user:", dbError);
      return NextResponse.json(
        {
          error: "Database operation failed",
          details: "There was an error connecting to the authentication database.",
          offlineError: true
        },
        { status: 503 } // Service Unavailable
      );
    }
  } catch (error) {
    console.error("API: Error creating staff auth:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Internal server error"
      },
      { status: 500 }
    );
  }
}
