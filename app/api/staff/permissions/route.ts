import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { verifyJwtAuth } from "@/lib/api-middleware";
import { serverGetStaff, serverUpdateStaffMember } from "@/lib/db/server-db";
import { cleanRestaurantId } from "@/lib/db/db-utils"; 

/**
 * PouchDB operations for staff permissions
 */
class StaffPermissionsService {
  constructor() {
    // No need for CouchDB configuration
  }

  /**
   * Get a staff member by ID
   */
  async getStaff(staffId: string, restaurantId: string) {
    try {
      console.log(`StaffPermissionsService: Getting staff with ID ${staffId} from restaurant ${restaurantId}`);
      
      // Get all staff members from PouchDB
      const staffDoc = await serverGetStaff(restaurantId);
      
      if (staffDoc && staffDoc.members && Array.isArray(staffDoc.members)) {
        // Find the staff member by ID
        const staffMember = staffDoc.members.find((m: any) => m.id === staffId);

        if (staffMember) {
          console.log(`StaffPermissionsService: Found staff member ${staffId} in staff collection`);
          return staffMember;
        }
      }

      console.error(`StaffPermissionsService: Staff member ${staffId} not found in staff collection`);
      return null;
    } catch (error) {
      console.error(`StaffPermissionsService: Error getting staff ${staffId}:`, error);
      throw error;
    }
  }

  /**
   * Update a staff member's permissions
   */
  async updateStaffPermissions(staffId: string, permissions: any, restaurantId: string) {
    try {
      console.log(`StaffPermissionsService: Updating permissions for staff ${staffId} in restaurant ${restaurantId}`);
      
      // Update staff member's permissions using serverUpdateStaffMember
      const updatedStaff = await serverUpdateStaffMember(restaurantId, staffId, { permissions });
      
      console.log(`StaffPermissionsService: Successfully updated staff permissions for ${staffId}`);

      return {
        staffId,
        permissions,
        success: true
      };
    } catch (error) {
      console.error(`StaffPermissionsService: Error updating staff permissions:`, error);
      throw error;
    }
  }
}

// Create an instance of the service
const staffPermissionsService = new StaffPermissionsService();

// Validation schema for permission update
const permissionUpdateSchema = z.object({
  staffId: z.string().min(1, "Staff ID is required"),
  restaurantId: z.string().min(1, "Restaurant ID is required"),
  permissions: z.object({
    pages: z.object({
      menu: z.boolean().default(false),
      orders: z.boolean().default(false),
      waiter: z.boolean().default(false),
      kitchen: z.boolean().default(false),
      finance: z.boolean().default(false),
      analytics: z.boolean().default(false),
      inventory: z.boolean().default(false),
      production: z.boolean().default(false),
      staff: z.boolean().default(false),
      settings: z.boolean().default(false),
      suppliers: z.boolean().default(false),
    })
  })
});

/**
 * PUT handler for updating staff permissions
 * NOTE: Temporarily disabled permission checks as requested
 */
export async function PUT(request: NextRequest) {
  console.log("API: /api/staff/permissions - PUT request received");

  try {
    // 1. Verify JWT authentication
    const authResult = await verifyJwtAuth(request);

    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || "Unauthorized" },
        { status: 401 }
      );
    }

    // 2. Check user permissions - TEMPORARILY DISABLED
    const { user } = authResult;
    // All users are allowed to update permissions for now
    
    // 3. Validate request body
    const body = await request.json();
    const validation = permissionUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: "Validation failed",
          details: validation.error.formErrors.fieldErrors
        },
        { status: 400 }
      );
    }

    // 4. Extract and validate data
    const { staffId, permissions } = validation.data;

    // Always use the authenticated user's restaurant ID for security
    const restaurantId = user.restaurantId;
    if (!restaurantId) {
      return NextResponse.json(
        { error: "Missing restaurant ID in user profile" },
        { status: 400 }
      );
    }

    // 5. Clean the restaurant ID
    const cleanedRestaurantId = cleanRestaurantId(restaurantId);

    try {
      // 6. Update the permissions
      await staffPermissionsService.updateStaffPermissions(staffId, permissions, cleanedRestaurantId);

      // 7. Return success response
      return NextResponse.json({
        success: true,
        message: "Staff permissions updated successfully",
        data: {
          staffId,
          permissions
        }
      });
    } catch (error) {
      console.error("Error updating staff permissions:", error);
      return NextResponse.json(
        {
          error: "Error updating staff permissions",
          details: error instanceof Error ? error.message : String(error)
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error in staff permissions API handler:", error);
    return NextResponse.json(
      {
        error: "Server error",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * GET handler for getting all permissions
 * NOTE: Permission checks are temporarily disabled
 */
export async function GET(request: NextRequest) {
  console.log("API: /api/staff/permissions - GET request received");

  try {
    // Verify JWT authentication
    const authResult = await verifyJwtAuth(request);

    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || "Unauthorized" },
        { status: 401 }
      );
    }

    // All users are allowed to view permissions for now
    
    // Get the restaurant ID from the authenticated user
    const { user } = authResult;
    const restaurantId = user.restaurantId;
    
    if (!restaurantId) {
      return NextResponse.json(
        { error: "Missing restaurant ID in user profile" },
        { status: 400 }
      );
    }

    // Clean the restaurant ID
    const cleanedRestaurantId = cleanRestaurantId(restaurantId);

    try {
      // Get all staff members
      const staffData = await serverGetStaff(cleanedRestaurantId);
      
      // Return only the permissions data
      const staffWithPermissions = staffData.members.map((staff: any) => ({
        id: staff.id,
        name: staff.name,
        permissions: staff.permissions || { pages: {} }
      }));

      return NextResponse.json({
        success: true,
        data: staffWithPermissions
      });
    } catch (error) {
      console.error("Error getting staff permissions:", error);
      return NextResponse.json(
        {
          error: "Error getting staff permissions",
          details: error instanceof Error ? error.message : String(error)
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error in staff permissions API handler:", error);
    return NextResponse.json(
      {
        error: "Server error",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}