import { NextRequest, NextResponse } from 'next/server';
import { getMongoUserByUsername } from '@/lib/auth/mongo-auth-ops';
import { verifyPassword } from '@/lib/auth/new-auth-service';
import { generateToken } from '@/lib/auth/new-auth-service';
import { User as JwtUser } from '@/lib/auth/new-auth-service';
import { getStaffMember } from '@/lib/db/v4/operations/per-staff-ops';
import { initializeV4Database } from '@/lib/db/v4';
import { getAllStaff } from '@/lib/db/v4/operations/per-staff-ops';

export async function POST(req: NextRequest) {
  try {
    console.log('[API Staff Login] Received POST request');
    const body = await req.json();
    console.log('[API Staff Login] Request body:', body);
    const { username, password } = body;

    if (!username || !password) {
      console.warn('[API Staff Login] Missing username or password:', { username, password });
      return NextResponse.json(
        { error: 'Username and password are required' },
        { status: 400 }
      );
    }

    // 1. Fetch user from MongoDB by username (no restaurantId)
    console.log('[API Staff Login] Fetching user from MongoDB by username:', username);
    const staffUser = await getMongoUserByUsername(username);

    // Log user details for debugging (without sensitive info)
    if (staffUser) {
      console.log('[API Staff Login] MongoDB user found:', {
        _id: staffUser._id,
        name: staffUser.name,
        username: staffUser.username,
        role: staffUser.role,
        restaurantId: staffUser.restaurantId
      });
    } else {
      console.log('[API Staff Login] No user found with username:', username);
    }

    if (!staffUser) {
      console.warn('[API Staff Login] Invalid credentials - user not found for username:', username);
      return NextResponse.json({ error: 'Invalid credentials - user not found' }, { status: 401 });
    }

    // 2. Verify password
    console.log('[API Staff Login] Verifying password for user:', username);
    const isValidPassword = await verifyPassword(password, staffUser.password);
    console.log('[API Staff Login] Password valid:', isValidPassword);

    if (!isValidPassword) {
      console.warn('[API Staff Login] Invalid credentials - password incorrect for username:', username);
      return NextResponse.json({ error: 'Invalid credentials - password incorrect' }, { status: 401 });
    }

    // 3. Fetch actual permissions from PouchDB staff document
    let actualPermissions = null; // No default permissions - they're ONLY in PouchDB
    
    if (staffUser.restaurantId) {
      try {
        console.log('[API Staff Login] Fetching permissions from PouchDB staff document');
        console.log('[API Staff Login] MongoDB user _id:', staffUser._id);
        console.log('[API Staff Login] Restaurant ID:', staffUser.restaurantId);
        
        // Initialize V4 database for the restaurant
        await initializeV4Database(staffUser.restaurantId);
        
        // Get ALL staff members and find the one with matching userId
        console.log('[API Staff Login] Getting all staff to find matching userId...');
        const allStaff = await getAllStaff();
        console.log('[API Staff Login] Total staff members found:', allStaff.length);
        
        // Find staff document where userId matches MongoDB user's _id
        const staffMember = allStaff.find(staff => staff.userId === staffUser._id);
        
        if (staffMember && staffMember.permissions) {
          console.log('[API Staff Login] ✅ Found staff member with permissions:', {
            docId: staffMember._id,
            staffId: staffMember.id,
            userId: staffMember.userId,
            name: staffMember.name,
            hasPermissions: !!staffMember.permissions,
            pagesPermissions: JSON.stringify(staffMember.permissions.pages, null, 2),
            tabsPermissions: JSON.stringify(staffMember.permissions.tabs, null, 2)
          });
          
          // Use the raw permissions from PouchDB - don't convert to array format
          // The permissions context expects the raw object format
          actualPermissions = staffMember.permissions;
          
          console.log('[API Staff Login] ✅ Using raw permissions for JWT:', JSON.stringify(actualPermissions, null, 2));
          
        } else {
          console.error('[API Staff Login] ❌ Staff member not found or has no permissions');
          console.log('[API Staff Login] Looking for userId:', staffUser._id);
          console.log('[API Staff Login] Available staff members:', allStaff.map(s => ({
            docId: s._id,
            id: s.id,
            name: s.name,
            userId: s.userId,
            hasPermissions: !!s.permissions
          })));
          
          // Set null permissions if staff not found - let the UI handle empty state
          actualPermissions = null;
        }
      } catch (error) {
        console.error('[API Staff Login] ❌ Error fetching permissions from PouchDB:', error);
        // Set null permissions on error - let the UI handle empty state
        actualPermissions = null;
      }
    } else {
      console.error('[API Staff Login] ❌ Missing restaurantId - cannot fetch permissions');
      // Set null permissions if no restaurant - let the UI handle empty state
      actualPermissions = null;
    }

    // 4. Prepare user object for JWT generation
    const userForToken: JwtUser = {
      id: staffUser._id,
      name: staffUser.name,
      email: staffUser.email,
      username: staffUser.username,
      role: staffUser.role,
      restaurantId: staffUser.restaurantId,
      permissions: actualPermissions, // Use actual permissions from PouchDB
      metadata: staffUser.metadata,
    };
    console.log('[API Staff Login] User object for JWT:', {
      ...userForToken,
      permissions: actualPermissions ? 'included' : 'none'
    });

    // 5. Generate JWT
    const token = generateToken(userForToken);
    console.log('[API Staff Login] JWT generated for user:', username);

    return NextResponse.json({
      message: 'Staff login successful',
      token,
      user: {
        id: staffUser._id,
        name: staffUser.name,
        username: staffUser.username,
        role: staffUser.role,
        restaurantId: staffUser.restaurantId
      }
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (error) {
    console.error('[API Staff Login] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: `Login failed: ${errorMessage}` }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}