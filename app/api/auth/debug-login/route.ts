import { NextResponse } from "next/server";
import { getMongoUserByEmail } from '@/lib/auth/mongo-auth-ops';
import { compare } from "bcryptjs";

/**
 * Debug endpoint to help diagnose login issues
 * This should only be enabled in development
 */
export async function POST(req: Request) {
  // Only allow in development
  if (process.env.NODE_ENV !== "development") {
    return NextResponse.json(
      { error: "This endpoint is only available in development" },
      { status: 403 }
    );
  }

  try {
    const body = await req.json();
    const { email, password } = body;
    
    if (!email || !password) {
      return NextResponse.json(
        { error: "Missing email or password" },
        { status: 400 }
      );
    }
    
    console.log(`Debug Login - Checking user: ${email}`);
    
    // Get the user by email
    const user = await getMongoUserByEmail(email);
    
    if (!user) {
      return NextResponse.json(
        { error: "User not found", email },
        { status: 404 }
      );
    }
    
    // Verify the structure of the user object
    const userStructure = {
      id: user._id,
      type: user.type,
      hasEmail: !!user.email,
      hasPassword: !!user.password,
      role: user.role,
      createdAt: user.createdAt,
      hasMetadata: !!user.metadata,
      metadataKeys: user.metadata ? Object.keys(user.metadata) : [],
    };
    
    // Verify the password
    let passwordValid = false;
    let passwordError = null;
    
    try {
      if (user.password) {
        passwordValid = await compare(password, user.password);
      }
    } catch (error) {
      passwordError = error instanceof Error ? error.message : "Unknown error";
    }
    
    return NextResponse.json({
      success: true,
      user: userStructure,
      passwordCheck: {
        valid: passwordValid,
        error: passwordError
      }
    });
  } catch (error) {
    console.error("Debug Login Error:", error);
    return NextResponse.json(
      { error: "Server error", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 