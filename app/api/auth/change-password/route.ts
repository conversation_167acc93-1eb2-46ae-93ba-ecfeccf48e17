import { NextResponse } from "next/server";
import { updateMongoUserCredentials } from '@/lib/auth/mongo-auth-ops';
import { z } from "zod";
import { jwtDecode } from "jwt-decode";
import { compare } from "bcryptjs";
import { getMongoUserById } from "@/lib/auth/mongo-auth-ops";

// Define validation schema
const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(8, "New password must be at least 8 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number"),
});

// Extract user from request using token
async function getUserFromRequest(req: Request) {
  try {
    // Get authorization header
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    // Extract token
    const token = authHeader.substring(7);
    if (!token) {
      return null;
    }

    // Decode token
    const decoded = jwtDecode<{sub: string; name: string}>(token);
    if (!decoded || !decoded.sub) {
      return null;
    }

    return {
      id: decoded.sub,
      name: decoded.name
    };
  } catch (error) {
    console.error("Error extracting user from request:", error);
    return null;
  }
}

export async function POST(req: Request) {
  try {
    // Get the current user from request
    const user = await getUserFromRequest(req);
    
    if (!user || !user.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const userId = user.id;
    const body = await req.json();

    // Validate input using Zod
    const result = changePasswordSchema.safeParse(body);
    
    if (!result.success) {
      const errors = result.error.format();
      return NextResponse.json(
        { error: "Validation failed", details: errors },
        { status: 400 }
      );
    }

    const { currentPassword, newPassword } = result.data;

    // Check current password before updating
    const userDoc = await getMongoUserById(userId);
    if (!userDoc) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    const isValidPassword = await compare(currentPassword, userDoc.password);
    if (!isValidPassword) {
      return NextResponse.json(
        { error: "Current password is incorrect" },
        { status: 400 }
      );
    }
    // Change password in MongoDB
    const changeResult = await updateMongoUserCredentials(userId, { plaintextPassword: newPassword });

    if (!changeResult.success) {
      return NextResponse.json(
        { error: changeResult.error },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Password changed successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Password change error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 