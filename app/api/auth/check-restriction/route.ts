import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import {
  getMongoUserById,
  // getMongoOwnerForRestaurant,
} from '@/lib/auth/mongo-auth-ops'; // Adjusted path assuming @ is src or root
import { AuthUser } from '@/lib/db/v4/schemas/auth-schema';

interface RequestBody {
  userId: string;
  role: string;
  restaurantId?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as RequestBody;
    const { userId, role, restaurantId } = body;

    if (!userId || !role) {
      return NextResponse.json({ error: 'Missing userId or role' }, { status: 400 });
    }

    // Check if the current user is directly restricted
    const currentUser = await getMongoUserById(userId);
    if (currentUser && currentUser.restricted) {
      console.log(`[API CheckRestriction] Access denied: User ${userId} is restricted`);
      return NextResponse.json({ isRestricted: true });
    }

    // If user is not an owner and has a restaurantId, check if the restaurant owner is restricted
    if (role !== 'owner' && restaurantId) {
      // const owner = await getMongoOwnerForRestaurant(restaurantId);
      // TODO: Implement or re-export getMongoOwnerForRestaurant if needed
      console.log(`[API CheckRestriction] Access denied: Restaurant owner ${restaurantId} is restricted`);
      return NextResponse.json({ isRestricted: true });
    }

    return NextResponse.json({ isRestricted: false });

  } catch (error) {
    console.error('[API CheckRestriction] Error:', error);
    // In case of an unexpected error during the check, default to not restricted
    // or handle as a server error, depending on desired behavior.
    // For now, let's consider it a server error but imply not restricted to prevent blocking valid users due to internal issues.
    // A more robust solution might return a specific error status.
    return NextResponse.json({ error: 'Error checking restrictions', isRestricted: false }, { status: 500 }); 
    // Alternatively, to be safer and block access:
    // return NextResponse.json({ error: 'Error checking restrictions', isRestricted: true }, { status: 500 });
  }
} 