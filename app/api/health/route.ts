import { NextRequest, NextResponse } from 'next/server';
import { corsJsonResponse, addCorsHeaders, corsOptionsResponse } from '@/lib/utils/cors';

/**
 * API endpoint for system health check
 * This provides a simple health check for the application
 */

export async function GET(request: NextRequest) {
  return corsJsonResponse({
    status: 'ok',
    timestamp: Date.now(),
    message: 'System is running',
  });
}

export async function HEAD(request: NextRequest) {
  // Simple HEAD response for quick connectivity checks
  const res = new NextResponse(null, {
    status: 200,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    },
  });
  return addCorsHeaders(res);
}

export async function OPTIONS() {
  // Handle CORS preflight requests
  return corsOptionsResponse();
} 