import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';

// Pre-configured OAuth credentials for easy setup
// In production, these would be environment variables
const EASY_OAUTH_CONFIG = {
  clientId: process.env.GOOGLE_OAUTH_CLIENT_ID || '102537660956-857id6odqfnou4kl6o1cbtl6b4sqgalv.apps.googleusercontent.com',
  clientSecret: process.env.GOOGLE_OAUTH_CLIENT_SECRET || 'GOCSPX-example-secret-key',
  redirectUri: process.env.GOOGLE_OAUTH_REDIRECT_URI || `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/google-drive/oauth/callback`
};

export async function GET(request: NextRequest) {
  try {
    console.log('🔐 Starting Google OAuth authorization flow...');
    
    // Create OAuth2 client with pre-configured credentials
    const oauth2Client = new google.auth.OAuth2(
      EASY_OAUTH_CONFIG.clientId,
      EASY_OAUTH_CONFIG.clientSecret,
      EASY_OAUTH_CONFIG.redirectUri
    );

    // Generate the authorization URL
    const authUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline', // Required for refresh token
      prompt: 'consent', // Force consent screen to get refresh token
      scope: [
        'https://www.googleapis.com/auth/drive',
        'https://www.googleapis.com/auth/userinfo.profile',
        'https://www.googleapis.com/auth/userinfo.email'
      ],
      response_type: 'code'
    });

    console.log('✅ Authorization URL generated successfully');
    
    // Redirect to Google's OAuth consent screen
    return NextResponse.redirect(authUrl);
    
  } catch (error) {
    console.error('❌ OAuth authorization error:', error);
    
    // Return an HTML page with error message that can communicate with parent window
    const errorHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Google Drive Setup - Error</title>
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              display: flex;
              align-items: center;
              justify-content: center;
              min-height: 100vh;
              margin: 0;
              background: #f8fafc;
            }
            .error-container {
              text-align: center;
              padding: 2rem;
              background: white;
              border-radius: 8px;
              box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
              max-width: 400px;
            }
            .error-icon {
              font-size: 3rem;
              margin-bottom: 1rem;
            }
            .error-title {
              font-size: 1.25rem;
              font-weight: 600;
              color: #dc2626;
              margin-bottom: 0.5rem;
            }
            .error-message {
              color: #6b7280;
              margin-bottom: 1.5rem;
            }
            .close-button {
              background: #3b82f6;
              color: white;
              border: none;
              padding: 0.5rem 1rem;
              border-radius: 4px;
              cursor: pointer;
              font-size: 0.875rem;
            }
            .close-button:hover {
              background: #2563eb;
            }
          </style>
        </head>
        <body>
          <div class="error-container">
            <div class="error-icon">⚠️</div>
            <h1 class="error-title">Setup Failed</h1>
            <p class="error-message">
              Failed to start Google Drive authorization. Please try again or contact support.
            </p>
            <button class="close-button" onclick="closeWindow()">Close</button>
          </div>
          
          <script>
            function closeWindow() {
              if (window.opener) {
                window.opener.postMessage({
                  type: 'GOOGLE_OAUTH_ERROR',
                  error: 'Failed to start authorization flow'
                }, window.location.origin);
              }
              window.close();
            }
            
            // Auto-close after 10 seconds
            setTimeout(closeWindow, 10000);
          </script>
        </body>
      </html>
    `;
    
    return new NextResponse(errorHtml, {
      status: 500,
      headers: {
        'Content-Type': 'text/html',
      },
    });
  }
}