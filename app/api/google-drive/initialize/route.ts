import { NextRequest, NextResponse } from 'next/server';
import { GoogleDriveService } from '@/lib/services/google-drive-service';

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Google Drive initialize API called');

    const { restaurantId } = await request.json();

    if (!restaurantId) {
      return NextResponse.json(
        { success: false, error: 'Restaurant ID is required' },
        { status: 400 }
      );
    }

    // Initialize Google Drive service
    const googleDriveService = new GoogleDriveService();
    const initialized = await googleDriveService.initializeFromMongoDB(restaurantId);

    if (initialized) {
      console.log('✅ Google Drive service initialized successfully');
      return NextResponse.json({
        success: true,
        message: 'Google Drive service initialized successfully'
      });
    } else {
      console.log('📭 No Google Drive configuration found');
      return NextResponse.json(
        { success: false, error: 'No Google Drive configuration found for this restaurant' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('❌ Google Drive initialize API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Initialization failed' 
      },
      { status: 500 }
    );
  }
} 