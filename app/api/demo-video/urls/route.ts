import { NextRequest, NextResponse } from 'next/server';
import R2Service from '@/lib/services/r2-service';

/**
 * 🎬 Demo Video URLs API
 * 
 * Returns signed URLs for demo video files
 * Uses the same R2Service pattern as Windows releases
 */

export async function GET(request: NextRequest) {
  try {
    console.log('🎬 [Demo Video API] Getting video URLs');
    
    // Video file names
    const videoFiles = {
      mp4: 'demo-video.mp4',
      webm: 'demo-video.webm'
    };
    
    const urls: { mp4?: string; webm?: string } = {};
    
    // Check and get URL for MP4
    try {
      const mp4Exists = await R2Service.fileExists(videoFiles.mp4);
      if (mp4Exists) {
        urls.mp4 = await R2Service.getDownloadUrl(videoFiles.mp4, 3600); // 1 hour expiry
        console.log('✅ [Demo Video API] MP4 URL generated');
      } else {
        console.log('⚠️ [Demo Video API] MP4 file not found');
      }
    } catch (error) {
      console.error('❌ [Demo Video API] Failed to get MP4 URL:', error);
    }
    
    // Check and get URL for WebM
    try {
      const webmExists = await R2Service.fileExists(videoFiles.webm);
      if (webmExists) {
        urls.webm = await R2Service.getDownloadUrl(videoFiles.webm, 3600); // 1 hour expiry
        console.log('✅ [Demo Video API] WebM URL generated');
      } else {
        console.log('⚠️ [Demo Video API] WebM file not found');
      }
    } catch (error) {
      console.error('❌ [Demo Video API] Failed to get WebM URL:', error);
    }
    
    // Return error if no videos found
    if (!urls.mp4 && !urls.webm) {
      console.log('❌ [Demo Video API] No video files found');
      return NextResponse.json(
        { 
          success: false,
          error: 'No video files available',
          message: 'Demo video files not found in R2 storage' 
        },
        { status: 404 }
      );
    }
    
    console.log('✅ [Demo Video API] Video URLs ready');
    
    return NextResponse.json({
      success: true,
      urls,
      expiresIn: 3600, // 1 hour
      message: `Found ${Object.keys(urls).length} video format(s)`
    });
    
  } catch (error) {
    console.error('❌ [Demo Video API] Error getting video URLs:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'Failed to get demo video URLs' 
      },
      { status: 500 }
    );
  }
}