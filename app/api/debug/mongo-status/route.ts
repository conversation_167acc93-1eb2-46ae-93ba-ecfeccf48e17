import { NextResponse } from 'next/server';
import clientPromise, { isMongoDBReachable } from '@/lib/mongodb';
import { MongoClient } from 'mongodb';

export async function GET() {
  try {
    console.log('[API] MongoDB status check initiated');
    
    // First check if MongoDB is reachable
    const reachabilityCheck = await isMongoDBReachable();
    
    // If it's not reachable according to the quick check, return that result
    if (!reachabilityCheck.reachable) {
      console.log('[API] MongoDB quick check failed:', reachabilityCheck.error);
      return NextResponse.json({
        reachable: false,
        error: reachabilityCheck.error,
        details: null
      });
    }
    
    // If it passed the quick check, try a more comprehensive check
    try {
      const client = await clientPromise;
      
      // Get admin info
      let adminInfo: any = null;
      try {
        adminInfo = await client.db().admin().serverInfo();
      } catch (e: any) {
        console.warn('[API] Could not retrieve adminInfo, possibly due to APIStrictError:', e.message);
        adminInfo = { error: `Could not retrieve adminInfo: ${e.message}` };
      }
      
      // Get topology description - careful with this part as it's using internal APIs
      let topologyDescription = {};
      
      try {
        // Access the internal topology description for detailed information
        // Note: This is accessing internals of the MongoClient which may change between versions
        const topology = (client as any).topology;
        if (topology) {
          const description = topology.description;
          if (description) {
            const servers = [];
            if (description.servers && typeof description.servers.entries === 'function') {
              // Cast the entries to the expected type and convert to array
              for (const [key, server] of description.servers.entries()) {
                servers.push({
                  address: key,
                  type: server.type,
                  roundTripTime: server.roundTripTime,
                  lastUpdateTime: server.lastUpdateTime ? new Date(server.lastUpdateTime).toISOString() : null,
                  error: server.error ? server.error.message : null
                });
              }
            }
            
            topologyDescription = {
              type: description.type,
              setName: description.setName,
              maxSetVersion: description.maxSetVersion,
              maxElectionId: description.maxElectionId ? description.maxElectionId.toString() : null,
              servers
            };
          }
        }
      } catch (e) {
        console.error('[API] Error accessing topology description:', e);
        topologyDescription = { error: 'Could not access topology description' };
      }
      
      return NextResponse.json({
        reachable: true,
        details: {
          version: adminInfo?.version || (adminInfo?.error ? adminInfo.error : 'Unknown'),
          db: client.db().databaseName,
          server: adminInfo?.host || (adminInfo?.error ? 'N/A due to error' : 'Unknown'),
          topology: topologyDescription
        }
      });
    } catch (error) {
      console.error('[API] Error in detailed MongoDB check:', error);
      return NextResponse.json({
        reachable: false,
        error: error instanceof Error ? error.message : 'Unknown MongoDB connection error',
        details: null
      });
    }
  } catch (error) {
    console.error('[API] MongoDB status check error:', error);
    return NextResponse.json({
      reachable: false,
      error: error instanceof Error ? error.message : 'Unknown error checking MongoDB status',
      details: null
    });
  }
} 