import { NextRequest, NextResponse } from 'next/server';
import R2Service from '@/lib/services/r2-service';

/**
 * 📥 Get Latest Windows Release
 * 
 * Returns the download URL for the latest Windows .exe file
 * Used by the landing page download button
 */

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [Releases API] Getting latest Windows release');
    
    // Configuration - using the new clean filename format
    const releaseFileName = 'bistro.exe';
    
    // Check if the file exists
    const fileExists = await R2Service.fileExists(releaseFileName);
    
    if (!fileExists) {
      console.log('❌ [Releases API] No release file found');
      return NextResponse.json(
        { 
          error: 'No release available',
          message: 'Windows release file not found' 
        },
        { status: 404 }
      );
    }
    
    // Get file metadata
    const metadata = await R2Service.getFileMetadata(releaseFileName);
    
    // Generate download URL (valid for 1 hour)
    const downloadUrl = await R2Service.getDownloadUrl(releaseFileName, 3600);
    
    console.log('✅ [Releases API] Latest release found');
    
    return NextResponse.json({
      success: true,
      downloadUrl,
      fileName: releaseFileName,
      size: metadata.size,
      lastModified: metadata.lastModified,
      version: 'latest' // You can extract this from filename later
    });
    
  } catch (error) {
    console.error('❌ [Releases API] Error getting latest release:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to get latest release' 
      },
      { status: 500 }
    );
  }
}