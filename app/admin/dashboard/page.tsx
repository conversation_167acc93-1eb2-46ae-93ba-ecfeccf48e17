"use client";

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { useRouter } from 'next/navigation';
import { useStaticNavigation } from '@/lib/utils/navigation';
import { Shield, Users, Lock, Settings, AlertCircle } from 'lucide-react';

export default function AdminDashboard() {
  const [loading, setLoading] = useState(true);
  const [adminEmail, setAdminEmail] = useState<string | null>(null);
  const router = useRouter();
  const { navigate } = useStaticNavigation();

  useEffect(() => {
    // Vérifier si l'utilisateur est admin en comparant avec la variable d'environnement ADMIN_EMAIL
    const checkAdminStatus = async () => {
      try {
        const response = await fetch('/api/admin/check-admin');
        const data = await response.json();
        
        if (data.success) {
          setAdminEmail(data.email);
          setLoading(false);
        } else {
          // Pas admin, rediriger vers l'accueil
          navigate('');
        }
      } catch (error) {
        console.error('Erreur lors de la vérification du statut admin :', error);
        // Erreur, rediriger vers l'accueil
        navigate('');
      }
    };
    
    checkAdminStatus();
  }, [navigate]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <p>Chargement du tableau de bord administrateur...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="flex flex-col space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold">Tableau de bord administrateur</h1>
          <p className="text-muted-foreground">
            Bienvenue dans le panneau de contrôle administrateur. Gérez votre plateforme ici.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-500" /> 
                Propriétaires de restaurants
              </CardTitle>
              <CardDescription>
                Gérer l'accès à l'abonnement pour les propriétaires de restaurants
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Autorisez ou restreignez l'accès à la plateforme pour les propriétaires de restaurants. Restreindre un propriétaire restreint également tout son personnel.
              </p>
            </CardContent>
            <CardFooter>
              <Button variant="default" className="w-full" 
                onClick={() => navigate('admin/owners')}>
                Gérer les propriétaires
              </Button>
            </CardFooter>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5 text-amber-500" /> 
                Contrôle d'accès
              </CardTitle>
              <CardDescription>
                Voir et gérer les contrôles d'accès à l'échelle de la plateforme
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Vérifiez l'état d'accès actuel, gérez les restrictions IP et consultez les journaux d'accès.
              </p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" disabled>
                Bientôt disponible
              </Button>
            </CardFooter>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-green-500" /> 
                Paramètres du système
              </CardTitle>
              <CardDescription>
                Configurer les paramètres globaux de la plateforme
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Gérez les modèles d'e-mails, les paramètres de notification et d'autres configurations globales du système.
              </p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" disabled>
                Bientôt disponible
              </Button>
            </CardFooter>
          </Card>
        </div>

        <Card className="mt-6 border-amber-200 bg-amber-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-amber-800">
              <AlertCircle className="h-5 w-5" /> 
              Informations administrateur
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-amber-800">
              Vous êtes connecté en tant qu'administrateur avec l'e-mail : <strong>{adminEmail}</strong>
            </p>
            <p className="text-sm text-amber-800 mt-2">
              Toute modification effectuée ici affectera tous les utilisateurs de la plateforme. Veuillez procéder avec prudence.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 