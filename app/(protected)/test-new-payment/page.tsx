'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Plus, DollarSign, TrendingUp, TrendingDown, CreditCard } from 'lucide-react';

// Import NEW payment system for testing
import {
  addAdvance,
  addDeduction,
  addBonus,
  getAllBalances,
  getDetailedBalances,
  addMultipleBalances,
  updateBalance,
  deleteBalance,
  createPaymentSnapshot,
  createPaymentSnapshotWithStrategy,
  previewPayment,
  getPaymentHistory,
  getPaymentAnalytics,
  calculatePaymentAmounts
} from '@/lib/services/new-staff-balance-service';

import type { PaymentStrategy } from '@/lib/services/new-staff-balance-service';
import NewPaymentHistory from '@/components/payment/NewPaymentHistory';

import {
  initializeV4Database,
  createNewPaymentIndexes
} from '@/lib/db/v4';

import { getAllStaff } from '@/lib/db/v4';
import { getCurrentRestaurantId } from '@/lib/db/v4/utils/restaurant-id';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import type { StaffDocument } from '@/lib/db/v4/schemas/per-staff-schemas';
import type { BalanceSummary, PaymentSnapshotDocument } from '@/lib/db/v4/schemas/new-payment-schemas';

export default function TestNewPaymentPage() {
  const { toast } = useToast();
  const { isAuthenticated, user } = useAuth();
  const [staff, setStaff] = useState<StaffDocument[]>([]);
  const [selectedStaffId, setSelectedStaffId] = useState<string>('');
  const [balances, setBalances] = useState<BalanceSummary | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<PaymentSnapshotDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [dbInitialized, setDbInitialized] = useState(false);
  const [initializingDb, setInitializingDb] = useState(false);
  const [indexesCreated, setIndexesCreated] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);
  const [runningTests, setRunningTests] = useState(false);
  const [paymentStrategy, setPaymentStrategy] = useState<PaymentStrategy>('USE_ALL_BALANCES');
  const [paymentPreview, setPaymentPreview] = useState<any>(null);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [analytics, setAnalytics] = useState<any>(null);

  // Form states
  const [advanceAmount, setAdvanceAmount] = useState('');
  const [advanceReason, setAdvanceReason] = useState('');
  const [deductionAmount, setDeductionAmount] = useState('');
  const [deductionReason, setDeductionReason] = useState('');
  const [bonusAmount, setBonusAmount] = useState('');
  const [bonusReason, setBonusReason] = useState('');
  const [baseSalary, setBaseSalary] = useState('');

  useEffect(() => {
    if (isAuthenticated && user) {
      initializeDatabase();
    }
  }, [isAuthenticated, user]);

  useEffect(() => {
    if (selectedStaffId && dbInitialized) {
      loadBalances();
      loadPaymentHistory();
    }
  }, [selectedStaffId, dbInitialized]);

  const initializeDatabase = async () => {
    if (initializingDb) return;

    setInitializingDb(true);
    try {
      // Get restaurant ID from auth
      const restaurantId = user?.restaurantId || getCurrentRestaurantId();

      if (!restaurantId) {
        throw new Error('No restaurant ID found in authentication data');
      }

      console.log('🔧 Initializing database with restaurant ID:', restaurantId);

      // Initialize the V4 database
      await initializeV4Database(restaurantId);

      // Add a small delay to ensure database is fully ready
      await new Promise(resolve => setTimeout(resolve, 100));

      setDbInitialized(true);

      // Create NEW payment system indexes
      await createNewPaymentIndexes();
      setIndexesCreated(true);

      // Load staff after database is ready
      await loadStaff();

      toast({
        title: "✅ Database Initialized",
        description: "NEW payment system ready for testing",
      });
    } catch (error) {
      console.error('Error initializing database:', error);
      toast({
        title: "❌ Database Initialization Failed",
        description: `Failed to initialize database: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setInitializingDb(false);
    }
  };

  const loadStaff = async () => {
    try {
      console.log('🔄 Loading staff members...');
      const staffList = await getAllStaff();
      console.log('📋 Staff loaded:', staffList.length, 'members');
      setStaff(staffList);
      if (staffList.length > 0) {
        setSelectedStaffId(staffList[0].id);
        console.log('✅ Selected first staff member:', staffList[0].name);
      } else {
        console.warn('⚠️ No staff members found in database');
        toast({
          title: "⚠️ No Staff Found",
          description: "No staff members found. Please add staff members first.",
          variant: "default",
        });
      }
    } catch (error) {
      console.error('❌ Error loading staff:', error);
      toast({
        title: "❌ Error Loading Staff",
        description: `Failed to load staff list: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    }
  };

  const loadBalances = async () => {
    if (!selectedStaffId) return;
    try {
      const balanceData = await getAllBalances(selectedStaffId);
      setBalances(balanceData);
    } catch (error) {
      console.error('Error loading balances:', error);
      // The service function now handles database waiting, so this shouldn't happen
      toast({
        title: "❌ Error Loading Balances",
        description: "Failed to load balance data",
        variant: "destructive",
      });
    }
  };

  const loadPaymentHistory = async () => {
    if (!selectedStaffId) return;
    try {
      const history = await getPaymentHistory(selectedStaffId);
      setPaymentHistory(history);
    } catch (error) {
      console.error('Error loading payment history:', error);
      // The service function now handles database waiting, so this shouldn't happen
      toast({
        title: "❌ Error Loading History",
        description: "Failed to load payment history",
        variant: "destructive",
      });
    }
  };

  const handleAddAdvance = async () => {
    if (!selectedStaffId || !advanceAmount || !advanceReason || !dbInitialized) return;
    setLoading(true);
    try {
      await addAdvance({
        staffId: selectedStaffId,
        amount: parseFloat(advanceAmount),
        reason: advanceReason
      });
      setAdvanceAmount('');
      setAdvanceReason('');
      // Add delay before reloading balances
      setTimeout(() => {
        loadBalances();
      }, 200);
      toast({
        title: "✅ Advance Added",
        description: `Added ${advanceAmount} DA advance`,
      });
    } catch (error) {
      console.error('Error adding advance:', error);
      if (error instanceof Error && error.message.includes('Database not initialized')) {
        toast({
          title: "⏳ Database Not Ready",
          description: "Please wait for database initialization to complete",
          variant: "destructive",
        });
      } else {
        toast({
          title: "❌ Error Adding Advance",
          description: `Failed to add advance: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleAddDeduction = async () => {
    if (!selectedStaffId || !deductionAmount || !deductionReason || !dbInitialized) return;
    setLoading(true);
    try {
      await addDeduction({
        staffId: selectedStaffId,
        amount: parseFloat(deductionAmount),
        reason: deductionReason
      });
      setDeductionAmount('');
      setDeductionReason('');
      await loadBalances();
      toast({
        title: "✅ Deduction Added",
        description: `Added ${deductionAmount} DA deduction`,
      });
    } catch (error) {
      console.error('Error adding deduction:', error);
      toast({
        title: "❌ Error Adding Deduction",
        description: "Failed to add deduction",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddBonus = async () => {
    if (!selectedStaffId || !bonusAmount || !bonusReason || !dbInitialized) return;
    setLoading(true);
    try {
      await addBonus({
        staffId: selectedStaffId,
        amount: parseFloat(bonusAmount),
        reason: bonusReason
      });
      setBonusAmount('');
      setBonusReason('');
      await loadBalances();
      toast({
        title: "✅ Bonus Added",
        description: `Added ${bonusAmount} DA bonus`,
      });
    } catch (error) {
      console.error('Error adding bonus:', error);
      toast({
        title: "❌ Error Adding Bonus",
        description: "Failed to add bonus",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePreviewPayment = async () => {
    if (!selectedStaffId || !baseSalary || !dbInitialized) return;
    setLoading(true);
    try {
      const preview = await previewPayment(
        selectedStaffId,
        parseFloat(baseSalary),
        paymentStrategy
      );
      setPaymentPreview(preview);
      toast({
        title: "✅ Payment Preview Generated",
        description: `Net amount: ${preview.calculation.netAmount.toLocaleString()} DA`,
      });
    } catch (error) {
      console.error('Error previewing payment:', error);
      toast({
        title: "❌ Error Previewing Payment",
        description: "Failed to generate payment preview",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePayment = async () => {
    if (!selectedStaffId || !baseSalary || !dbInitialized) return;
    setLoading(true);
    try {
      await createPaymentSnapshotWithStrategy({
        staffId: selectedStaffId,
        baseSalary: parseFloat(baseSalary),
        strategy: paymentStrategy,
        notes: `Payment using ${paymentStrategy} strategy`
      });
      setBaseSalary('');
      setPaymentPreview(null);
      await loadBalances();
      await loadPaymentHistory();
      toast({
        title: "✅ Payment Created",
        description: `Created payment snapshot using ${paymentStrategy} strategy`,
      });
    } catch (error) {
      console.error('Error creating payment:', error);
      toast({
        title: "❌ Error Creating Payment",
        description: "Failed to create payment snapshot",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadAnalytics = async () => {
    if (!selectedStaffId || !dbInitialized) return;
    try {
      const analyticsData = await getPaymentAnalytics(selectedStaffId);
      setAnalytics(analyticsData);
      setShowAnalytics(true);
    } catch (error) {
      console.error('Error loading analytics:', error);
      toast({
        title: "❌ Error Loading Analytics",
        description: "Failed to load payment analytics",
        variant: "destructive",
      });
    }
  };

  // ===== COMPREHENSIVE TESTING FUNCTIONS =====

  const runComprehensiveTests = async () => {
    if (!selectedStaffId || !dbInitialized) return;

    setRunningTests(true);
    setTestResults([]);
    const results: string[] = [];

    try {
      results.push("🧪 Starting comprehensive NEW payment system tests...");

      results.push("📝 Test 1: Adding multiple balance types...");
      await addAdvance({ staffId: selectedStaffId, amount: 100, reason: "Test advance" });
      await addDeduction({ staffId: selectedStaffId, amount: 50, reason: "Test deduction" });
      await addBonus({ staffId: selectedStaffId, amount: 75, reason: "Test bonus" });
      results.push("✅ Test 1: Successfully added advance, deduction, and bonus");

      // Test 2: Verify balance calculations
      results.push("📝 Test 2: Verifying balance calculations...");
      const balances = await getAllBalances(selectedStaffId);
      const expectedTotal = 75 - 50 - 100; // bonus - deduction - advance = -75
      if (balances.total === expectedTotal) {
        results.push(`✅ Test 2: Balance calculation correct (${balances.total} DA)`);
      } else {
        results.push(`❌ Test 2: Balance calculation incorrect. Expected ${expectedTotal}, got ${balances.total}`);
      }

      // Test 3: Test bulk operations
      results.push("📝 Test 3: Testing bulk balance addition...");
      await addMultipleBalances(selectedStaffId, [
        { balanceType: 'BONUS', amount: 25, reason: "Bulk bonus 1" },
        { balanceType: 'BONUS', amount: 25, reason: "Bulk bonus 2" },
        { balanceType: 'DEDUCTION', amount: 10, reason: "Bulk deduction" }
      ]);
      results.push("✅ Test 3: Successfully added multiple balances");

      // Test 4: Test detailed balance retrieval
      results.push("📝 Test 4: Testing detailed balance retrieval...");
      const detailed = await getDetailedBalances(selectedStaffId);
      results.push(`✅ Test 4: Retrieved ${detailed.entries.advances.length} advances, ${detailed.entries.deductions.length} deductions, ${detailed.entries.bonuses.length} bonuses`);

      // Test 5: Test payment snapshot creation
      results.push("📝 Test 5: Testing payment snapshot creation...");
      const snapshot = await createPaymentSnapshot({
        staffId: selectedStaffId,
        baseSalary: 2000,
        useAllBonuses: true,
        useAllDeductions: true,
        useAllAdvances: true,
        notes: "Comprehensive test payment"
      });
      results.push(`✅ Test 5: Created payment snapshot with net amount ${snapshot.netAmount} DA`);

      // Test 6: Verify balances are marked as used
      results.push("📝 Test 6: Verifying balances marked as used...");
      const balancesAfterPayment = await getAllBalances(selectedStaffId);
      if (balancesAfterPayment.total === 0) {
        results.push("✅ Test 6: All balances correctly marked as used");
      } else {
        results.push(`❌ Test 6: Some balances not marked as used (remaining: ${balancesAfterPayment.total} DA)`);
      }

      // Test 7: Test payment history
      results.push("📝 Test 7: Testing payment history retrieval...");
      const history = await getPaymentHistory(selectedStaffId);
      if (history.length > 0) {
        results.push(`✅ Test 7: Retrieved ${history.length} payment snapshots`);
      } else {
        results.push("❌ Test 7: No payment history found");
      }

      // Test 8: Test validation (error cases)
      results.push("📝 Test 8: Testing validation and error handling...");
      try {
        await addAdvance({ staffId: selectedStaffId, amount: -100, reason: "Invalid amount" });
        results.push("❌ Test 8: Validation failed - negative amount was accepted");
      } catch (error) {
        results.push("✅ Test 8: Validation working - negative amount rejected");
      }

      try {
        await addBonus({ staffId: selectedStaffId, amount: 100, reason: "" });
        results.push("❌ Test 8: Validation failed - empty reason was accepted");
      } catch (error) {
        results.push("✅ Test 8: Validation working - empty reason rejected");
      }

      results.push("🎉 All comprehensive tests completed!");

      // Refresh data
      await loadBalances();
      await loadPaymentHistory();

    } catch (error) {
      results.push(`❌ Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setTestResults(results);
      setRunningTests(false);
    }
  };

  const selectedStaff = staff.find(s => s.id === selectedStaffId);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">🧪 Test NEW Payment System</h1>
          <p className="text-muted-foreground">Test the NEW separate balance payment system</p>
        </div>
        <div className="flex gap-2">
          <Badge variant={dbInitialized ? "default" : "secondary"}>
            {dbInitialized ? "✅ Database Ready" : initializingDb ? "⏳ Initializing..." : "❌ Not Initialized"}
          </Badge>
          <Badge variant={indexesCreated ? "default" : "secondary"}>
            {indexesCreated ? "✅ Indexes Ready" : "⏳ Pending..."}
          </Badge>
        </div>
      </div>

      {/* Staff Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>👤 Select Staff Member</span>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={loadStaff}
              disabled={loading}
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : "🔄 Reload Staff"}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <select 
            value={selectedStaffId} 
            onChange={(e) => setSelectedStaffId(e.target.value)}
            className="w-full p-2 border rounded"
          >
            <option value="">Select a staff member...</option>
            {staff.map(s => (
              <option key={s.id} value={s.id}>{s.name} - {s.role}</option>
            ))}
          </select>
          
          <div className="mt-2 text-sm text-muted-foreground">
            {staff.length === 0 ? (
              <span className="text-orange-600">⚠️ No staff members loaded. Click "Reload Staff" or check console for errors.</span>
            ) : (
              <span>✅ {staff.length} staff member(s) available</span>
            )}
          </div>
          
          {selectedStaff && (
            <div className="mt-2 text-sm text-muted-foreground">
              Selected: {selectedStaff.name} ({selectedStaff.role}) - Base Salary: {selectedStaff.paymentConfig.baseSalary} DA
            </div>
          )}
        </CardContent>
      </Card>

      {!isAuthenticated && (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">Please log in to test the new payment system</p>
          </CardContent>
        </Card>
      )}

      {isAuthenticated && !dbInitialized && (
        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center gap-2">
              {initializingDb && <Loader2 className="h-4 w-4 animate-spin" />}
              <p className="text-muted-foreground">
                {initializingDb ? "Initializing database..." : "Database not initialized"}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {isAuthenticated && dbInitialized && selectedStaffId && (
        <>
          {/* Current Balances */}
          <Card>
            <CardHeader>
              <CardTitle>💰 Current Balances (NEW System)</CardTitle>
            </CardHeader>
            <CardContent>
              {balances ? (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">+{balances.bonuses}</div>
                    <div className="text-sm text-muted-foreground">Bonuses</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">-{balances.deductions}</div>
                    <div className="text-sm text-muted-foreground">Deductions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">-{balances.advances}</div>
                    <div className="text-sm text-muted-foreground">Advances</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{balances.total}</div>
                    <div className="text-sm text-muted-foreground">Net Total</div>
                  </div>
                </div>
              ) : (
                <div className="text-center text-muted-foreground">Loading balances...</div>
              )}
            </CardContent>
          </Card>

          {/* Comprehensive Testing Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🧪 Comprehensive Testing
              </CardTitle>
              <CardDescription>
                Run automated tests to verify all NEW payment system functionality
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={runComprehensiveTests}
                disabled={runningTests || !selectedStaffId}
                className="w-full"
                size="lg"
                variant="outline"
              >
                {runningTests ? <Loader2 className="h-4 w-4 animate-spin" /> : "🚀"}
                {runningTests ? "Running Tests..." : "Run Comprehensive Tests"}
              </Button>

              {testResults.length > 0 && (
                <div className="border rounded p-4 bg-muted/50 max-h-60 overflow-y-auto">
                  <div className="text-sm font-medium mb-2">Test Results:</div>
                  <div className="space-y-1 text-sm font-mono">
                    {testResults.map((result, index) => (
                      <div
                        key={index}
                        className={`${
                          result.includes('✅') ? 'text-green-600' :
                          result.includes('❌') ? 'text-red-600' :
                          result.includes('🧪') || result.includes('🎉') ? 'text-blue-600 font-bold' :
                          'text-muted-foreground'
                        }`}
                      >
                        {result}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Add Balance Forms */}
          <div className="grid md:grid-cols-3 gap-6">
            {/* Add Advance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingDown className="h-5 w-5 text-orange-600" />
                  Add Advance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  type="number"
                  placeholder="Amount (DA)"
                  value={advanceAmount}
                  onChange={(e) => setAdvanceAmount(e.target.value)}
                />
                <Textarea
                  placeholder="Reason for advance"
                  value={advanceReason}
                  onChange={(e) => setAdvanceReason(e.target.value)}
                />
                <Button 
                  onClick={handleAddAdvance} 
                  disabled={loading || !advanceAmount || !advanceReason}
                  className="w-full"
                >
                  {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
                  Add Advance
                </Button>
              </CardContent>
            </Card>

            {/* Add Deduction */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingDown className="h-5 w-5 text-red-600" />
                  Add Deduction
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  type="number"
                  placeholder="Amount (DA)"
                  value={deductionAmount}
                  onChange={(e) => setDeductionAmount(e.target.value)}
                />
                <Textarea
                  placeholder="Reason for deduction"
                  value={deductionReason}
                  onChange={(e) => setDeductionReason(e.target.value)}
                />
                <Button 
                  onClick={handleAddDeduction} 
                  disabled={loading || !deductionAmount || !deductionReason}
                  className="w-full"
                >
                  {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
                  Add Deduction
                </Button>
              </CardContent>
            </Card>

            {/* Add Bonus */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  Add Bonus
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  type="number"
                  placeholder="Amount (DA)"
                  value={bonusAmount}
                  onChange={(e) => setBonusAmount(e.target.value)}
                />
                <Textarea
                  placeholder="Reason for bonus"
                  value={bonusReason}
                  onChange={(e) => setBonusReason(e.target.value)}
                />
                <Button 
                  onClick={handleAddBonus} 
                  disabled={loading || !bonusAmount || !bonusReason}
                  className="w-full"
                >
                  {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
                  Add Bonus
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Payment Creation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Enhanced Payment System
              </CardTitle>
              <CardDescription>
                Create payment snapshots with advanced strategies and preview functionality
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                type="number"
                placeholder="Base Salary Amount (DA)"
                value={baseSalary}
                onChange={(e) => setBaseSalary(e.target.value)}
              />

              <div>
                <label className="text-sm font-medium">Payment Strategy:</label>
                <select
                  value={paymentStrategy}
                  onChange={(e) => setPaymentStrategy(e.target.value as PaymentStrategy)}
                  className="w-full p-2 border rounded mt-1"
                >
                  <option value="USE_ALL_BALANCES">Use All Balances</option>
                  <option value="USE_OLDEST_FIRST">Use Oldest First (FIFO)</option>
                  <option value="USE_NEWEST_FIRST">Use Newest First (LIFO)</option>
                  <option value="MAXIMIZE_NET_PAYMENT">Maximize Net Payment</option>
                  <option value="MINIMIZE_DEDUCTIONS">Minimize Deductions</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <Button
                  onClick={handlePreviewPayment}
                  disabled={loading || !baseSalary}
                  variant="outline"
                >
                  {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : "👁️"}
                  Preview Payment
                </Button>
                <Button
                  onClick={handleCreatePayment}
                  disabled={loading || !baseSalary}
                >
                  {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <DollarSign className="h-4 w-4" />}
                  Create Payment
                </Button>
              </div>

              {paymentPreview && (
                <div className="border rounded p-4 bg-blue-50">
                  <div className="text-sm font-medium mb-2">Payment Preview ({paymentPreview.strategy}):</div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>Base: {paymentPreview.calculation.baseSalary.toLocaleString()} DA</div>
                    <div className="text-green-600">Bonus: +{paymentPreview.calculation.bonusAmount.toLocaleString()} DA</div>
                    <div className="text-red-600">Deduction: -{paymentPreview.calculation.deductionAmount.toLocaleString()} DA</div>
                    <div className="text-orange-600">Advance: -{paymentPreview.calculation.advanceAmount.toLocaleString()} DA</div>
                  </div>
                  <div className="mt-2 pt-2 border-t">
                    <div className="font-bold">Net Amount: {paymentPreview.calculation.netAmount.toLocaleString()} DA</div>
                    <div className="text-xs text-muted-foreground">
                      Will use {paymentPreview.willUseBalanceIds.length} balance entries
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Payment Analytics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                📊 Payment Analytics
              </CardTitle>
              <CardDescription>
                View detailed payment analytics and history
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={loadAnalytics}
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : "📈"}
                Load Payment Analytics
              </Button>

              {analytics && showAnalytics && (
                <div className="border rounded p-4 bg-green-50">
                  <div className="text-sm font-medium mb-2">Payment Analytics Summary:</div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>Total Payments: {analytics.totalPayments}</div>
                    <div>Total Net Paid: {analytics.totalNetPaid.toLocaleString()} DA</div>
                    <div>Average Payment: {analytics.averageNetPayment.toLocaleString()} DA</div>
                    <div>Payment Frequency: {analytics.paymentFrequency.toFixed(1)}/month</div>
                    <div>Total Bonuses: {analytics.totalBonuses.toLocaleString()} DA</div>
                    <div>Total Deductions: {analytics.totalDeductions.toLocaleString()} DA</div>
                  </div>
                  {analytics.largestPayment && (
                    <div className="mt-2 pt-2 border-t text-xs">
                      <div>Largest Payment: {analytics.largestPayment.netAmount.toLocaleString()} DA</div>
                      <div>Smallest Payment: {analytics.smallestPayment.netAmount.toLocaleString()} DA</div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* NEW Payment History Component */}
          <Card>
            <CardHeader>
              <CardTitle>📊 NEW Payment History Component</CardTitle>
              <CardDescription>
                Production-ready payment history component with filtering, analytics, and export
              </CardDescription>
            </CardHeader>
            <CardContent>
              <NewPaymentHistory
                staffId={selectedStaffId}
                showAnalytics={true}
                maxHeight="500px"
              />
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
