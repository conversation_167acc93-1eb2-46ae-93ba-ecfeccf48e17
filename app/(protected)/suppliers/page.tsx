"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { useSuppliersV4 } from "@/lib/hooks/useSuppliersV4";
import { useStockV4 } from '@/lib/hooks/useStockV4';
import { Supplier } from "@/types/suppliers";
import { StockItem } from "@/types/stock";
import { useToast } from "@/components/ui/use-toast";
import { BatchedPurchaseHistory } from "@/components/stock/BatchedPurchaseHistory";
import { databaseV4 } from "@/lib/db/v4/core/db-instance";
import { SimpleSupplierForm } from '@/components/suppliers/SimpleSupplierForm';

// UI Components
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

// Icons
import {
  Plus,
  Search,
  Edit,
  Trash2,
  RefreshCw,
  Eye,
  CheckCircle2,
  XCircle,
  Coins,
  CreditCard,
  ShoppingCart,
  Database,
  Phone,
  ArrowLeft,
  Filter,
  X
} from "lucide-react";

// Schema for simplified supplier validation
const supplierSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  phoneNumber: z.string().min(8, { message: "Phone number must be at least 8 characters." }),
  items: z.array(z.string()).optional(),
  category: z.string().optional(),
  notes: z.string().optional(),
  balance: z.coerce.number().default(0),
  isActive: z.boolean().default(true),
});

// Supplier categories
const supplierCategories = [
  "Ingredients",
  "Beverages", 
  "Packaging",
  "Cleaning Supplies",
  "Office Supplies",
  "Other"
] as const;

export default function SuppliersPage() {
  const { isAuthenticated, user, loading } = useAuth();
  const { toast } = useToast();
  const {
    suppliers,
    isLoading: isLoadingSuppliers,
    error: suppliersError,
    isReady: isSuppliersReady,
    refreshSuppliers,
    createSupplier,
    updateSupplier,
    deleteSupplier
  } = useSuppliersV4();

  const {
    stockItems,
    purchases,
    isLoading: isLoadingStock,
    isReady: isStockReady,
  } = useStockV4();

  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [editingSupplier, setEditingSupplier] = useState<Supplier | null>(null);
  const [isSupplierDialogOpen, setIsSupplierDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [supplierToDelete, setSupplierToDelete] = useState<Supplier | null>(null);
  const [viewingSupplier, setViewingSupplier] = useState<Supplier | null>(null);
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [suggestedPaymentAmount, setSuggestedPaymentAmount] = useState<number | null>(null);
  const [isSeeding, setIsSeeding] = useState(false);
  const [showFilters, setShowFilters] = useState(true);

  // Payment form state
  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [paymentNotes, setPaymentNotes] = useState<string>("");
  const [isSubmittingPayment, setIsSubmittingPayment] = useState<boolean>(false);

  // Transaction state
  const [transactionSearchTerm, setTransactionSearchTerm] = useState("");

  // Get purchases for current supplier
  const supplierPurchases = viewingSupplier 
    ? purchases.filter(purchase => purchase.supplierId === viewingSupplier.id)
    : [];

  // Filter suppliers
  const filteredSuppliers = suppliers.filter(supplier => {
    const matchesSearch =
      supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.phoneNumber.includes(searchTerm);

    const matchesCategory = selectedCategory
      ? selectedCategory === "none"
        ? !supplier.category
        : supplier.category === selectedCategory
      : true;

    const matchesStatus = statusFilter
      ? (statusFilter === 'active' ? supplier.isActive : !supplier.isActive)
      : true;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  // Helper functions
  const getStockItemsByCategory = (category: string) => {
    return stockItems.filter(item => item.category === category);
  };

  const handleSupplierSubmit = async (data: any) => {
    try {
      if (editingSupplier) {
        await updateSupplier(editingSupplier.id, data);
        toast({
          title: "Fournisseur mis à jour",
          description: "Les informations du fournisseur ont été mises à jour avec succès.",
        });
      } else {
        await createSupplier(data);
        toast({
          title: "Fournisseur ajouté",
          description: "Le nouveau fournisseur a été ajouté avec succès.",
        });
      }
      setIsSupplierDialogOpen(false);
      setEditingSupplier(null);
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Une erreur s'est produite lors de l'enregistrement du fournisseur.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteSupplier = async () => {
    if (!supplierToDelete) return;

    try {
      await deleteSupplier(supplierToDelete.id);
      toast({
        title: "Fournisseur supprimé",
        description: "Le fournisseur a été supprimé avec succès.",
      });
      setIsDeleteDialogOpen(false);
      setSupplierToDelete(null);
      if (viewingSupplier?.id === supplierToDelete.id) {
        setViewingSupplier(null);
      }
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de supprimer le fournisseur.",
        variant: "destructive",
      });
    }
  };

  const handlePaymentToSupplier = async (data: any) => {
    if (!viewingSupplier || !data.amount || data.amount <= 0) return;

    try {
      // TODO: Implement payment recording without PurchaseTransaction
      // For now, just update supplier balance directly
      await updateSupplier(viewingSupplier.id, {
        balance: viewingSupplier.balance - data.amount
      });

      toast({
        title: "Paiement enregistré",
        description: `Paiement de ${data.amount} DA enregistré avec succès.`,
      });

      await refreshSuppliers();
    } catch (error) {
      toast({
        title: "Erreur de paiement",
        description: "Impossible d'enregistrer le paiement.",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd MMM yyyy', { locale: fr });
  };

  // Calculate payment summary for a supplier
  const calculatePaymentSummary = (supplier: Supplier | null) => {
    if (!supplier) return { totalDue: 0, totalPaid: 0 };

    // For now, just return the supplier balance
    // TODO: Calculate from actual purchase and payment records
    return { 
      totalDue: Math.max(supplier.balance, 0), 
      totalPaid: 0 
    };
  };

  const openEditDialog = (supplier: Supplier) => {
    setEditingSupplier(supplier);
    setIsSupplierDialogOpen(true);
  };

  const openDeleteDialog = (supplier: Supplier) => {
    setSupplierToDelete(supplier);
    setIsDeleteDialogOpen(true);
  };

  const openSupplierView = (supplier: Supplier) => {
    setViewingSupplier(supplier);
  };

  // Calculate total debt
  const totalDebt = suppliers.reduce((sum, s) => s.balance > 0 ? sum + s.balance : sum, 0);

  if (loading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Chargement...</p>
        </div>
      </div>
    );
  }

  if (suppliersError) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <p className="text-sm text-destructive">Erreur de chargement des fournisseurs</p>
          <Button onClick={() => window.location.reload()} className="mt-2">
            Réessayer
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Compact Header with Integrated Filters */}
      <div className="sticky top-0 z-50 bg-background border-b">
        {/* Mobile Header */}
        <div className="lg:hidden">
          {/* Top Row: Title, Stats, Actions */}
          <div className="flex items-center justify-between px-2 py-1.5">
            <div className="flex items-center space-x-1.5">
              <h1 className="text-base font-semibold">Fournisseurs</h1>
              <Badge variant="secondary" className="text-xs h-4 px-1">
                {filteredSuppliers.length}
              </Badge>
              {totalDebt > 0 && (
                <Badge variant="destructive" className="text-xs h-4 px-1">
                  {totalDebt.toFixed(0)} DA
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="h-6 w-6 p-0"
              >
                <Filter className="h-3 w-3" />
              </Button>
              <Button
                onClick={() => setIsSupplierDialogOpen(true)}
                className="h-6 px-2 text-xs"
              >
                <Plus className="h-3 w-3 mr-1" />
                Ajouter
              </Button>
            </div>
          </div>

          {/* Mobile Filters (when expanded) */}
          {showFilters && (
            <div className="border-t bg-muted/20 px-2 py-1.5">
              <div className="space-y-1.5">
                <div className="relative">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
                  <Input
                    placeholder="Rechercher..."
                    className="pl-7 h-6 text-xs"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="flex space-x-1.5">
                  <Select
                    value={selectedCategory || "all"}
                    onValueChange={(value) => setSelectedCategory(value === "all" ? null : value)}
                  >
                    <SelectTrigger className="h-6 flex-1 text-xs">
                      <SelectValue placeholder="Catégorie" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Toutes</SelectItem>
                      <SelectItem value="none">Aucune</SelectItem>
                      {supplierCategories.map(category => (
                        <SelectItem key={category} value={category}>{category}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select
                    value={statusFilter || "all"}
                    onValueChange={(value) => setStatusFilter(value === "all" ? null : value)}
                  >
                    <SelectTrigger className="h-6 w-18 text-xs">
                      <SelectValue placeholder="Statut" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tous</SelectItem>
                      <SelectItem value="active">Actif</SelectItem>
                      <SelectItem value="inactive">Inactif</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" onClick={refreshSuppliers} className="h-6 w-6 p-0">
                    <RefreshCw className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Desktop Header */}
        <div className="hidden lg:flex items-center justify-center px-3 py-2">
          <div className="flex items-center space-x-3 max-w-6xl w-full">
            {/* Left: Title and Stats */}
            <div className="flex items-center space-x-2">
              <h1 className="text-lg font-semibold">Fournisseurs</h1>
              <Badge variant="secondary" className="text-sm h-5 px-2">
                {filteredSuppliers.length}
              </Badge>
              {totalDebt > 0 && (
                <Badge variant="destructive" className="text-sm h-5 px-2">
                  Dette: {totalDebt.toFixed(2)} DA
                </Badge>
              )}
            </div>

            {/* Center: Search and Filters */}
            <div className="flex-1 flex items-center justify-center space-x-2 max-w-2xl">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Rechercher des fournisseurs..."
                  className="pl-10 h-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select
                value={selectedCategory || "all"}
                onValueChange={(value) => setSelectedCategory(value === "all" ? null : value)}
              >
                <SelectTrigger className="h-8 w-36">
                  <SelectValue placeholder="Catégorie" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes</SelectItem>
                  <SelectItem value="none">Aucune</SelectItem>
                  {supplierCategories.map(category => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={statusFilter || "all"}
                onValueChange={(value) => setStatusFilter(value === "all" ? null : value)}
              >
                <SelectTrigger className="h-8 w-20">
                  <SelectValue placeholder="Statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous</SelectItem>
                  <SelectItem value="active">Actif</SelectItem>
                  <SelectItem value="inactive">Inactif</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" onClick={refreshSuppliers} className="h-8 px-2">
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>

            {/* Right: Actions */}
            <div className="flex items-center">
              <Button
                onClick={() => setIsSupplierDialogOpen(true)}
                className="h-8 px-3"
              >
                <Plus className="h-4 w-4 mr-1" />
                Ajouter fournisseur
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Full Width */}
      <div className="flex-1 flex overflow-hidden">
        {/* Suppliers List */}
        <div className={`${viewingSupplier ? 'hidden lg:block' : ''} lg:w-80 border-r overflow-auto`}>
          {filteredSuppliers.length === 0 ? (
            <div className="flex items-center justify-center h-32 text-center p-2">
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Aucun fournisseur trouvé</p>
                <Button size="sm" onClick={() => setIsSupplierDialogOpen(true)} className="h-7">
                  <Plus className="h-4 w-4 mr-1" />
                  Ajouter fournisseur
                </Button>
              </div>
            </div>
          ) : (
            <div>
              {filteredSuppliers.map((supplier) => (
                <div
                  key={supplier.id}
                  className={`px-2 py-1.5 cursor-pointer hover:bg-muted/50 transition-colors border-b border-border/50 ${
                    viewingSupplier?.id === supplier.id ? 'bg-muted border-r-2 border-primary' : ''
                  }`}
                  onClick={() => setViewingSupplier(supplier)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-1.5">
                        <span className="font-medium text-sm truncate">{supplier.name}</span>
                        {!supplier.isActive && (
                          <Badge variant="outline" className="h-4 text-xs px-1">Inactif</Badge>
                        )}
                        {supplier.category && (
                          <Badge variant="secondary" className="h-4 text-xs px-1">{supplier.category}</Badge>
                        )}
                      </div>
                      <div className="flex items-center space-x-1 text-xs text-muted-foreground mt-0.5">
                        <Phone className="h-3 w-3" />
                        <span className="truncate">{supplier.phoneNumber}</span>
                      </div>
                    </div>
                    <div className="text-right ml-2">
                      <div className={`text-sm font-medium ${
                        supplier.balance > 0 ? 'text-red-600' : 
                        supplier.balance < 0 ? 'text-green-600' : 'text-muted-foreground'
                      }`}>
                        {Math.abs(supplier.balance).toFixed(2)} DA
                      </div>
                      <div className="flex space-x-0.5 mt-0.5">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            openEditDialog(supplier);
                          }}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0 text-destructive hover:text-destructive"
                          onClick={(e) => {
                            e.stopPropagation();
                            openDeleteDialog(supplier);
                          }}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Supplier Details - Full Width */}
        <div className="flex-1 overflow-auto">
          {viewingSupplier ? (
            <div className="h-full flex flex-col">
              {/* Mobile Back Button */}
              <div className="lg:hidden flex items-center space-x-2 px-2 py-1.5 border-b">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setViewingSupplier(null)}
                  className="h-6 w-6 p-0"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <span className="font-medium truncate">{viewingSupplier.name}</span>
              </div>

              {/* Desktop Header */}
              <div className="hidden lg:block border-b bg-muted/10 px-3 py-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-lg">{viewingSupplier.name}</span>
                    <Badge variant={viewingSupplier.isActive ? "default" : "outline"} className="h-5">
                      {viewingSupplier.isActive ? "Actif" : "Inactif"}
                    </Badge>
                    {viewingSupplier.category && (
                      <Badge variant="secondary" className="h-5">{viewingSupplier.category}</Badge>
                    )}
                  </div>
                  <Button variant="ghost" size="sm" onClick={() => setViewingSupplier(null)} className="h-6 w-6 p-0">
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 p-2 lg:p-3 space-y-2 lg:space-y-3 overflow-auto">
                {/* Contact & Balance Row */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 lg:gap-3">
                  <div className="flex items-center space-x-1.5">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{viewingSupplier.phoneNumber}</span>
                  </div>
                  <div className="flex items-center space-x-1.5">
                    <Coins className="h-4 w-4 text-muted-foreground" />
                    <span className={`font-medium text-sm ${
                      viewingSupplier.balance > 0 ? 'text-red-600' : 
                      viewingSupplier.balance < 0 ? 'text-green-600' : 'text-muted-foreground'
                    }`}>
                      {Math.abs(viewingSupplier.balance).toFixed(2)} DA
                      <span className="text-muted-foreground ml-1 text-xs">
                        ({viewingSupplier.balance > 0 ? 'dette' : viewingSupplier.balance < 0 ? 'crédit' : 'équilibré'})
                      </span>
                    </span>
                  </div>
                </div>

                {/* Notes */}
                {viewingSupplier.notes && (
                  <div className="bg-muted/30 p-2 rounded text-sm">
                    <span className="font-medium">Notes: </span>
                    {viewingSupplier.notes}
                  </div>
                )}

                {/* Payment Form */}
                {viewingSupplier.balance > 0 && (
                  <div className="border rounded p-2 lg:p-3 bg-background">
                    <div className="flex items-center space-x-1.5 mb-2">
                      <CreditCard className="h-4 w-4" />
                      <span className="font-medium text-sm">Effectuer un paiement</span>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      <Input
                        type="number"
                        placeholder="Montant"
                        min="0.01"
                        step="0.01"
                        value={paymentAmount || ''}
                        onChange={(e) => setPaymentAmount(parseFloat(e.target.value) || 0)}
                        className="h-8"
                      />
                      <Button
                        variant="outline"
                        onClick={() => setPaymentAmount(viewingSupplier.balance)}
                        className="h-8 text-sm"
                      >
                        Payer le solde complet
                      </Button>
                    </div>
                    <Input
                      placeholder="Notes (optionnel)"
                      value={paymentNotes}
                      onChange={(e) => setPaymentNotes(e.target.value)}
                      className="h-8 mt-2"
                    />
                    <Button
                      onClick={async () => {
                        if (!viewingSupplier || paymentAmount <= 0) return;
                        setIsSubmittingPayment(true);
                        try {
                          await handlePaymentToSupplier({
                            amount: paymentAmount,
                            notes: paymentNotes
                          });
                          setPaymentAmount(0);
                          setPaymentNotes("");
                        } finally {
                          setIsSubmittingPayment(false);
                        }
                      }}
                      disabled={!paymentAmount || paymentAmount <= 0 || isSubmittingPayment}
                      className="w-full h-8 mt-2 text-sm"
                    >
                      {isSubmittingPayment ? (
                        <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                      ) : (
                        <CreditCard className="h-4 w-4 mr-1" />
                      )}
                      Payer {paymentAmount > 0 && `${paymentAmount.toFixed(2)} DA`}
                    </Button>
                  </div>
                )}

                {/* Transaction History */}
                <div className="border rounded overflow-hidden">
                  <div className="bg-muted/20 px-2 py-1.5 border-b">
                    <span className="font-medium text-sm">Historique des transactions</span>
                  </div>
                  <div className="max-h-64 lg:max-h-96 overflow-auto">
                    <BatchedPurchaseHistory
                      purchases={supplierPurchases}
                      stockItems={stockItems}
                      suppliers={suppliers}
                      searchTerm={transactionSearchTerm}
                      onSearchChange={setTransactionSearchTerm}
                      isMobile={true}
                    />
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="hidden lg:flex lg:items-center lg:justify-center lg:h-full">
              <div className="text-center space-y-2">
                <Eye className="h-12 w-12 text-muted-foreground/50 mx-auto" />
                <h3 className="text-lg font-medium">Sélectionner un fournisseur</h3>
                <p className="text-muted-foreground max-w-sm text-sm">
                  Choisissez un fournisseur dans la liste pour voir les détails.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Dialogs */}
      <Dialog open={isSupplierDialogOpen} onOpenChange={setIsSupplierDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader className="pb-2">
            <DialogTitle>
              {editingSupplier ? "Modifier le fournisseur" : "Nouveau fournisseur"}
            </DialogTitle>
          </DialogHeader>
          <SimpleSupplierForm
            onSubmit={handleSupplierSubmit}
            initialData={editingSupplier}
            onCancel={() => {
              setIsSupplierDialogOpen(false);
              setEditingSupplier(null);
            }}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-sm">
          <DialogHeader className="pb-2">
            <DialogTitle>Confirmer la suppression</DialogTitle>
            <DialogDescription>
              Supprimer "{supplierToDelete?.name}" ? Cette action est irréversible.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)} className="h-8">
              Annuler
            </Button>
            <Button variant="destructive" onClick={handleDeleteSupplier} className="h-8">
              Supprimer
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}