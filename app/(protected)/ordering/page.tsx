"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useRouter } from 'next/navigation';
import { useStaticNavigation } from '@/lib/utils/navigation';
import NewOrderingInterface from '../../components/NewOrderingInterface';
import OrderList from '../../components/OrderList';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { EditOrderProvider } from '@/components/providers/EditOrderContext';

export default function OrderingPage() {
  const { isAuthenticated, loading } = useAuth();
  const router = useRouter();
  const { navigate } = useStaticNavigation();
  const [tab, setTab] = useState('ordering');

  useEffect(() => {
    // Redirect to login if not authenticated and auth check is complete
    if (!loading && !isAuthenticated) {
      navigate('auth');
    }
  }, [isAuthenticated, loading, navigate]);

  // Show loading until auth is determined
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Only render the page when authenticated
  if (!isAuthenticated) return null;

  return (
    <EditOrderProvider>
      <div className="absolute inset-0 flex flex-col">
        <Tabs value={tab} onValueChange={setTab} className="flex-grow flex flex-col">
          <div className="flex-shrink-0 flex justify-between items-center px-3 pt-3 pb-1">
            <TabsList>
              <TabsTrigger value="ordering">Ordering Interface</TabsTrigger>
              <TabsTrigger value="orders">Orders List</TabsTrigger>
            </TabsList>
          </div>
          <TabsContent value="ordering" className="flex-grow overflow-hidden min-h-0">
            <NewOrderingInterface />
          </TabsContent>
          <TabsContent value="orders" className="flex-grow overflow-hidden min-h-0">
            <OrderList setTab={setTab} />
          </TabsContent>
        </Tabs>
      </div>
    </EditOrderProvider>
  );
}