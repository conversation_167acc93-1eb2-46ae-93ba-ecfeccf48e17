'use client';

import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { isAdmin } from "@/lib/auth/role-utils";
import { CorsFixHelper } from "@/components/CorsFixHelper";
import { SyncStatus } from "@/components/ui/sync-status";
import { Cache<PERSON>leaner } from "@/components/CacheCleaner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CalendarDays, Settings, Shield, Trash2, Printer, MonitorSmartphone, Timer, AlertCircle, ChefHat, Cloud, TestTube, Bug, ExternalLink } from "lucide-react";
import { useState, useEffect } from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { GoogleDriveSettings } from "@/components/settings/GoogleDriveSettings";
import { KitchenPrintingSetup } from "@/components/settings/KitchenPrintingSetup";
import { RestaurantInfoSettings } from "@/components/settings/RestaurantInfoSettings";

import { useMenuV4 } from "@/lib/hooks/use-menu-v4";
import { useRouter } from "next/navigation";

export default function SettingsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const router = useRouter();
  const userIsAdmin = isAdmin(user);

  const { categories } = useMenuV4();

  const navigateToKitchenPrinterTest = () => {
    router.push('/debug/kitchen-printer-test');
  };

  const navigateToDebugPage = () => {
    router.push('/debug');
  };

  const navigateToCashTest = () => {
    router.push('/debug/caisse-test');
  };



  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-2">Settings</h1>
      <p className="text-muted-foreground mb-8">Manage application settings and troubleshoot issues</p>

      <Tabs defaultValue="general">
        <TabsList className="mb-8">
          <TabsTrigger value="general">
            <Settings className="h-4 w-4 mr-2" />
            General
          </TabsTrigger>
          <TabsTrigger value="cloud">
            <Cloud className="h-4 w-4 mr-2" />
            Cloud Storage
          </TabsTrigger>
          {userIsAdmin && (
            <TabsTrigger value="advanced">
              <Shield className="h-4 w-4 mr-2" />
              Advanced
            </TabsTrigger>
          )}
          <TabsTrigger value="restaurant-info">
            <Printer className="h-4 w-4 mr-2" />
            Restaurant Info
          </TabsTrigger>
          <TabsTrigger value="kitchen-printers">
            <Printer className="h-4 w-4 mr-2" />
            Kitchen Printers
          </TabsTrigger>
        </TabsList>

        <TabsContent value="cloud" className="space-y-6">
          <GoogleDriveSettings />
        </TabsContent>

        <TabsContent value="kitchen-printers" className="space-y-6">
          <KitchenPrintingSetup categories={categories} />
        </TabsContent>

        {userIsAdmin && (
          <TabsContent value="advanced" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Advanced Settings</CardTitle>
                <CardDescription>Administrator-only options</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                      <Bug className="h-5 w-5" />
                      Debug & Testing Tools
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card className="p-4">
                        <div className="flex items-center gap-3 mb-2">
                          <TestTube className="h-5 w-5 text-blue-600" />
                          <h4 className="font-medium">Kitchen Printer Testing</h4>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          Comprehensive testing suite for all kitchen printer systems (single, multi-station, multi-barcode)
                        </p>
                        <Button
                          onClick={navigateToKitchenPrinterTest}
                          className="w-full"
                          size="sm"
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Open Printer Test Suite
                        </Button>
                      </Card>

                      <Card className="p-4">
                        <div className="flex items-center gap-3 mb-2">
                          <MonitorSmartphone className="h-5 w-5 text-green-600" />
                          <h4 className="font-medium">Cash System Testing</h4>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          Test cash register functionality, sessions, and counting operations
                        </p>
                        <Button
                          onClick={navigateToCashTest}
                          variant="outline"
                          className="w-full"
                          size="sm"
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Open Cash Test Suite
                        </Button>
                      </Card>

                      <Card className="p-4">
                        <div className="flex items-center gap-3 mb-2">
                          <Settings className="h-5 w-5 text-purple-600" />
                          <h4 className="font-medium">General Debug Tools</h4>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          Database cleanup, order format fixes, and other debugging utilities
                        </p>
                        <Button
                          onClick={navigateToDebugPage}
                          variant="outline"
                          className="w-full"
                          size="sm"
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Open Debug Tools
                        </Button>
                      </Card>

                      <Card className="p-4 border-orange-200 bg-orange-50">
                        <div className="flex items-center gap-3 mb-2">
                          <AlertCircle className="h-5 w-5 text-orange-600" />
                          <h4 className="font-medium text-orange-800">Development Only</h4>
                        </div>
                        <p className="text-sm text-orange-700">
                          These testing tools are only available in development mode and are automatically disabled in production builds.
                        </p>
                      </Card>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Application Settings</CardTitle>
              <CardDescription>General configuration options</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="restaurant-info" className="space-y-6">
          <RestaurantInfoSettings />
        </TabsContent>
      </Tabs>
    </div>
  );
}