'use client';

import * as React from 'react';
import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2 } from "lucide-react";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import { Info } from "lucide-react";

export interface StaffPermissions {
  pages: {
    menu: boolean;
    orders: boolean;
    waiter: boolean;
    kitchen: boolean;
    finance: boolean;
    analytics: boolean;
    inventory: boolean;
    production: boolean;
    staff: boolean;
    settings: boolean;
    suppliers: boolean;
  };
  tabs?: {
    inventory: {
      inventory: boolean;
      counts: boolean;
      waste: boolean;
      production: boolean;
      recettes: boolean;
    };
    staff: {
      staff: boolean;
      shifts_schedule: boolean;
      attendance: boolean;
      payments: boolean;
    };
  };
  components?: Array<{
    page: string;
    component: string;
    access: boolean;
  }>;
}

interface StaffPermissionsFormProps {
  initialPermissions?: StaffPermissions;
  staffName?: string;
  onSubmit: (permissions: StaffPermissions) => Promise<void>;
  onCancel?: () => void;
}

export function StaffPermissionsForm({
  initialPermissions,
  staffName,
  onSubmit,
  onCancel
}: StaffPermissionsFormProps) {
  const [permissions, setPermissions] = useState<StaffPermissions>(
    initialPermissions || {
      pages: {
        menu: false,
        orders: false,
        waiter: false,
        kitchen: false,
        finance: false,
        analytics: false,
        inventory: false,
        production: false,
        staff: false,
        settings: false,
        suppliers: false,
      },
      tabs: {
        inventory: {
          inventory: false,
          counts: false,
          waste: false,
          production: false,
          recettes: false,
        },
        staff: {
          staff: false,
          shifts_schedule: false,
          attendance: false,
          payments: false,
        }
      },
      components: []
    }
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handlePermissionChange = (page: keyof StaffPermissions['pages']) => {
    setPermissions(prev => ({
      ...prev,
      pages: {
        ...prev.pages,
        [page]: !prev.pages[page as keyof StaffPermissions['pages']]
      }
    }));
  };

  const handleTabPermissionChange = (page: 'inventory' | 'staff', tab: string) => {
    setPermissions(prev => {
      // Create a deep copy to avoid mutation
      const newPermissions = { ...prev };

      // Ensure tabs object exists
      if (!newPermissions.tabs) {
        newPermissions.tabs = {
          inventory: { 
            inventory: false, 
            counts: false, 
            waste: false, 
            production: false, 
            recettes: false 
          },
          staff: { 
            staff: false, 
            shifts_schedule: false, 
            attendance: false, 
            payments: false 
          }
        };
      }

      // Toggle the specific tab permission
      if (page === 'inventory') {
        const currentValue = newPermissions.tabs.inventory[tab as keyof typeof newPermissions.tabs.inventory];
        newPermissions.tabs.inventory[tab as keyof typeof newPermissions.tabs.inventory] = !currentValue;
      } else if (page === 'staff') {
        const currentValue = newPermissions.tabs.staff[tab as keyof typeof newPermissions.tabs.staff];
        newPermissions.tabs.staff[tab as keyof typeof newPermissions.tabs.staff] = !currentValue;
      }

      return newPermissions;
    });
  };

  const handleComponentPermissionChange = (page: string, component: string) => {
    setPermissions(prev => {
      // Create a deep copy to avoid mutation
      const newPermissions = { ...prev };

      // Ensure components array exists
      if (!newPermissions.components) {
        newPermissions.components = [];
      }

      // Check if component already exists in the array
      const existingIndex = newPermissions.components.findIndex(
        c => c.page === page && c.component === component
      );

      if (existingIndex >= 0) {
        // Toggle the existing component's access
        newPermissions.components[existingIndex].access = !newPermissions.components[existingIndex].access;
        
        // If access is false, remove the component from the array
        if (!newPermissions.components[existingIndex].access) {
          newPermissions.components.splice(existingIndex, 1);
        }
      } else {
        // Add the component with access true
        newPermissions.components.push({
          page,
          component,
          access: true
        });
      }

      return newPermissions;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      await onSubmit(permissions);
    } finally {
      setIsSubmitting(false);
    }
  };

  const permissionDescriptions = {
    menu: "Manage menu items, categories, and visibility",
    orders: "View and process customer orders",
    waiter: "Access the waiter app for taking orders",
    kitchen: "Access the kitchen display system",
    finance: "Access financial data, expenses, and cash drawer",
    analytics: "View business analytics and reports",
    inventory: "Manage and track inventory and stock",
    production: "Manage food production and recipes",
    staff: "Manage staff accounts and schedules",
    settings: "Configure restaurant settings",
    suppliers: "Manage supplier information and orders"
  };

  // Tab descriptions
  const tabDescriptions = {
    inventory: {
      inventory: "View and manage inventory items",
      counts: "Perform and manage stock counts",
      waste: "Track and record inventory waste",
      production: "Manage production and batch recipes",
      recettes: "View and manage recipes"
    },
    staff: {
      staff: "View and manage staff list",
      shifts_schedule: "Manage shifts and schedules",
      attendance: "Track staff attendance",
      payments: "Manage staff payments"
    }
  };

  // Active tab state
  const [activeTab, setActiveTab] = useState<string>("pages");

  return (
    <div>
      <Card className="border-none shadow-none">
        <CardHeader className="px-0">
          <CardTitle className="text-xl">
            {staffName ? `Permissions for ${staffName}` : 'Staff Permissions'}
          </CardTitle>
          <CardDescription>
            Control which features this staff member can access in the app
          </CardDescription>
        </CardHeader>
        <CardContent className="px-0 pt-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="pages">Page Access</TabsTrigger>
              <TabsTrigger value="tabs">Tab Access</TabsTrigger>
            </TabsList>

            {/* Page Permissions Tab */}
            <TabsContent value="pages">
              <div className="grid gap-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(permissions.pages).map(([page, enabled]) => (
                    <div key={page} className="flex items-start space-x-3 space-y-0 rounded-md border p-4">
                      <Checkbox
                        id={`permission-${page}`}
                        checked={enabled}
                        onCheckedChange={() => handlePermissionChange(page as keyof StaffPermissions['pages'])}
                        className="mt-1"
                      />
                      <div className="flex flex-col">
                        <label
                          htmlFor={`permission-${page}`}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                        >
                          <span className="capitalize">{page}</span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3.5 w-3.5 ml-1.5 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent side="right" align="start" className="max-w-[220px]">
                                {permissionDescriptions[page as keyof typeof permissionDescriptions]}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </label>
                        <p className="text-xs text-muted-foreground mt-1">
                          {permissionDescriptions[page as keyof typeof permissionDescriptions]}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* Tab Permissions Tab */}
            <TabsContent value="tabs">
              <div className="space-y-6">
                {/* Inventory Tabs Section */}
                <div className="space-y-3">
                  <h3 className="text-md font-medium">Inventory Page Tabs</h3>
                  <p className="text-sm text-muted-foreground mb-3">
                    Control which tabs within the Inventory page this staff member can access
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {permissions.tabs && Object.entries(permissions.tabs.inventory).map(([tab, enabled]) => (
                      <div key={tab} className="flex items-start space-x-3 space-y-0 rounded-md border p-4">
                        <Checkbox
                          id={`tab-inventory-${tab}`}
                          checked={enabled}
                          onCheckedChange={() => handleTabPermissionChange('inventory', tab)}
                          className="mt-1"
                          disabled={!permissions.pages.inventory}
                        />
                        <div className="flex flex-col">
                          <label
                            htmlFor={`tab-inventory-${tab}`}
                            className={`text-sm font-medium leading-none flex items-center ${!permissions.pages.inventory ? 'text-muted-foreground' : ''}`}
                          >
                            <span className="capitalize">{tab}</span>
                          </label>
                          <p className="text-xs text-muted-foreground mt-1">
                            {tabDescriptions.inventory[tab as keyof typeof tabDescriptions.inventory]}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {!permissions.pages.inventory && (
                    <p className="text-xs text-amber-500 mt-1">
                      Note: Enable Inventory page access first to manage tab permissions
                    </p>
                  )}
                </div>

                {/* Staff Tabs Section */}
                <div className="space-y-3">
                  <h3 className="text-md font-medium">Staff Page Tabs</h3>
                  <p className="text-sm text-muted-foreground mb-3">
                    Control which tabs within the Staff page this staff member can access
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {permissions.tabs && Object.entries(permissions.tabs.staff).map(([tab, enabled]) => (
                      <div key={tab} className="flex items-start space-x-3 space-y-0 rounded-md border p-4">
                        <Checkbox
                          id={`tab-staff-${tab}`}
                          checked={enabled}
                          onCheckedChange={() => handleTabPermissionChange('staff', tab)}
                          className="mt-1"
                          disabled={!permissions.pages.staff}
                        />
                        <div className="flex flex-col">
                          <label
                            htmlFor={`tab-staff-${tab}`}
                            className={`text-sm font-medium leading-none flex items-center ${!permissions.pages.staff ? 'text-muted-foreground' : ''}`}
                          >
                            <span className="capitalize">{tab.replace('_', ' ')}</span>
                          </label>
                          <p className="text-xs text-muted-foreground mt-1">
                            {tabDescriptions.staff[tab as keyof typeof tabDescriptions.staff]}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {!permissions.pages.staff && (
                    <p className="text-xs text-amber-500 mt-1">
                      Note: Enable Staff page access first to manage tab permissions
                    </p>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end space-x-4 mt-6">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="button" disabled={isSubmitting} onClick={handleSubmit}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Permissions'
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}