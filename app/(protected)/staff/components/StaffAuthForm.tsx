'use client';

import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Loader2, User, AlertTriangle, X, Check } from "lucide-react";
import { updateStaffMember } from '@/lib/db/v4';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { getApiUrl } from '@/lib/build-config';
import { isUsernameAvailable } from '@/lib/services/staff-auth-service';

// Use a more flexible staff member type to avoid conflicts
interface StaffMemberForAuth {
  id: string;
  name: string;
  role: string;
  email?: string;
  hasUserAccount?: boolean;
  username?: string;
  permissions?: {
    pages: {
      menu: boolean;
      orders: boolean;
      finance: boolean;
      inventory: boolean;
      staff: boolean;
      settings: boolean;
      suppliers: boolean;
    };
    tabs?: {
      inventory?: {
        inventory?: boolean;
        counts?: boolean;
        waste?: boolean;
        production?: boolean;
        recettes?: boolean;
      };
      staff?: {
        staff?: boolean;
        shifts_schedule?: boolean;
        attendance?: boolean;
        payments?: boolean;
      };
    };
  };
}

interface StaffAuthFormProps {
  staffMember: StaffMemberForAuth;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function StaffAuthForm({ staffMember, onSuccess, onCancel }: StaffAuthFormProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { isAuthenticated, user } = useAuth();
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);
  const [usernameAvailable, setUsernameAvailable] = useState<boolean | null>(null);

  // Check if this is an edit operation
  const isEditing = staffMember.hasUserAccount;

  // Create dynamic schema based on editing mode
  const authFormSchema = z.object({
    username: z.string().min(3, "Username must be at least 3 characters"),
    password: isEditing 
      ? z.string().optional() 
      : z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: isEditing 
      ? z.string().optional() 
      : z.string().min(8, "Password confirmation is required"),
    permissions: z.object({
      pages: z.object({
        menu: z.boolean().default(true),
        orders: z.boolean().default(true),
        finance: z.boolean().default(false),
        inventory: z.boolean().default(false),
        staff: z.boolean().default(false),
        settings: z.boolean().default(false),
        suppliers: z.boolean().default(false),
      }),
      tabs: z.object({
        inventory: z.object({
          inventory: z.boolean().default(false),
          counts: z.boolean().default(false),
          waste: z.boolean().default(false),
          production: z.boolean().default(false),
          recettes: z.boolean().default(false),
        }).optional(),
        staff: z.object({
          staff: z.boolean().default(false),
          shifts_schedule: z.boolean().default(false),
          attendance: z.boolean().default(false),
          payments: z.boolean().default(false),
        }).optional(),
      }).optional(),
    }),
  }).refine((data) => {
    // Only validate password match if password is provided
    if (data.password && data.password.length > 0) {
      return data.password === data.confirmPassword;
    }
    return true;
  }, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

  type AuthFormValues = z.infer<typeof authFormSchema>;

  // Initialize form
  const form = useForm<AuthFormValues>({
    resolver: zodResolver(authFormSchema),
    defaultValues: {
      username: staffMember.username || '', // Pre-fill username if editing
      password: '',
      confirmPassword: '',
      permissions: {
        pages: {
          menu: staffMember.permissions?.pages?.menu ?? true,
          orders: staffMember.permissions?.pages?.orders ?? true,
          finance: staffMember.permissions?.pages?.finance ?? false,
          inventory: staffMember.permissions?.pages?.inventory ?? false,
          staff: staffMember.permissions?.pages?.staff ?? false,
          settings: staffMember.permissions?.pages?.settings ?? false,
          suppliers: staffMember.permissions?.pages?.suppliers ?? false,
        },
        tabs: {
          inventory: {
            inventory: staffMember.permissions?.tabs?.inventory?.inventory ?? false,
            counts: staffMember.permissions?.tabs?.inventory?.counts ?? false,
            waste: staffMember.permissions?.tabs?.inventory?.waste ?? false,
            production: staffMember.permissions?.tabs?.inventory?.production ?? false,
            recettes: staffMember.permissions?.tabs?.inventory?.recettes ?? false,
          },
          staff: {
            staff: staffMember.permissions?.tabs?.staff?.staff ?? false,
            shifts_schedule: staffMember.permissions?.tabs?.staff?.shifts_schedule ?? false,
            attendance: staffMember.permissions?.tabs?.staff?.attendance ?? false,
            payments: staffMember.permissions?.tabs?.staff?.payments ?? false,
          },
        },
      },
    },
  });

  // Handle permission toggle
  const handlePermissionChange = (page: string) => {
    const currentPermissions = form.getValues('permissions.pages');
    form.setValue(`permissions.pages.${page}` as any, !currentPermissions[page as keyof typeof currentPermissions]);
  };

  // Handle tab permission toggle
  const handleTabPermissionChange = (section: string, tab: string) => {
    const currentTabs = form.getValues('permissions.tabs') || {};
    const sectionTabs = (currentTabs as any)[section] || {};
    
    form.setValue(`permissions.tabs.${section}.${tab}` as any, !sectionTabs[tab]);
  };

  // Watch permissions for UI updates
  const watchedPermissions = form.watch('permissions');

  // Real-time username validation
  const checkUsernameAvailability = useCallback(async (username: string) => {
    if (!username || username.length < 3) {
      setUsernameAvailable(null);
      return;
    }

    // Don't check if we're editing and the username hasn't changed
    if (isEditing && username === staffMember.username) {
      setUsernameAvailable(null);
      return;
    }

    setIsCheckingUsername(true);
    try {
      const available = await isUsernameAvailable(username);
      setUsernameAvailable(available);
      
      // Log for debugging
      console.log(`Username "${username}" availability:`, available);
    } catch (error) {
      console.error('Error checking username availability:', error);
      setUsernameAvailable(null);
      
      // Show a toast for network errors
      toast({
        title: "Erreur de vérification",
        description: "Impossible de vérifier la disponibilité du nom d'utilisateur. Vérifiez votre connexion.",
        variant: "destructive",
      });
    } finally {
      setIsCheckingUsername(false);
    }
  }, [isEditing, staffMember.username, toast]);

  // Debounced username check
  useEffect(() => {
    const username = form.watch('username');
    if (!username) {
      setUsernameAvailable(null);
      return;
    }

    const timeoutId = setTimeout(() => {
      checkUsernameAvailability(username);
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [form.watch('username'), checkUsernameAvailability]);

  // Handle form submission
  const onSubmit = async (data: AuthFormValues) => {
    setIsSubmitting(true);

    try {
      console.log('StaffAuthForm: Starting auth operation for staff:', staffMember.name);

      // Check username availability before submitting (for new auth creation)
      if (!isEditing) {
        if (usernameAvailable === false) {
          throw new Error('Ce nom d\'utilisateur existe déjà. Veuillez en choisir un autre.');
        }
        
        if (usernameAvailable === null && data.username) {
          // Force check username availability if not already checked
          setIsCheckingUsername(true);
          try {
            const available = await isUsernameAvailable(data.username);
            if (!available) {
              throw new Error('Ce nom d\'utilisateur existe déjà. Veuillez en choisir un autre.');
            }
          } finally {
            setIsCheckingUsername(false);
          }
        }
      }

      // Check if user is authenticated
      if (!isAuthenticated || !user) {
        throw new Error('Vous devez être connecté pour effectuer cette action.');
      }

      console.log(`StaffAuthForm: ${isEditing ? 'Updating' : 'Creating'} auth for staff:`, staffMember.id);

      if (isEditing) {
        // UPDATE existing auth credentials via API
        const updateData: { username: string; password?: string } = {
          username: data.username
        };
        
        // Only include password if it was provided
        if (data.password && data.password.trim() !== '') {
          updateData.password = data.password;
        }

        console.log('StaffAuthForm: Calling update auth API');
        const updateUrl = getApiUrl('staff/auth/update') || '/api/staff/auth/update';
        const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
        const response = await fetch(updateUrl, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
          },
          body: JSON.stringify({
            staffId: staffMember.id,
            ...updateData
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          
          // Provide specific error messages based on status
          if (response.status === 401) {
            throw new Error('Authentication required. Please log in again.');
          } else if (response.status === 403) {
            throw new Error('You do not have permission to perform this action.');
          } else {
            throw new Error(errorData.error || 'Failed to update auth credentials');
          }
        }

        const result = await response.json();
        console.log('StaffAuthForm: Auth update successful:', result);

        // Update the PouchDB staff record with new auth info
        try {
          await updateStaffMember(staffMember.id, {
            username: data.username,
            hasUserAccount: true,
            permissions: data.permissions, // Update permissions too
          });
          console.log('StaffAuthForm: PouchDB staff record updated');
        } catch (pouchError) {
          console.warn('StaffAuthForm: Failed to update PouchDB record (non-critical):', pouchError);
        }

        toast({
          title: "Accès mis à jour",
          description: `Les identifiants et permissions ont été mis à jour pour ${staffMember.name}`,
        });

      } else {
        // CREATE new auth credentials via API
        console.log('StaffAuthForm: Calling create auth API');
        
        // Get restaurant ID for the request
        let restaurantId;
        try {
          const { getCurrentRestaurantId } = await import('@/lib/db/v4/utils/restaurant-id');
          restaurantId = getCurrentRestaurantId();
          
          if (!restaurantId) {
            throw new Error('Could not determine restaurant ID');
          }
          
          // Clean restaurant ID if it has prefix
          if (restaurantId.startsWith('resto_')) {
            restaurantId = restaurantId.replace('resto_', '');
          }
        } catch (error) {
          console.error('StaffAuthForm: Error getting restaurant ID:', error);
          throw new Error('Could not determine restaurant ID. Please try again.');
        }

        const createUrl = getApiUrl('staff/auth/create') || '/api/staff/auth/create';
        const tokenCreate = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
        const response = await fetch(createUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...(tokenCreate ? { 'Authorization': `Bearer ${tokenCreate}` } : {}),
          },
          body: JSON.stringify({
            staffId: staffMember.id,
            username: data.username,
            password: data.password || '',
            name: staffMember.name,
            email: staffMember.email || '',
            role: staffMember.role,
            restaurantId: restaurantId,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          
          // Provide specific error messages based on status
          if (response.status === 401) {
            throw new Error('Authentication required. Please log in again.');
          } else if (response.status === 403) {
            throw new Error('You do not have permission to create staff accounts.');
          } else {
            throw new Error(errorData.error || 'Failed to create auth credentials');
          }
        }

        const result = await response.json();
        console.log('StaffAuthForm: Auth creation successful:', result);

        // Update the PouchDB staff record with auth info AND permissions
        try {
          await updateStaffMember(staffMember.id, {
            hasUserAccount: true,
            userId: result.userId,
            username: data.username,
            permissions: data.permissions, // Use permissions from form
          });
          
          console.log('StaffAuthForm: PouchDB staff record updated with auth info and permissions');
        } catch (pouchError) {
          console.warn('StaffAuthForm: Failed to update PouchDB record (non-critical):', pouchError);
          // Still consider the auth creation successful even if PouchDB update fails
        }

        toast({
          title: "Accès créé",
          description: `Les identifiants et permissions ont été créés pour ${staffMember.name}`,
        });
      }

      // Reset form
      form.reset();

      // Call onSuccess callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('StaffAuthForm: Error with auth credentials:', error);
      
      // Check if it's a network/offline error
      if (error instanceof TypeError && error.message.includes('fetch')) {
        toast({
          title: "Erreur réseau",
          description: "Veuillez vérifier votre connexion internet et réessayer. Les opérations d'authentification nécessitent une connectivité serveur.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Erreur",
          description: error instanceof Error ? error.message : `Échec de ${isEditing ? 'mise à jour' : 'création'} des identifiants`,
          variant: "destructive",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-3 p-2">
      <div className="flex items-center gap-2 bg-muted/50 rounded-md px-2 py-1">
        <User className="h-4 w-4 text-muted-foreground" />
        <div className="text-xs">
          <span className="font-medium">{staffMember.name}</span>
          <span className="text-muted-foreground ml-2">({staffMember.role})</span>
          {isEditing && (
            <span className="text-xs text-green-600 ml-2">• Accès créé</span>
          )}
        </div>
      </div>

      {/* Auth Status Warning - Only show if not authenticated */}
      {!isAuthenticated && (
        <div className="flex items-center gap-2 bg-yellow-50 border border-yellow-200 rounded-md px-2 py-1">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <div className="text-xs text-yellow-800">
            Session expirée - Veuillez vous reconnecter
          </div>
        </div>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2">
          {/* Auth Credentials */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 items-end border-b pb-2">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="text-xs">Nom d'utilisateur</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input size={12} className="h-7 text-xs px-2" placeholder="Nom d'utilisateur" {...field} />
                      {isCheckingUsername && (
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                          <div className="animate-spin h-3 w-3 border border-gray-300 border-t-blue-600 rounded-full"></div>
                        </div>
                      )}
                      {!isCheckingUsername && usernameAvailable === false && (
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                          <X className="h-3 w-3 text-red-500" />
                        </div>
                      )}
                      {!isCheckingUsername && usernameAvailable === true && (
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                          <Check className="h-3 w-3 text-green-500" />
                        </div>
                      )}
                    </div>
                  </FormControl>
                  {usernameAvailable === false && (
                    <p className="text-xs text-red-500">❌ Ce nom d'utilisateur existe déjà</p>
                  )}
                  {usernameAvailable === true && (
                    <p className="text-xs text-green-600">✅ Nom d'utilisateur disponible</p>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="text-xs">{isEditing ? 'Nouveau mot de passe' : 'Mot de passe'}</FormLabel>
                  <FormControl>
                    <Input size={12} className="h-7 text-xs px-2" type="password" placeholder="Mot de passe" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="text-xs">Confirmer</FormLabel>
                  <FormControl>
                    <Input size={12} className="h-7 text-xs px-2" type="password" placeholder="Confirmer" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* App Permissions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {/* Page Permissions */}
            <div>
              <div className="text-xs font-semibold mb-1">Accès aux pages</div>
              <div className="flex flex-wrap gap-1">
                {Object.entries(watchedPermissions.pages).map(([page, enabled]) => (
                  <div key={page} className="flex items-center gap-1 border rounded px-2 py-1 bg-background/80">
                    <Checkbox
                      id={`permission-${page}`}
                      checked={enabled}
                      onCheckedChange={() => handlePermissionChange(page)}
                      className="h-3 w-3"
                    />
                    <Label
                      htmlFor={`permission-${page}`}
                      className="text-xs cursor-pointer capitalize"
                    >
                      {page === 'menu' && 'Menu'}
                      {page === 'orders' && 'Commandes'}
                      {page === 'finance' && 'Finances'}
                      {page === 'inventory' && 'Stock'}
                      {page === 'staff' && 'Personnel'}
                      {page === 'settings' && 'Paramètres'}
                      {page === 'suppliers' && 'Fournisseurs'}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Tab Permissions - 2 columns */}
            <div className="flex flex-col gap-2">
              <div className="text-xs font-semibold mb-1">Accès aux onglets</div>
              <div className="flex gap-2">
                {/* Inventory Tabs */}
                <div className="flex-1">
                  <div className="text-[11px] font-medium mb-1">Stock</div>
                  <div className="flex flex-wrap gap-1">
                    {watchedPermissions.tabs?.inventory && Object.entries(watchedPermissions.tabs.inventory).map(([tab, enabled]) => (
                      <div key={tab} className={`flex items-center gap-1 border rounded px-1 py-0.5 ${!watchedPermissions.pages.inventory ? 'opacity-50' : ''}`}>
                        <Checkbox
                          id={`tab-inventory-${tab}`}
                          checked={enabled}
                          disabled={!watchedPermissions.pages.inventory}
                          onCheckedChange={() => handleTabPermissionChange('inventory', tab)}
                          className="h-3 w-3"
                        />
                        <Label
                          htmlFor={`tab-inventory-${tab}`}
                          className="text-[11px] cursor-pointer capitalize"
                        >
                          {tab === 'inventory' && 'Articles'}
                          {tab === 'counts' && 'Inventaires'}
                          {tab === 'waste' && 'Pertes'}
                          {tab === 'production' && 'Production'}
                          {tab === 'recettes' && 'Recettes'}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
                {/* Staff Tabs */}
                <div className="flex-1">
                  <div className="text-[11px] font-medium mb-1">Personnel</div>
                  <div className="flex flex-wrap gap-1">
                    {watchedPermissions.tabs?.staff && Object.entries(watchedPermissions.tabs.staff).map(([tab, enabled]) => (
                      <div key={tab} className={`flex items-center gap-1 border rounded px-1 py-0.5 ${!watchedPermissions.pages.staff ? 'opacity-50' : ''}`}>
                        <Checkbox
                          id={`tab-staff-${tab}`}
                          checked={enabled}
                          disabled={!watchedPermissions.pages.staff}
                          onCheckedChange={() => handleTabPermissionChange('staff', tab)}
                          className="h-3 w-3"
                        />
                        <Label
                          htmlFor={`tab-staff-${tab}`}
                          className="text-[11px] cursor-pointer capitalize"
                        >
                          {tab === 'staff' && 'Liste'}
                          {tab === 'shifts_schedule' && 'Plannings'}
                          {tab === 'attendance' && 'Présence'}
                          {tab === 'payments' && 'Paiements'}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-2 border-t mt-2">
            {onCancel && (
              <Button type="button" variant="outline" size="sm" onClick={onCancel}>
                Annuler
              </Button>
            )}
            <Button 
              type="submit" 
              size="sm" 
              disabled={
                isSubmitting || 
                isCheckingUsername || 
                (!isEditing && usernameAvailable === false)
              }
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditing ? 'Mise à jour...' : 'Création...'}
                </>
              ) : isCheckingUsername ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Vérification...
                </>
              ) : (
                isEditing ? 'Mettre à jour' : 'Créer'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
} 