"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react';

export default function DebugStaffAuthPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testAuthCreation = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error("No authentication token found - please refresh the page and try again");
      }

      const response = await fetch('/api/staff/debug-auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to test auth creation');
      }
      
      setResults(data);
    } catch (err) {
      console.error('Error testing auth creation:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Debug Staff Auth Creation</h1>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Staff Auth Debug Tool</CardTitle>
          <CardDescription>
            This tool tests the creation of staff auth documents with UUIDs to ensure consistency.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            Click the button below to create a test user with a UUID as the document ID.
            This will help diagnose issues with staff auth creation.
          </p>
          
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {results && (
            <div className="mt-4 space-y-4">
              <Alert variant={results.success ? "default" : "destructive"}>
                <CheckCircle className="h-4 w-4" />
                <AlertTitle>Test Results</AlertTitle>
                <AlertDescription>
                  <div className="mt-2">
                    <p>Success: {results.success ? 'Yes' : 'No'}</p>
                    <p>Message: {results.message}</p>
                    <p>Test ID: {results.testId}</p>
                    <p>User ID: {results.userId}</p>
                    {results.userDocId && <p>User Doc ID: {results.userDocId}</p>}
                    {results.idsMatch !== undefined && (
                      <p>IDs Match: {results.idsMatch ? 'Yes' : 'No'}</p>
                    )}
                    {results.verificationError && (
                      <p>Verification Error: {results.verificationError}</p>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button 
            onClick={testAuthCreation} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Testing Auth Creation...
              </>
            ) : (
              'Test Auth Creation'
            )}
          </Button>
        </CardFooter>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>What This Tool Does</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            This tool tests the creation of staff auth documents with UUIDs to ensure consistency
            between the auth document and the staff document.
          </p>
          <p className="mb-4">
            The test:
          </p>
          <ol className="list-decimal pl-6 mb-4 space-y-2">
            <li>Generates a UUID</li>
            <li>Creates a test user with the UUID as the document ID</li>
            <li>Verifies that the user was created with the correct ID</li>
            <li>Reports whether the IDs match</li>
          </ol>
          <p>
            If the IDs match, then the staff auth creation process is working correctly.
            If not, there may be an issue with how the UUID is being passed to the auth creation process.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
