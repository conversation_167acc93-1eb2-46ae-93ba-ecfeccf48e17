'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Trash2, RefreshCw } from 'lucide-react';
import { purgeOldFormatOrders } from '@/lib/db/v4';
import { useOrderV4 } from '@/lib/hooks/use-order-v4';

export default function DebugPage() {
  const [purgeResult, setPurgeResult] = useState<{ deleted: number; kept: number } | null>(null);
  const [isPurging, setIsPurging] = useState(false);
  const { orders, refreshOrders } = useOrderV4();

  const handlePurgeOldOrders = async () => {
    if (!confirm('⚠️ This will DELETE ALL old format orders! Are you sure?')) {
      return;
    }

    setIsPurging(true);
    try {
      console.log('🗑️ Starting purge of old format orders...');
      const result = await purgeOldFormatOrders();
      setPurgeResult(result);
      
      // Refresh orders to see the updated list
      await refreshOrders({ forceRefresh: true });
      
      console.log('✅ Purge completed:', result);
    } catch (error) {
      console.error('❌ Purge failed:', error);
      alert('Purge failed: ' + (error as any)?.message);
    } finally {
      setIsPurging(false);
    }
  };

  const oldFormatOrders = orders.filter(o => !o.id.match(/^order:\d{8}-\d+$/));
  const newFormatOrders = orders.filter(o => o.id.match(/^order:\d{8}-\d+$/));

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">🔧 Debug Tools</h1>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5" />
            Order Format Cleanup
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Badge variant="destructive" className="mb-2">
                Old Format Orders: {oldFormatOrders.length}
              </Badge>
              <div className="text-sm text-muted-foreground">
                {oldFormatOrders.slice(0, 3).map(o => (
                  <div key={o.id}>{o.id}</div>
                ))}
                {oldFormatOrders.length > 3 && <div>...and {oldFormatOrders.length - 3} more</div>}
              </div>
            </div>
            
            <div>
              <Badge variant="default" className="mb-2">
                New Format Orders: {newFormatOrders.length}
              </Badge>
              <div className="text-sm text-muted-foreground">
                {newFormatOrders.slice(0, 3).map(o => (
                  <div key={o.id}>{o.id}</div>
                ))}
                {newFormatOrders.length > 3 && <div>...and {newFormatOrders.length - 3} more</div>}
              </div>
            </div>
          </div>

          <Button 
            onClick={handlePurgeOldOrders}
            disabled={isPurging || oldFormatOrders.length === 0}
            variant="destructive"
            className="w-full"
          >
            {isPurging ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Purging Old Orders...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                🗑️ DELETE ALL {oldFormatOrders.length} Old Format Orders
              </>
            )}
          </Button>

          {purgeResult && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-semibold text-green-800">✅ Purge Completed!</h3>
              <p className="text-green-700">
                Deleted: {purgeResult.deleted} orders | Kept: {purgeResult.kept} orders
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 