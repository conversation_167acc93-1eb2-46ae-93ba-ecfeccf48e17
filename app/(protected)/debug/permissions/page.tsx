'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { usePermissions } from '@/lib/hooks/use-permissions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, Check, X } from 'lucide-react';
import { getAllStaff } from '@/lib/db/v4';

export default function DebugPermissionsPage() {
  const { user, isAuthenticated, isOwner } = useAuth();
  const { permissions, hasPageAccess, isLoading, error } = usePermissions();
  const [staffInfo, setStaffInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);
  
  // Load staff info from PouchDB
  const loadStaffInfo = async () => {
    try {
      setLoading(true);
      setFetchError(null);
      
      if (!user?.id) {
        setFetchError('No user ID found');
        return;
      }
      
      console.log('🔍 Debug: Loading staff info for user ID:', user.id);
      
      // Get all staff and find the one with matching userId
      const allStaff = await getAllStaff();
      console.log('🔍 Debug: Total staff found:', allStaff.length);
      
      const staffMember = allStaff.find(staff => staff.userId === user.id);
      
      if (staffMember) {
        console.log('🔍 Debug: Found staff member:', staffMember);
        setStaffInfo({
          staffDocument: staffMember,
          hasPermissions: !!staffMember.permissions,
          permissionsStructure: staffMember.permissions
        });
      } else {
        console.log('🔍 Debug: No staff member found with userId:', user.id);
        console.log('🔍 Debug: Available staff userIds:', allStaff.map(s => ({ id: s.id, userId: s.userId, name: s.name })));
        setStaffInfo({
          staffDocument: null,
          hasPermissions: false,
          permissionsStructure: null
        });
      }
    } catch (error) {
      console.error('Error loading staff info from PouchDB:', error);
      setFetchError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };
  
  // Load staff info on mount
  useEffect(() => {
    if (isAuthenticated && user) {
      loadStaffInfo();
    }
  }, [isAuthenticated, user]);
  
  const refreshInfo = () => {
    loadStaffInfo();
  };
  
  // Component for displaying page permission status
  const PagePermissionItem = ({ page, hasAccess }: { page: string; hasAccess: boolean }) => (
    <div className="flex items-center justify-between py-1">
      <span className="text-sm">{page}</span>
        {hasAccess ? (
        <Check className="h-4 w-4 text-green-600" />
        ) : (
        <X className="h-4 w-4 text-red-600" />
        )}
    </div>
  );
  
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">🔍 Permission Debug</h1>
        <Button onClick={refreshInfo} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        {/* Current User Info */}
        <Card>
          <CardHeader>
            <CardTitle>Current User</CardTitle>
            <CardDescription>Information about the authenticated user</CardDescription>
          </CardHeader>
          <CardContent>
            {isAuthenticated ? (
              <>
                <div className="space-y-2">
                  <div><strong>ID:</strong> {user?.id}</div>
                  <div><strong>Name:</strong> {user?.name}</div>
                  <div><strong>Role:</strong> {user?.role}</div>
                  <div><strong>Is Owner:</strong> {isOwner ? 'Yes' : 'No'}</div>
                  <div><strong>Restaurant ID:</strong> {user?.restaurantId}</div>
                </div>
              </>
            ) : (
              <div className="text-yellow-600">Not authenticated</div>
            )}
          </CardContent>
        </Card>
        
        {/* Permissions Context */}
        <Card>
          <CardHeader>
            <CardTitle>Permissions Context</CardTitle>
            <CardDescription>Current active permissions from new hook</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
                <span className="ml-2">Loading permissions...</span>
              </div>
            ) : error ? (
              <div className="text-red-600">
                <strong>Error:</strong> {error}
              </div>
            ) : (
            <div className="space-y-4">
              <div className="border rounded-md p-3">
                <h3 className="font-semibold mb-2">Page Permissions:</h3>
                <div className="space-y-1 divide-y">
                  <PagePermissionItem page="Menu" hasAccess={hasPageAccess('menu')} />
                  <PagePermissionItem page="Orders" hasAccess={hasPageAccess('orders')} />
                  <PagePermissionItem page="Finance" hasAccess={hasPageAccess('finance')} />
                  <PagePermissionItem page="Inventory" hasAccess={hasPageAccess('inventory')} />
                  <PagePermissionItem page="Staff" hasAccess={hasPageAccess('staff')} />
                  <PagePermissionItem page="Settings" hasAccess={hasPageAccess('settings')} />
                  <PagePermissionItem page="Suppliers" hasAccess={hasPageAccess('suppliers')} />
                </div>
              </div>
            </div>
            )}
          </CardContent>
        </Card>
        
        {/* PouchDB Staff Document Analysis */}
        <Card className="md:col-span-2">
          <CardHeader>
              <CardTitle>PouchDB Staff Document Analysis</CardTitle>
            <CardDescription>Direct analysis of staff document from local PouchDB</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center p-4">
                <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
                <span className="ml-2">Loading staff info...</span>
              </div>
            ) : fetchError ? (
              <div className="text-red-600">
                <strong>Error:</strong> {fetchError}
                    </div>
                  ) : (
              <div className="space-y-4">
                <div className="p-3 border rounded-md">
                  <h3 className="font-semibold mb-2">PouchDB Staff Document:</h3>
                  {staffInfo?.staffDocument ? (
                    <div className="space-y-2">
                      <div><strong>Staff ID:</strong> {staffInfo.staffDocument.id}</div>
                      <div><strong>Name:</strong> {staffInfo.staffDocument.name}</div>
                      <div><strong>Username:</strong> {staffInfo.staffDocument.name || staffInfo.staffDocument.username || 'Unknown'}</div>
                      <div><strong>Has Permissions:</strong> {staffInfo.hasPermissions ? 'Yes' : 'No'}</div>
                      
                      {staffInfo.permissionsStructure ? (
                        <div>
                          <strong>PouchDB Staff Permissions:</strong>
                          <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto">
                            {JSON.stringify(staffInfo.permissionsStructure, null, 2)}
                          </pre>
                        </div>
                      ) : (
                        <div className="text-yellow-600">No permissions found in PouchDB staff document</div>
                      )}
                    </div>
                  ) : (
                    <div className="text-yellow-600">No matching staff document found in PouchDB</div>
                  )}
                </div>
                
                {/* Raw data for developer inspection */}
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm text-gray-500">View Raw Debug Data</summary>
                  <pre className="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto max-h-96">
                    {JSON.stringify(staffInfo, null, 2)}
                  </pre>
                </details>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}