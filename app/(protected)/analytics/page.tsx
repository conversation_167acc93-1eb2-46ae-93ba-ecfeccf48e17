"use client"

import React, { useState } from 'react';
import { formatCurrency } from '@/lib/utils/currency';
import { 
  LayoutDashboardIcon, 
  ShoppingCartIcon, 
  DollarSignIcon, 
  ClipboardListIcon,
  UsersIcon, 
  BarChart4Icon, 
  RefreshCwIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from 'react-day-picker';

// Analytics Tab components imports
import SalesTab from '@/components/analytics/tabs/SalesTab';
import CogsTab from '@/components/analytics/tabs/CogsTab';
import ExpensesV4Tab from '@/components/analytics/tabs/ExpensesV4Tab';

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState('sales');
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = () => {
    setIsLoading(true);
    // Here you would fetch fresh data from your APIs/databases
    setTimeout(() => setIsLoading(false), 1000); // Simulating data fetch
  };

  const clearDateRange = () => {
    setDateRange(undefined);
  };

  return (
    <div className="container max-w-full px-4 py-6 space-y-6">
      {/* Header with date range picker */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-full">
            <BarChart4Icon className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Analytique</h1>
            <p className="text-sm text-muted-foreground">Aperçu et métriques de performance</p>
          </div>
        </div>
        
        <div className="flex flex-col md:flex-row gap-2">
          <DateRangePicker 
            value={dateRange}
            onChange={setDateRange}
          />
          {dateRange && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearDateRange}
            >
              Clear Date Filter
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCwIcon className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
            {isLoading ? "Actualisation..." : "Actualiser"}
          </Button>
        </div>
      </div>

      {/* Main Tabs */}
      <Tabs defaultValue="sales" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-3 md:w-auto w-full">
          <TabsTrigger value="sales" className="flex items-center gap-2">
            <ShoppingCartIcon className="h-4 w-4" />
            <span className="hidden md:inline">Ventes</span>
            <span className="md:hidden">🛒</span>
          </TabsTrigger>
          <TabsTrigger value="cogs" className="flex items-center gap-2">
            <DollarSignIcon className="h-4 w-4" />
            <span className="hidden md:inline">CMV</span>
            <span className="md:hidden">📉</span>
          </TabsTrigger>
          <TabsTrigger value="expenses" className="flex items-center gap-2">
            <ClipboardListIcon className="h-4 w-4" />
            <span className="hidden md:inline">Dépenses</span>
            <span className="md:hidden">🧾</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="sales" className="space-y-4">
          <SalesTab dateRange={dateRange} />
        </TabsContent>
        
        <TabsContent value="cogs" className="space-y-4">
          <CogsTab dateRange={dateRange} />
        </TabsContent>
        
        <TabsContent value="expenses" className="space-y-4">
          <ExpensesV4Tab dateRange={dateRange} />
        </TabsContent>
      </Tabs>


    </div>
  );
}