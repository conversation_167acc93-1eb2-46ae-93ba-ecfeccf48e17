"use client";

import { useEffect, useState, useRef } from "react";
import { PageHeader } from "@/components/ui/page-header";
import { QRCodeScanner } from "@/components/qr-code-scanner";
import { Capacitor } from "@capacitor/core";
import { Al<PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoIcon, CheckIcon, WifiOffIcon, ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { getRestaurantDbName } from "@/lib/db/db-utils";
import PouchDB from "pouchdb";
import { databaseV4 } from "@/lib/db/v4/core/db-instance";
import { useStaticNavigation } from '@/lib/utils/navigation';

// Define the connection info structure
interface ConnectionInfo {
  ip: string;
  port: number;
  deviceId: string;
  hostname?: string;
  restaurantId: string;
}

// Helper function to log and track sync progress
const sendP2PLog = (message: string) => {
  console.log(`📱 QR Scanner: ${message}`);
};

export default function QRScannerPage() {
  const router = useRouter();
  const { navigate } = useStaticNavigation();
  const [isNative, setIsNative] = useState(false);
  const [connectionInfo, setConnectionInfo] = useState<ConnectionInfo | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'connecting' | 'connected' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [syncStatus, setSyncStatus] = useState<{db: string, status: string, details?: any, timestamp: string}[]>([]);
  
  // Reference to store the sync handler for cleanup
  const syncHandlerRef = useRef<any>(null);
  
  useEffect(() => {
    // Check if running on a native platform
    setIsNative(Capacitor.isNativePlatform());
  }, []);
  
  // Cleanup function to stop sync when unmounting
  useEffect(() => {
    return () => {
      if (syncHandlerRef.current && typeof syncHandlerRef.current.cancel === 'function') {
        syncHandlerRef.current.cancel();
      }
    };
  }, []);
  
  const handleScanSuccess = async (data: string) => {
    try {
      // Parse the QR code data
      const parsedData = JSON.parse(data) as ConnectionInfo;
      console.log("✅ Scan successful:", parsedData);
      
      if (!parsedData.ip || !parsedData.port || !parsedData.deviceId || !parsedData.restaurantId) {
        throw new Error("Invalid QR code: Missing required connection information");
      }
      
      // Store the connection info
      setConnectionInfo(parsedData);
      setConnectionStatus('connecting');
      setErrorMessage(null);
      
      const dbName = getRestaurantDbName(parsedData.restaurantId);
      try {
        // Create or open local PouchDB instance for this restaurant
        // Fixed: Removed revs_limit option that causes HTTP adapter bugs with CouchDB
    const localDb = new PouchDB(dbName, { auto_compaction: true });
        console.log(`Using local PouchDB for ${dbName}`);
        
        // Create remote connection URL with correct authentication credentials
        const remoteUrl = `http://admin:admin@${parsedData.ip}:${parsedData.port}/${dbName}`;
        console.log(`Remote URL: ${remoteUrl.replace(/\/\/admin:admin@/, '//****:****@')}`); // Log URL without actual credentials
        
        // Test connection first with timeout and more diagnostic info
        try {
          console.log("Testing connection to CouchDB server...");
          sendP2PLog(`Testing connection to CouchDB at ${parsedData.ip}:${parsedData.port}`);
          
          // Attempt CORS preflight check with plain fetch first
          console.log(`Testing CORS with fetch to http://${parsedData.ip}:${parsedData.port}/`);
          try {
            // Try a CORS preflight check with added troubleshooting info
            sendP2PLog(`CORS check: Device IP=${parsedData.ip}, port=${parsedData.port}, using timeout=5000ms`);
            
            const response = await fetch(`http://${parsedData.ip}:${parsedData.port}/`, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Origin': window.location.origin
              },
              // Add timeout with AbortController
              signal: AbortSignal.timeout(5000)
            });
            
            if (response.ok) {
              const body = await response.text();
              console.log("Basic connectivity test successful!", body.substring(0, 100) + (body.length > 100 ? '...' : ''));
              sendP2PLog("✅ Basic connectivity test successful!");
            } else {
              console.warn(`Basic connectivity test returned status: ${response.status} ${response.statusText}`);
              sendP2PLog(`⚠️ Basic connectivity test returned status: ${response.status} ${response.statusText}`);
              
              if (response.status === 401) {
                sendP2PLog("⚠️ Authentication issues detected - will try with credentials");
              } else if (response.status === 404) {
                sendP2PLog("⚠️ Server endpoint not found - CouchDB server might be misconfigured");
              }
            }
          } catch (corsError: any) {
            console.warn(`CORS preflight check failed: ${corsError.message || corsError}`);
            sendP2PLog(`⚠️ CORS preflight check failed: ${corsError.message || corsError}`);
            
            // Add more detailed error logging based on error type
            if (corsError.name === 'AbortError') {
              sendP2PLog(`⚠️ Connection timed out during preflight check. Network might be unstable.`);
            } else if (corsError.message && corsError.message.includes('Failed to fetch')) {
              sendP2PLog(`⚠️ Network error - device might not be on the same network or has firewall`);
            } else if (corsError.message && corsError.message.includes('NetworkError')) {
              sendP2PLog(`⚠️ CORS policy error - CouchDB might need proper CORS configuration`);
            }
            
            // Continue anyway as PouchDB has its own connection handling
            sendP2PLog(`Will attempt PouchDB connection despite preflight issues`);
          }
          
          // Use PouchDB with a timeout for the actual connection test
          sendP2PLog(`Connecting to CouchDB via PouchDB with URL ${remoteUrl.replace(/\/\/admin:admin@/, '//****:****@')}`);
          
          const remoteDb = new PouchDB(remoteUrl, {
            skip_setup: false,
            // @ts-ignore PouchDB types don't include these options
            ajax: {
              timeout: 10000, // 10 second timeout
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
              }
            }
          });
          
          // Get db info with a manual timeout
          const dbInfoPromise = remoteDb.info();
          const timeoutPromise = new Promise((_, reject) => 
            setTimeout(() => reject(new Error("Connection timeout after 10 seconds")), 10000)
          );
          
          const dbInfo = await Promise.race([dbInfoPromise, timeoutPromise]);
          console.log("Connection successful! Database info:", dbInfo);
          sendP2PLog(`✅ PouchDB connection successful!`);
        } catch (connError: any) {
          console.error("Connection test failed:", connError);
          sendP2PLog(`❌ Connection test failed: ${connError.message || String(connError)}`);
          
          if (connError.status) {
            sendP2PLog(`HTTP status code: ${connError.status}`);
          }
          
          if (connError.reason) {
            sendP2PLog(`Error reason: ${connError.reason}`);
          }
          
          const errorMsg = connError.message || String(connError);
          
          // Provide more specific error messages based on error type
          if (errorMsg.includes('timeout') || errorMsg.includes('ETIMEDOUT')) {
            sendP2PLog(`❌ Connection timed out. Device may be unreachable.`);
            throw new Error(`Connection timed out. Please check that the device at ${parsedData.ip}:${parsedData.port} is reachable.`);
          } else if (errorMsg.includes('ECONNREFUSED')) {
            sendP2PLog(`❌ Connection refused. CouchDB might not be running.`);
            throw new Error(`Connection refused at ${parsedData.ip}:${parsedData.port}. Please check that CouchDB is running.`);
          } else if (connError.status === 401) {
            sendP2PLog(`❌ Authentication failed. Please check CouchDB credentials.`);
            throw new Error(`Authentication failed. Please check CouchDB credentials.`);
          } else if (errorMsg.includes('fetch') || errorMsg.includes('NetworkError')) {
            sendP2PLog(`❌ Network error connecting to ${parsedData.ip}:${parsedData.port}`);
            throw new Error(`Network error: Unable to connect to ${parsedData.ip}:${parsedData.port}. Make sure both devices are on the same network.`);
          } else if (errorMsg.includes('CORS')) {
            sendP2PLog(`❌ CORS policy error. CouchDB might need proper CORS headers.`);
            throw new Error(`CORS policy error: The connection was blocked by the browser. The CouchDB server might need proper CORS configuration.`);
          } else {
            sendP2PLog(`❌ General connection error: ${errorMsg}`);
            throw new Error(`Failed to connect: ${errorMsg}`);
          }
        }
        
        // Set up sync with proper options
        const syncOptions = {
          live: true,
          retry: true,
          // @ts-ignore PouchDB types don't include these options
          auth: {
            username: 'admin',
            password: 'admin'
          },
          // @ts-ignore PouchDB types don't include these options
          ajax: {
            timeout: 30000, // 30 second timeout for sync operations
          },
          batch_size: 50, // Process in smaller batches
          back_off_function: function (delay: number) {
            // Custom backoff strategy - cap at 1 minute max delay
            if (delay === 0) return 1000;
            return Math.min(delay * 1.5, 60000);
          }
        };
        
        // Start bidirectional sync
        // @ts-ignore PouchDB types don't match the actual behavior
        const syncHandler = PouchDB.sync(localDb, remoteUrl, syncOptions);
        
        // Store the sync handler in the ref for cleanup
        syncHandlerRef.current = syncHandler;

        // Debug: function to update syncStatus state
        function updateSyncStatus(db: string, status: string, details: any = {}) {
          const now = new Date();
          setSyncStatus(prev => {
            const filtered = prev.filter(s => s.db !== db);
            return [...filtered, { 
              db, 
              status, 
              details,
              timestamp: now.toISOString()
            }];
          });
        }

        // Only listen to real sync events
        // @ts-ignore PouchDB types are incomplete
        syncHandler.on('change', (info: any) => {
          console.log(`Sync change for ${dbName}:`, info);
          updateSyncStatus(dbName, 'change', {
            direction: info.direction,
            docs_read: info.change?.docs_read || 0,
            docs_written: info.change?.docs_written || 0,
            pending: info.change?.pending || 0,
            last_seq: info.change?.last_seq
          });
        });
        
        // @ts-ignore PouchDB types are incomplete
        syncHandler.on('paused', (info: any) => {
          console.log(`Sync paused for ${dbName}:`, info);
          updateSyncStatus(dbName, 'paused', {
            reason: info?.reason || 'No changes to sync'
          });
        });
        
        // @ts-ignore PouchDB types are incomplete
        syncHandler.on('active', (info: any) => {
          console.log(`Sync active for ${dbName}:`, info);
          updateSyncStatus(dbName, 'active', {
            message: 'Syncing changes...'
          });
        });
        
        // @ts-ignore PouchDB types are incomplete
        syncHandler.on('denied', (info: any) => {
          console.error(`Sync denied for ${dbName}:`, info);
          updateSyncStatus(dbName, 'denied', {
            reason: info?.reason || 'Permission denied',
            doc_id: info?.doc_id
          });
        });
        
        // @ts-ignore PouchDB types are incomplete
        syncHandler.on('complete', (info: any) => {
          console.log(`Sync complete for ${dbName}:`, info);
          updateSyncStatus(dbName, 'complete', {
            docs_read: info.docs_read || 0,
            docs_written: info.docs_written || 0,
            status: 'finished'
          });
        });
        
        // @ts-ignore PouchDB types are incomplete
        syncHandler.on('error', (err: any) => {
          console.error(`Sync error for ${dbName}:`, err);
          updateSyncStatus(dbName, 'error', {
            message: err.message || String(err),
            status: err.status
          });
          setConnectionStatus('error');
          setErrorMessage(`Sync error: ${err.message || err}`);
          toast.error('Sync error occurred', { duration: 3000 });
        });

        setConnectionStatus('connected');
        toast.success("Connected and syncing with device", { duration: 3000 });
      } catch (error) {
        console.error(`❌ Failed to initiate sync for ${dbName}:`, error);
        setConnectionStatus('error');
        setErrorMessage(
          `Failed to initiate sync for ${dbName}: ${error instanceof Error ? error.message : error}`
        );
        toast.error("Failed to connect to device", { duration: 3000 });
      }
      
    } catch (error) {
      console.error("❌ Failed to process QR data:", error);
      setConnectionStatus('error');
      setErrorMessage(error instanceof Error ? error.message : "Unknown error");
      toast.error("Invalid QR code format", { duration: 3000 });
    }
  };
  
  const handleViewP2PSyncPage = () => {
    router.push('/p2p-sync');
    navigate('p2p-sync');
  };
  
  const handleForceSync = async () => {
    if (!connectionInfo) return;
    
    try {
      // Set UI to show sync in progress
      setConnectionStatus('connecting');
      toast.info("Starting forced data sync...", { duration: 3000 });
      
      const dbName = getRestaurantDbName(connectionInfo.restaurantId);
      
      // Create PouchDB instances
      const localDb = new PouchDB(dbName, { auto_compaction: true });
      const remoteUrl = `http://admin:admin@${connectionInfo.ip}:${connectionInfo.port}/${dbName}`;
      
      // Set up one-time sync options (non-live)
      const syncOptions = {
        live: false,
        retry: true,
        // @ts-ignore PouchDB types don't include these options
        auth: {
          username: 'admin',
          password: 'admin'
        },
        // @ts-ignore PouchDB types don't include these options
        ajax: {
          timeout: 30000,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        }
      };
      
      // Update status to show we're doing a one-time pull
      setSyncStatus(prev => {
        return [...prev, {
          db: dbName,
          status: 'pulling',
          details: { message: 'Starting forced one-time pull...' },
          timestamp: new Date().toISOString()
        }];
      });
      
      // Execute a one-time pull
      console.log(`Starting forced one-time pull from ${connectionInfo.hostname || connectionInfo.deviceId}`);
      const pullResult = await localDb.replicate.from(remoteUrl, syncOptions);
      
      // Update status with results
      setSyncStatus(prev => {
        const filtered = prev.filter(s => !(s.db === dbName && s.status === 'pulling'));
        return [...filtered, {
          db: dbName,
          status: 'pull-success',
          details: {
            docs_read: pullResult.docs_read || 0,
            docs_written: pullResult.docs_written || 0,
            message: pullResult.docs_written > 0 
              ? `Sync successful! ${pullResult.docs_written} documents updated.` 
              : 'Sync completed. No changes needed.'
          },
          timestamp: new Date().toISOString()
        }];
      });
      
      setConnectionStatus('connected');
      toast.success(
        pullResult.docs_written > 0 
          ? `Sync successful! ${pullResult.docs_written} documents updated.` 
          : "Sync completed. No changes needed.", 
        { duration: 3000 }
      );
    } catch (error) {
      console.error("Force sync error:", error);
      setConnectionStatus('error');
      setErrorMessage(error instanceof Error ? error.message : "Unknown error during forced sync");
      toast.error("Sync failed. Check connection and try again.", { duration: 3000 });
    }
  };
  
  const renderConnectionStatus = () => {
    if (!connectionInfo) return null;
    
    return (
      <Card className="p-4 mb-6">
        <div className="flex flex-col space-y-4">
          <div className="flex items-center space-x-2">
            {connectionStatus === 'connected' ? (
              <CheckIcon className="h-5 w-5 text-green-500" />
            ) : connectionStatus === 'connecting' ? (
              <div className="h-5 w-5 rounded-full border-2 border-blue-500 border-t-transparent animate-spin" />
            ) : (
              <WifiOffIcon className="h-5 w-5 text-red-500" />
            )}
            <h3 className="text-lg font-medium">
              {connectionStatus === 'connected' ? 'Connected' : 
               connectionStatus === 'connecting' ? 'Connecting...' : 
               'Connection Failed'}
            </h3>
          </div>
          
          <div className="text-sm">
            <p>Device: {connectionInfo.hostname || connectionInfo.deviceId}</p>
            <p>IP: {connectionInfo.ip}:{connectionInfo.port}</p>
            <p>Database: {getRestaurantDbName(connectionInfo.restaurantId)}</p>
          </div>
          
          {connectionStatus === 'connected' && (
            <div className="mt-2">
              <h4 className="text-sm font-medium mb-1">Sync Status:</h4>
              <ul className="text-xs space-y-3">
                {syncStatus.map((status, idx) => (
                  <li key={idx} className="border-b pb-2">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className={`h-2 w-2 rounded-full ${
                        status.status === 'active' || status.status === 'change' || status.status === 'pull-success' ? 'bg-green-500' :
                        status.status === 'paused' ? 'bg-yellow-500' :
                        status.status === 'error' || status.status === 'denied' ? 'bg-red-500' :
                        status.status === 'pulling' ? 'bg-blue-500 animate-pulse' :
                        'bg-gray-500'
                      }`} />
                      <span className="font-semibold">{status.db}: {status.status}</span>
                    </div>
                    
                    {status.details && (
                      <div className="ml-4 text-xs text-slate-600">
                        {status.status === 'change' && (
                          <>
                            <p>Direction: {status.details.direction || 'N/A'}</p>
                            <p>Documents read: {status.details.docs_read}</p>
                            <p>Documents written: {status.details.docs_written}</p>
                            {status.details.pending > 0 && 
                              <p>Pending: {status.details.pending}</p>
                            }
                          </>
                        )}
                        
                        {status.status === 'paused' && (
                          <>
                            <p>Reason: {status.details.reason || 'Waiting for changes'}</p>
                            {status.details.pending && 
                              <p className="text-blue-500">{status.details.pending}</p>
                            }
                          </>
                        )}
                        
                        {status.status === 'active' && (
                          <>
                            <p>{status.details.message || 'Syncing...'}</p>
                            {status.details.pending && 
                              <p>Pending changes: {status.details.pending}</p>
                            }
                          </>
                        )}
                        
                        {status.status === 'error' && (
                          <p className="text-red-500">{status.details.message}</p>
                        )}
                        
                        {(status.status === 'complete' || status.status === 'initial-complete') && (
                          <>
                            <p>Documents read: {status.details.docs_read || status.details.pull?.docs_written}</p>
                            <p>Documents written: {status.details.docs_written || status.details.push?.docs_written}</p>
                          </>
                        )}
                        
                        {status.status === 'pulling' && (
                          <p className="text-blue-500">{status.details.message}</p>
                        )}
                        
                        {status.status === 'pull-success' && (
                          <>
                            <p>Documents read: {status.details.docs_read}</p>
                            <p>Documents written: {status.details.docs_written}</p>
                          </>
                        )}
                        
                        <p className="text-xs text-slate-400 mt-1">
                          Last updated: {new Date(status.timestamp).toLocaleTimeString()}
                        </p>
                      </div>
                    )}
                  </li>
                ))}
              </ul>
              
              {/* Add Force Sync button */}
              <Button 
                onClick={handleForceSync}
                className="mt-3 w-full bg-blue-500 hover:bg-blue-600 text-white font-medium text-sm rounded"
                size="sm"
              >
                Force One-Time Sync
              </Button>
            </div>
          )}
          
          {connectionStatus === 'error' && errorMessage && (
            <Alert variant="destructive" className="mt-2">
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
          )}
          
          <Button 
            onClick={handleViewP2PSyncPage} 
            variant="outline" 
            size="sm"
          >
            View Full Sync Status
          </Button>
        </div>
      </Card>
    );
  };
  
  return (
    <div className="container">
      {/* Go Back Button */}
      <button
        onClick={() => router.back()}
        className="flex items-center gap-2 text-sm text-blue-600 hover:underline mb-4"
      >
        <ArrowLeft className="h-4 w-4" />
        Back
      </button>
      <PageHeader 
        heading="QR Scanner" 
        description="Scan QR codes to connect to P2P devices" 
      />
      
      {!isNative && (
        <Alert className="mb-6">
          <InfoIcon className="h-4 w-4" />
          <AlertTitle>Native platform only</AlertTitle>
          <AlertDescription>
            The QR scanner is only available on native Android/iOS devices.
          </AlertDescription>
        </Alert>
      )}
      
      {renderConnectionStatus()}
      
      <div className="max-w-md mx-auto">
        <QRCodeScanner 
          onSuccess={handleScanSuccess}
        />
      </div>
    </div>
  );
} 