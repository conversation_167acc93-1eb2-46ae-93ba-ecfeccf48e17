'use client';

import React, { useState, useEffect } from 'react';
import { useP2PSync } from '../../hooks/use-p2p-sync';
import { useMobileP2PSync } from '../../lib/hooks/use-mobile-p2p-sync';
import { isMobileApp, getPlatformName } from '../../lib/utils/environment';
import { ArrowLeft, Info, Check, X, AlertTriangle, QrCode, Smartphone } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { PeerInfo } from '../../types/p2p-sync';
import { generateConnectionQRData } from '../../lib/p2p/utils';
import { getCurrentRestaurantId } from '../../lib/db/v4/utils/restaurant-id';
import { QRCodeSVG } from 'qrcode.react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';

// Interface for MDNSConfigurationDisplay props
interface MDNSConfigurationDisplayProps {
  isMobile: boolean;
  peers: PeerInfo[];
  mdnsStatus: 'not_running' | 'running' | 'error';
  logs: string[];
}

// Interface for mDNS configuration state
interface MDNSConfig {
  serviceType: string;
  serviceDomain: string;
  deviceId: string;
  serviceName: string;
  servicePort: string;
  platform: string;
  txtRecords: Record<string, string>;
  ipAddress: string;
}

// QR Code Generator Component for P2P sync
function QRCodeGenerator({ deviceId, ip, port, serviceName, restaurantId }: { 
  deviceId: string;
  ip: string;
  port: string;
  serviceName: string;
  restaurantId: string;
}) {
  const [qrData, setQrData] = useState('');
  
  useEffect(() => {
    if (deviceId && ip && port && restaurantId) {
      const connectionData = generateConnectionQRData(
        ip, 
        parseInt(port, 10), 
        deviceId,
        serviceName,
        restaurantId
      );
      setQrData(connectionData);
    }
  }, [deviceId, ip, port, serviceName, restaurantId]);
  
  if (!deviceId || !ip || port === 'Unknown') {
    return (
      <Card className="mt-6 bg-white">
        <CardHeader>
          <CardTitle className="flex items-center">
            <QrCode className="mr-2" size={18} />
            QR Code Connection
          </CardTitle>
          <CardDescription>
            Connect mobile devices by scanning a QR code
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-muted p-8 rounded-lg flex flex-col items-center justify-center">
            <AlertTriangle className="text-amber-500 mb-4" size={32} />
            <p className="text-sm text-center">
              Cannot generate QR code. Missing network information.
              <br />
              Make sure mDNS service is running.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className="mt-6 bg-white">
      <CardHeader>
        <CardTitle className="flex items-center">
          <QrCode className="mr-2" size={18} />
          QR Code Connection
        </CardTitle>
        <CardDescription>
          Connect mobile devices by scanning this QR code
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="bg-white p-6 rounded-lg flex flex-col items-center justify-center">
          <div className="p-4 bg-white rounded-lg shadow-sm border">
            <QRCodeSVG 
              value={qrData} 
              size={200}
              level="H"
            />
          </div>
          <div className="mt-4 text-sm text-center">
            <p className="font-medium">Scan with your mobile device</p>
            <p className="text-muted-foreground text-xs mt-1">
              IP: {ip}:{port} | ID: {deviceId.substring(0, 8)}...
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// MDNS Configuration Display Component
function MDNSConfigurationDisplay({ isMobile, peers, mdnsStatus, logs }: MDNSConfigurationDisplayProps) {
  // Extract important mDNS configuration details from logs
  const [mdnsConfig, setMdnsConfig] = useState<MDNSConfig>({
    serviceType: '_http._tcp.',
    serviceDomain: 'local.',
    deviceId: 'Unknown',
    serviceName: 'Unknown',
    servicePort: 'Unknown',
    platform: isMobile ? 'mobile' : 'desktop',
    txtRecords: {},
    ipAddress: 'Unknown'
  });

  useEffect(() => {
    // Parse logs to extract configuration information
    if (logs && logs.length > 0) {
      const newConfig = { ...mdnsConfig };
      
      // Look for device ID
      const deviceIdLog = logs.find((log: string) => 
        log.includes('device ID:') || 
        log.includes('ZeroConf initialized with device ID:') || 
        log.includes('SystemId:'));
      if (deviceIdLog) {
        const match = deviceIdLog.match(/[dD]evice ID:?\s*([a-f0-9\-]+)/i) || 
                      deviceIdLog.match(/SystemId:\s*([a-f0-9]+)/i) ||
                      deviceIdLog.match(/initialized with device ID:\s*([a-f0-9\-]+)/i);
        if (match && match[1]) {
          newConfig.deviceId = match[1];
        }
      }
      
      // Look for service name (supports mobile and desktop logs)
      const serviceNameLog = logs.find((log: string) => 
        log.includes('Service name:') ||
        log.toLowerCase().includes('published bonjour service:') ||
        log.toLowerCase().includes('published zeroconf service:')
      );
      if (serviceNameLog) {
        let match: RegExpMatchArray | null = null;
        // Match Published Bonjour service
        match = serviceNameLog.match(/Published Bonjour service:\s*([^\s,]+)/i);
        if (!match) {
          // Match Published ZeroConf service
          match = serviceNameLog.match(/Published ZeroConf service:\s*([^\s,]+)/i);
        }
        if (!match) {
          // Generic Service name:
          match = serviceNameLog.match(/[sS]ervice name:?\s*([^\s,]+)/i);
        }
        if (match && match[1]) {
          newConfig.serviceName = match[1];
        }
      }
      
      // Look for service port
      const portLog = logs.find((log: string) => 
        log.includes('Service port:') || /port[=:]?\s*\d+/i.test(log)
      );
      if (portLog) {
        const match = portLog.match(/Service port[=:]?\s*(\d+)/i)
                    || portLog.match(/port[=:]?\s*(\d+)/i);
        if (match && match[1]) {
          newConfig.servicePort = match[1];
        }
      }
      
      // Look for TXT records (desktop logs: 'TXT records: { ... }')
      const txtLog = logs.find((log: string) => 
        log.toLowerCase().includes('txt records:') || log.includes('props:')
      );
      if (txtLog) {
        try {
          const jsonMatch = txtLog.match(/\{[^}]+\}/);
          if (jsonMatch) {
            const txtJson = JSON.parse(jsonMatch[0]);
            newConfig.txtRecords = txtJson;
          }
        } catch (e) {
          console.error('Failed to parse TXT records from logs:', e);
        }
      }
      
      // Look for IP address in logs
      const ipLog = logs.find((log: string) => log.includes('Local IP:'));
      if (ipLog) {
        const match = ipLog.match(/Local IP:?\s*([\d\.]+)/i);
        if (match && match[1]) {
          newConfig.ipAddress = match[1];
        }
      } else if (peers && peers.length > 0) {
        // Use actual peer IP as fallback (mobile should display desktop peer's IP)
        newConfig.ipAddress = peers[0].ip;
      }
      
      setMdnsConfig(newConfig);
    }
  }, [logs, peers, isMobile]);

  // Function to check if a configuration value exists and is correct
  const getConfigStatus = (value: string | object, defaultValue = 'Unknown'): 'ok' | 'missing' | 'check' => {
    if (!value || value === defaultValue || (typeof value === 'object' && Object.keys(value).length === 0)) {
      return 'missing';
    }
    if (typeof value === 'string' && value.includes('Unknown')) {
      return 'check';
    }
    return 'ok';
  };

  return (
    <div className="mt-6 border rounded-lg overflow-hidden bg-white shadow-sm">
      <div className="border-b p-4 flex justify-between items-center">
        <h2 className="text-lg font-semibold">Required mDNS Configuration</h2>
        <div className="flex items-center text-xs text-gray-500">
          <Info size={14} className="mr-1" />
          All these fields must match between devices
        </div>
      </div>
      <div className="p-4">
        <div className="grid grid-cols-12 gap-4">
          <div className="col-span-4 font-medium">Field</div>
          <div className="col-span-6 font-medium">Value</div>
          <div className="col-span-2 font-medium">Status</div>
        </div>
        
        {/* Service Type */}
        <div className="grid grid-cols-12 gap-4 border-t mt-2 pt-2">
          <div className="col-span-4 flex items-center">
            <span className="font-mono text-sm">SERVICE_TYPE</span>
          </div>
          <div className="col-span-6 flex items-center">
            <code className="bg-slate-100 px-2 py-1 rounded text-sm">{mdnsConfig.serviceType}</code>
          </div>
          <div className="col-span-2">
            {getConfigStatus(mdnsConfig.serviceType) === 'ok' ? (
              <span className="text-green-600 flex items-center">
                <Check size={16} className="mr-1" /> OK
              </span>
            ) : (
              <span className="text-red-600 flex items-center">
                <X size={16} className="mr-1" /> Missing
              </span>
            )}
          </div>
        </div>
        
        {/* Service Domain */}
        <div className="grid grid-cols-12 gap-4 border-t mt-2 pt-2">
          <div className="col-span-4 flex items-center">
            <span className="font-mono text-sm">SERVICE_DOMAIN</span>
          </div>
          <div className="col-span-6 flex items-center">
            <code className="bg-slate-100 px-2 py-1 rounded text-sm">{mdnsConfig.serviceDomain}</code>
          </div>
          <div className="col-span-2">
            {getConfigStatus(mdnsConfig.serviceDomain) === 'ok' ? (
              <span className="text-green-600 flex items-center">
                <Check size={16} className="mr-1" /> OK
              </span>
            ) : (
              <span className="text-red-600 flex items-center">
                <X size={16} className="mr-1" /> Missing
              </span>
            )}
          </div>
        </div>
        
        {/* Device ID */}
        <div className="grid grid-cols-12 gap-4 border-t mt-2 pt-2">
          <div className="col-span-4 flex items-center">
            <span className="font-mono text-sm">deviceId</span>
          </div>
          <div className="col-span-6 flex items-center">
            <code className="bg-slate-100 px-2 py-1 rounded text-sm">{mdnsConfig.deviceId}</code>
          </div>
          <div className="col-span-2">
            {getConfigStatus(mdnsConfig.deviceId) === 'ok' ? (
              <span className="text-green-600 flex items-center">
                <Check size={16} className="mr-1" /> OK
              </span>
            ) : (
              <span className="text-red-600 flex items-center">
                <X size={16} className="mr-1" /> Missing
              </span>
            )}
          </div>
        </div>
        
        {/* Service Name */}
        <div className="grid grid-cols-12 gap-4 border-t mt-2 pt-2">
          <div className="col-span-4 flex items-center">
            <span className="font-mono text-sm">serviceName</span>
          </div>
          <div className="col-span-6 flex items-center">
            <code className="bg-slate-100 px-2 py-1 rounded text-sm">{mdnsConfig.serviceName}</code>
          </div>
          <div className="col-span-2">
            {getConfigStatus(mdnsConfig.serviceName) === 'ok' ? (
              <span className="text-green-600 flex items-center">
                <Check size={16} className="mr-1" /> OK
              </span>
            ) : (
              <span className="text-red-600 flex items-center">
                <X size={16} className="mr-1" /> Missing
              </span>
            )}
          </div>
        </div>
        
        {/* Service Port */}
        <div className="grid grid-cols-12 gap-4 border-t mt-2 pt-2">
          <div className="col-span-4 flex items-center">
            <span className="font-mono text-sm">port</span>
          </div>
          <div className="col-span-6 flex items-center">
            <code className="bg-slate-100 px-2 py-1 rounded text-sm">{mdnsConfig.servicePort}</code>
          </div>
          <div className="col-span-2">
            {getConfigStatus(mdnsConfig.servicePort) === 'ok' ? (
              <span className="text-green-600 flex items-center">
                <Check size={16} className="mr-1" /> OK
              </span>
            ) : (
              <span className="text-yellow-600 flex items-center">
                <AlertTriangle size={16} className="mr-1" /> Check
              </span>
            )}
          </div>
        </div>
        
        {/* TXT Records */}
        <div className="grid grid-cols-12 gap-4 border-t mt-2 pt-2">
          <div className="col-span-4 flex items-center">
            <span className="font-mono text-sm">{isMobile ? 'props' : 'txt'}</span>
          </div>
          <div className="col-span-6 flex items-center">
            <code className="bg-slate-100 px-2 py-1 rounded text-sm whitespace-pre-wrap break-all">
              {JSON.stringify(mdnsConfig.txtRecords, null, 2) || '{}'}
            </code>
          </div>
          <div className="col-span-2">
            {Object.keys(mdnsConfig.txtRecords).length > 0 ? (
              <span className="text-green-600 flex items-center">
                <Check size={16} className="mr-1" /> OK
              </span>
            ) : (
              <span className="text-yellow-600 flex items-center">
                <AlertTriangle size={16} className="mr-1" /> Check
              </span>
            )}
          </div>
        </div>
        
        {/* Platform */}
        <div className="grid grid-cols-12 gap-4 border-t mt-2 pt-2">
          <div className="col-span-4 flex items-center">
            <span className="font-mono text-sm">platform</span>
          </div>
          <div className="col-span-6 flex items-center">
            <code className="bg-slate-100 px-2 py-1 rounded text-sm">{mdnsConfig.platform}</code>
          </div>
          <div className="col-span-2">
            {getConfigStatus(mdnsConfig.platform) === 'ok' ? (
              <span className="text-green-600 flex items-center">
                <Check size={16} className="mr-1" /> OK
              </span>
            ) : (
              <span className="text-red-600 flex items-center">
                <X size={16} className="mr-1" /> Missing
              </span>
            )}
          </div>
        </div>
        
        {/* Network Status / IP */}
        <div className="grid grid-cols-12 gap-4 border-t mt-2 pt-2">
          <div className="col-span-4 flex items-center">
            <span className="font-mono text-sm">IP Address</span>
          </div>
          <div className="col-span-6 flex items-center">
            <code className="bg-slate-100 px-2 py-1 rounded text-sm">{mdnsConfig.ipAddress}</code>
          </div>
          <div className="col-span-2">
            {getConfigStatus(mdnsConfig.ipAddress) === 'ok' ? (
              <span className="text-green-600 flex items-center">
                <Check size={16} className="mr-1" /> OK
              </span>
            ) : (
              <span className="text-yellow-600 flex items-center">
                <AlertTriangle size={16} className="mr-1" /> Check
              </span>
            )}
          </div>
        </div>
        
        {/* mDNS Status */}
        <div className="grid grid-cols-12 gap-4 border-t mt-2 pt-2">
          <div className="col-span-4 flex items-center">
            <span className="font-mono text-sm">mDNS Status</span>
          </div>
          <div className="col-span-6 flex items-center">
            <code className={`px-2 py-1 rounded text-sm ${
              mdnsStatus === 'running' ? 'bg-green-100 text-green-800' : 
              mdnsStatus === 'error' ? 'bg-red-100 text-red-800' : 
              'bg-yellow-100 text-yellow-800'
            }`}>
              {mdnsStatus}
            </code>
          </div>
          <div className="col-span-2">
            {mdnsStatus === 'running' ? (
              <span className="text-green-600 flex items-center">
                <Check size={16} className="mr-1" /> OK
              </span>
            ) : mdnsStatus === 'error' ? (
              <span className="text-red-600 flex items-center">
                <X size={16} className="mr-1" /> Error
              </span>
            ) : (
              <span className="text-yellow-600 flex items-center">
                <AlertTriangle size={16} className="mr-1" /> Not Running
              </span>
            )}
          </div>
        </div>
        
        <div className="mt-4 text-sm p-2 bg-blue-50 border border-blue-200 rounded-md">
          <p className="font-medium text-blue-800">How to check for proper connectivity:</p>
          <ol className="list-decimal ml-4 mt-1 text-blue-700 space-y-1">
            <li>Both devices must show <strong>mdnsStatus = "running"</strong></li>
            <li>SERVICE_TYPE and SERVICE_DOMAIN must match exactly</li>
            <li>Both devices must have unique deviceId values</li>
            <li>For mobile, TXT records must include platform="mobile" and id="[yourDeviceId]"</li>
            <li>For desktop, TXT records must include platform="desktop" and id="[yourDeviceId]"</li>
            <li>Both devices must be on the same network and subnet</li>
          </ol>
        </div>
      </div>
    </div>
  );
}

function P2PSyncMonitorPage() {
  const router = useRouter();
  const [isMobile, setIsMobile] = useState(false);
  const [platform, setPlatform] = useState('unknown');
  const [mdnsConfig, setMdnsConfig] = useState<MDNSConfig>({
    serviceType: '_http._tcp.',
    serviceDomain: 'local.',
    deviceId: 'Unknown',
    serviceName: 'Unknown',
    servicePort: 'Unknown',
    platform: 'unknown',
    txtRecords: {},
    ipAddress: 'Unknown'
  });
  
  useEffect(() => {
    setIsMobile(isMobileApp());
    setPlatform(getPlatformName());
  }, []);
  
  // Use the appropriate P2P system based on platform
  const desktopSystem = useP2PSync();
  const mobileSystem = useMobileP2PSync(null, 8000);
  
  // Choose which system to display
  const activeSystem = isMobile ? mobileSystem : desktopSystem;
  
  // Extract common properties
  const { 
    isInitialized, 
    mdnsStatus, 
    logs = [], 
    peers = [] 
  } = activeSystem;
  
  // System-specific status data
  const [systemDetails, setSystemDetails] = useState<{ [key: string]: any }>({});

  // Add QR connection config state and parse logs for desktop environment
  const [qrConfig, setQrConfig] = useState<{ deviceId: string; ip: string; port: string; serviceName: string }>({
    deviceId: '',
    ip: '',
    port: '',
    serviceName: ''
  });

  useEffect(() => {
    if (!isMobile) {
      let deviceId = '';
      let ipAddress = '';
      let servicePort = '';
      let serviceName = '';

      const systemIdLog = logs.find(log => log.includes('SystemId:'));
      if (systemIdLog) {
        const match = systemIdLog.match(/SystemId:?\s*([a-f0-9-]+)/i);
        if (match) deviceId = match[1];
      }
      const portLog = logs.find(log => log.includes('Service port:'));
      if (portLog) {
        const match = portLog.match(/Service port[=:]?\s*(\d+)/i);
        if (match) servicePort = match[1];
      }
      const svcNameLog = logs.find(log => log.includes('Service name:'));
      if (svcNameLog) {
        const match = svcNameLog.match(/Service name[=:]?\s*([^\s,]+)/i);
        if (match) serviceName = match[1];
      }
      const ipLog = logs.find(log => log.includes('Local IP:'));
      if (ipLog) {
        const match = ipLog.match(/Local IP:?\s*([\d.]+)/i);
        if (match) ipAddress = match[1];
      }
      if (deviceId && ipAddress && servicePort && serviceName) {
        setQrConfig({ deviceId, ip: ipAddress, port: servicePort, serviceName });
      }
    }
  }, [logs, isMobile]);

  // Poll various system details for diagnostics
  useEffect(() => {
    // Gather system-specific diagnostic data
    const gatherSystemDetails = () => {
      const details: { [key: string]: any } = {
        timestamp: new Date().toISOString(),
        platform,
        isMobileApp: isMobile,
        detectedPeers: peers.length,
        mdnsStatus
      };
      
      // Add platform-specific details
      if (isMobile) {
        details.mobileSpecific = {
          discoveryInitialized: mobileSystem.isInitialized,
          initSteps: mobileSystem.initSteps || [],
        };
      } else {
        details.desktopSpecific = {
          electronDetected: desktopSystem.isElectron,
          activeSyncs: desktopSystem.syncStatuses?.length || 0,
        };
      }
      
      setSystemDetails(details);
    };
    
    // Immediately gather details
    gatherSystemDetails();
    
    // Set up polling
    const interval = setInterval(gatherSystemDetails, 2000);
    
    return () => clearInterval(interval);
  }, [
    platform,
    isMobile,
    peers.length,
    mdnsStatus,
    mobileSystem.isInitialized,
    // Only use .length or primitive values in deps
    Array.isArray(mobileSystem.initSteps) ? mobileSystem.initSteps.length : 0,
    desktopSystem.isElectron,
    Array.isArray(desktopSystem.syncStatuses) ? desktopSystem.syncStatuses.length : 0
  ]);

  // Helper functions for UI
  const getMdnsStatusUI = () => {
    switch (mdnsStatus) {
      case 'running':
        return (
          <div className="p-3 bg-green-100 border border-green-300 rounded-md flex items-center">
            <div className="h-4 w-4 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            <span className="font-medium text-green-800">
              mDNS is running and actively broadcasting
            </span>
          </div>
        );
      case 'error':
        return (
          <div className="p-3 bg-red-100 border border-red-300 rounded-md flex items-center">
            <div className="h-4 w-4 bg-red-500 rounded-full mr-2"></div>
            <span className="font-medium text-red-800">
              mDNS encountered an error
            </span>
          </div>
        );
      case 'not_running':
      default:
        return (
          <div className="p-3 bg-yellow-100 border border-yellow-300 rounded-md flex items-center">
            <div className="h-4 w-4 bg-yellow-500 rounded-full mr-2"></div>
            <span className="font-medium text-yellow-800">
              mDNS is not running
            </span>
          </div>
        );
    }
  };

  // Requirements check UI - show what's needed for mDNS to work
  const renderRequirementsCheck = () => {
    const requirements = [
      {
        name: "Bonjour/mDNS Support",
        met: mdnsStatus === 'running',
        importance: "critical",
        info: isMobile ? "ZeroConf Plugin" : "Bonjour Service"
      },
      {
        name: "Network Connection",
        met: peers.length > 0 || mdnsStatus === 'running',
        importance: "critical",
        info: "Connected to a local network"
      },
      {
        name: "Platform Support",
        met: isMobile ? true : desktopSystem.isElectron,
        importance: "critical",
        info: isMobile ? "Mobile App" : "Desktop App"
      },
      {
        name: "System Initialized",
        met: isInitialized,
        importance: "critical",
        info: "P2P sync system started"
      }
    ];

    return (
      <div className="mt-4 border rounded-md overflow-hidden">
        <div className="bg-slate-100 p-2 font-medium">System Requirements</div>
        <div className="divide-y">
          {requirements.map((req, idx) => (
            <div key={idx} className="flex items-center justify-between p-2">
              <div className="flex items-center">
                <div className={`h-3 w-3 rounded-full mr-2 ${req.met ? 'bg-green-500' : req.importance === 'critical' ? 'bg-red-500' : 'bg-yellow-500'}`}></div>
                <span>{req.name}</span>
              </div>
              <div className="text-sm text-gray-500">{req.info}</div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderNetworkDetails = () => {
    return (
      <div className="mt-4 border rounded-md overflow-hidden">
        <div className="bg-slate-100 p-2 font-medium">Network Diagnostics</div>
        <div className="p-3">
          <p className="text-sm mb-2">Detected network conditions:</p>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>Platform:</div>
            <div className="font-medium">{platform}</div>
            
            <div>Discovered Peers:</div>
            <div className="font-medium">{peers.length}</div>
            
            <div>mDNS Status:</div>
            <div className={`font-medium ${
              mdnsStatus === 'running' ? 'text-green-600' : 
              mdnsStatus === 'error' ? 'text-red-600' : 'text-yellow-600'
            }`}>{mdnsStatus}</div>
          </div>
          
          <div className="mt-3 text-xs text-gray-600">
            <p className="font-medium mb-1">Troubleshooting tips:</p>
            <ul className="list-disc ml-4 space-y-1">
              <li>Ensure both devices are on the same WiFi network</li>
              <li>Some networks block mDNS traffic (especially enterprise/public WiFi)</li>
              <li>Try disabling any active VPNs</li>
              <li>On mobile, verify app has required network permissions</li>
              <li>Restart both applications if status shows running but no peers detected</li>
            </ul>
          </div>
        </div>
      </div>
    );
  };

  // Update QR code render to use qrConfig
  const renderQRCodeSection = () => {
    if (isMobile) return null;
    const { deviceId, ip, port, serviceName } = qrConfig;
    const restaurantId = getCurrentRestaurantId();
    if (!deviceId || !ip || !port || !serviceName || !restaurantId) return null;
    return (
      <QRCodeGenerator 
        deviceId={deviceId}
        ip={ip}
        port={port}
        serviceName={serviceName}
        restaurantId={restaurantId}
      />
    );
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-3xl">
      {/* Navigation */}
      <button
        onClick={() => router.back()}
        className="mb-4 flex items-center text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
        type="button"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Go Back
      </button>
      
      {/* Title */}
      <h1 className="text-2xl font-bold mb-2">mDNS Status Monitor</h1>
      <p className="text-gray-600 mb-6">
        Real-time monitoring of your local network discovery service
      </p>
      
      {/* Platform Indicator */}
      <div className="mb-6 flex items-center text-sm text-gray-500">
        {isMobile ? (
          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-md font-medium">📱 Mobile Client Mode</span>
        ) : (
          <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-md font-medium">🖥️ Desktop Hub Mode</span>
        )}
        <span className="ml-2">Running on {platform}</span>
      </div>
      
      {/* Main Status Card */}
      <div className="mb-6 border rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="border-b p-4">
          <h2 className="text-lg font-semibold">mDNS Status</h2>
        </div>
        <div className="p-4">
          {getMdnsStatusUI()}
          
          {/* Requirements Check */}
          {renderRequirementsCheck()}
          
          {/* Discovered Peers */}
          <div className="mt-4">
            <h3 className="font-semibold mb-2">Discovered Peers ({peers.length})</h3>
            {peers.length === 0 ? (
              <div className="text-sm text-gray-500 italic">
                No peers discovered yet. Ensure other devices are on the same network.
              </div>
            ) : (
              <div className="border rounded-md overflow-hidden">
                <div className="grid grid-cols-12 gap-2 bg-slate-100 p-2 text-sm font-medium">
                  <div className="col-span-6">Name/ID</div>
                  <div className="col-span-4">IP Address</div>
                  <div className="col-span-2">Port</div>
                </div>
                <div className="divide-y">
                  {peers.map((peer, idx) => (
                    <div key={idx} className="grid grid-cols-12 gap-2 p-2 text-sm">
                      <div className="col-span-6 truncate" title={`${peer.hostname || 'Unknown'} (${peer.id})`}>
                        {peer.hostname || peer.id.substring(0, 8)}
                      </div>
                      <div className="col-span-4">{peer.ip}</div>
                      <div className="col-span-2">{peer.port}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          {/* System-specific Diagnostics */}
          <div className="mt-4">
            <h3 className="font-semibold mb-2">System Diagnostics</h3>
            <div className="bg-slate-50 p-3 rounded-md border text-sm font-mono overflow-x-auto">
              <pre>{JSON.stringify(systemDetails, null, 2)}</pre>
            </div>
          </div>
          
          {renderNetworkDetails()}
          
          {/* Add the QR code generator for desktop devices */}
          {renderQRCodeSection()}
        </div>
      </div>
      
      {/* Add the new mDNS Configuration Display component here */}
      <MDNSConfigurationDisplay 
        isMobile={isMobile} 
        peers={peers} 
        mdnsStatus={mdnsStatus} 
        logs={logs} 
      />
      
      {/* Log Panel */}
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="border-b p-4 flex justify-between items-center">
          <h2 className="text-lg font-semibold">mDNS System Logs</h2>
          <span className="text-xs text-gray-500">Latest logs appear at the top</span>
        </div>
        <div className="p-4 max-h-[400px] overflow-y-auto bg-slate-50">
          {logs.length === 0 ? (
            <div className="text-center p-6 text-gray-500">
              No logs available yet
            </div>
          ) : (
            <div className="space-y-1 text-sm font-mono">
              {logs.map((log, idx) => (
                <div key={idx} className="break-all">
                  {log}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Troubleshooting Section */}
      <div className="mt-6 border rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="border-b p-4">
          <h2 className="text-lg font-semibold">Troubleshooting mDNS</h2>
        </div>
        <div className="p-4">
          <h3 className="font-semibold mb-2">Common Issues</h3>
          <ul className="list-disc pl-5 space-y-2 text-sm">
            <li><strong>Devices can't discover each other:</strong> Ensure all devices are on the same WiFi network and subnet.</li>
            <li><strong>mDNS Status shows "error":</strong> Check your firewall settings and ensure ports 5353 (UDP) for mDNS are open.</li>
            <li><strong>Mobile device can't find desktop:</strong> Mobile clients can only connect to desktop hubs, not other mobile clients.</li>
            <li><strong>Desktop doesn't show Status = Running:</strong> Ensure the Bonjour service is running on your computer.</li>
          </ul>

          <h3 className="font-semibold mt-4 mb-2">Required Network Conditions</h3>
          <ul className="list-disc pl-5 space-y-1 text-sm">
            <li>Devices must be on the same local WiFi network</li>
            <li>Some corporate networks block mDNS traffic</li>
            <li>Mobile hotspot connections generally work well</li>
            <li>Network must allow UDP traffic on port 5353</li>
            <li>Network must allow the service ports (usually 8000-9000 range)</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

export default P2PSyncMonitorPage; 