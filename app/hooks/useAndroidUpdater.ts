import { useEffect, useState } from 'react';
import { Capacitor } from '@capacitor/core';
import { AppUpdate } from '@capawesome/capacitor-app-update';

interface UpdateInfo {
  version: string;
  url: string;
  releaseNotes?: string;
  mandatory?: boolean;
}

interface UpdateState {
  isChecking: boolean;
  updateAvailable: boolean;
  updateInfo: UpdateInfo | null;
  isDownloading: boolean;
  downloadProgress: number;
  error: string | null;
}

// TODO: Replace with your actual R2 bucket URL
const UPDATE_CHECK_URL = process.env.NEXT_PUBLIC_ANDROID_UPDATE_URL || 'https://your-r2-bucket.com/android-update.json';

export function useAndroidUpdater() {
  const [state, setState] = useState<UpdateState>({
    isChecking: false,
    updateAvailable: false,
    updateInfo: null,
    isDownloading: false,
    downloadProgress: 0,
    error: null,
  });

  // Only run on Android platform
  const isAndroid = Capacitor.getPlatform() === 'android';

  const getCurrentVersion = async (): Promise<string> => {
    try {
      const info = await AppUpdate.getAppUpdateInfo();
      return info.currentVersion;
    } catch (error) {
      console.error('Failed to get current version:', error);
      return '1.0.0'; // Fallback version
    }
  };

  const checkForUpdates = async (): Promise<void> => {
    if (!isAndroid) return;

    // Skip update check if URL is not configured
    if (UPDATE_CHECK_URL.includes('your-r2-bucket.com')) {
      console.log('Update check skipped: R2 URL not configured yet');
      return;
    }

    setState(prev => ({ ...prev, isChecking: true, error: null }));

    try {
      // Get current app version
      const currentVersion = await getCurrentVersion();

      // Fetch update info from R2 with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(UPDATE_CHECK_URL, {
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Update check failed: ${response.status}`);
      }

      const updateInfo: UpdateInfo = await response.json();

      // Compare versions (simple string comparison for now)
      const updateAvailable = updateInfo.version !== currentVersion;

      setState(prev => ({
        ...prev,
        isChecking: false,
        updateAvailable,
        updateInfo: updateAvailable ? updateInfo : null,
      }));

      console.log(`Version check: Current=${currentVersion}, Latest=${updateInfo.version}, Update=${updateAvailable}`);

    } catch (error) {
      console.warn('Update check failed (this is normal if R2 is not set up yet):', error);
      setState(prev => ({
        ...prev,
        isChecking: false,
        error: null, // Don't show error to user for failed update checks
      }));
    }
  };

  const downloadAndInstallUpdate = async (): Promise<void> => {
    if (!isAndroid || !state.updateInfo) return;

    setState(prev => ({ ...prev, isDownloading: true, downloadProgress: 0, error: null }));

    try {
      // Start the update download
      await AppUpdate.startFlexibleUpdate();
      
      // Monitor download progress
      const progressListener = await AppUpdate.addListener('onFlexibleUpdateStateUpdate', (state) => {
        if (state.installStatus === 'DOWNLOADING') {
          const progress = Math.round((state.bytesDownloaded / state.totalBytesToDownload) * 100);
          setState(prev => ({ ...prev, downloadProgress: progress }));
        } else if (state.installStatus === 'DOWNLOADED') {
          setState(prev => ({ ...prev, isDownloading: false, downloadProgress: 100 }));
          // Complete the installation
          AppUpdate.completeFlexibleUpdate();
        }
      });

      // Clean up listener after use
      setTimeout(() => {
        progressListener.remove();
      }, 30000); // 30 second timeout

    } catch (error) {
      console.error('Update download failed:', error);
      setState(prev => ({
        ...prev,
        isDownloading: false,
        error: error instanceof Error ? error.message : 'Failed to download update',
      }));
    }
  };

  const installImmediateUpdate = async (): Promise<void> => {
    if (!isAndroid || !state.updateInfo) return;

    try {
      // For immediate updates, the app will restart automatically
      await AppUpdate.performImmediateUpdate();
    } catch (error) {
      console.error('Immediate update failed:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to install update',
      }));
    }
  };

  // Auto-check for updates on mount (with delay to avoid blocking app startup)
  useEffect(() => {
    if (isAndroid) {
      // Delay update check by 5 seconds to let app start properly
      const timeoutId = setTimeout(() => {
        checkForUpdates();
      }, 5000);

      return () => clearTimeout(timeoutId);
    }
  }, [isAndroid]);

  return {
    ...state,
    isAndroid,
    checkForUpdates,
    downloadAndInstallUpdate,
    installImmediateUpdate,
  };
}
