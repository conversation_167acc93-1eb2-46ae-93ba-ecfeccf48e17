'use client';

import { Suspense, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useStaticNavigation } from '@/lib/utils/navigation';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

function AuthErrorContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { navigate } = useStaticNavigation();
  const [error, setError] = useState<string>("");
  
  useEffect(() => {
    // Get error from URL parameters
    const errorParam = searchParams?.get('error');
    if (errorParam) {
      // Map error codes to user-friendly messages
      switch (errorParam) {
        case 'CredentialsSignin':
          setError('Invalid email or password');
          break;
        case 'SessionRequired':
          setError('You need to be signed in to access this page');
          break;
        default:
          setError(errorParam);
      }
    } else {
      setError('An unknown authentication error occurred');
    }
  }, [searchParams]);

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <Link
        href="/auth"
        className="absolute left-4 top-4 md:left-8 md:top-8 flex items-center text-sm font-medium text-muted-foreground hover:text-primary"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to login
      </Link>
      
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Authentication Error</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-destructive">Login Failed</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
          </CardContent>
          <CardFooter>
            <Button className="w-full" onClick={() => navigate('auth')}>
              Return to Login
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}

export default function AuthError() {
  return (
    <Suspense fallback={
      <div className="container flex h-screen w-screen flex-col items-center justify-center">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <Card>
            <CardContent className="p-6">
              <p className="text-center">Loading...</p>
            </CardContent>
          </Card>
        </div>
      </div>
    }>
      <AuthErrorContent />
    </Suspense>
  );
} 