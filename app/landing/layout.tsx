'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";

// Almarai - bold, distinctive Arabic font with excellent readability
const almarai = Almarai({
  subsets: ["arabic"],
  weight: ["300", "400", "700", "800"],
  display: "swap",
  variable: "--font-almarai",
});

// Tajawal - elegant, modern Arabic font with beautiful curves
const tajawal = Tajawal({
  subsets: ["arabic"],
  weight: ["300", "400", "500", "700", "800", "900"],
  display: "swap",
  variable: "--font-tajawal",
});

// Changa - bold, distinctive Arabic font for strong headings
const changa = Changa({
  subsets: ["arabic"],
  weight: ["300", "400", "500", "600", "700", "800"],
  display: "swap",
  variable: "--font-changa",
});

export default function LandingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className={`${almarai.variable} ${tajawal.variable} ${changa.variable} landing-page`} dir="rtl">
      <style jsx global>{`
        .landing-page h1 {
          font-family: var(--font-changa), sans-serif;
          font-weight: 800;
          letter-spacing: -0.02em;
        }

        .landing-page h2 {
          font-family: var(--font-changa), sans-serif;
          font-weight: 700;
        }

        .landing-page {
          font-family: var(--font-almarai), sans-serif;
        }

        .landing-page .body-text {
          font-family: var(--font-tajawal), sans-serif;
          line-height: 1.8;
        }
      `}</style>
      {children}
    </div>
  );
}