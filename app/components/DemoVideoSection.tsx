'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';

/**
 * 🎬 Demo Video Section Component
 * 
 * Uses the same R2Service pattern as Windows downloads
 * Fetches signed URLs for secure video streaming
 */

interface VideoUrls {
  mp4?: string;
  webm?: string;
}

export default function DemoVideoSection() {
  const [videoUrls, setVideoUrls] = useState<VideoUrls>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchVideoUrls();
  }, []);

  const fetchVideoUrls = async () => {
    try {
      console.log('🎬 Fetching demo video URLs...');
      
      const response = await fetch('/api/demo-video/urls');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setVideoUrls(data.urls);
        console.log('✅ Video URLs loaded successfully');
      } else {
        throw new Error(data.message || 'Failed to load video URLs');
      }
    } catch (err) {
      console.error('❌ Failed to fetch video URLs:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4, ease: 'easeOut' }}
        className="mt-12 w-full max-w-6xl mx-auto"
      >
        <div className="bg-muted rounded-lg p-8 text-center animate-pulse">
          <div className="w-full h-64 bg-muted-foreground/20 rounded-lg mb-4"></div>
          <p className="text-muted-foreground">🎬 Loading demo video...</p>
        </div>
      </motion.div>
    );
  }

  if (error || (!videoUrls.mp4 && !videoUrls.webm)) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4, ease: 'easeOut' }}
        className="mt-12 w-full max-w-6xl mx-auto"
      >
        <div className="bg-muted rounded-lg p-8 text-center">
          <p className="text-muted-foreground mb-4">🎬 Demo video not available</p>
          {error && (
            <p className="text-sm text-red-500">
              Error: {error}
            </p>
          )}
          <button 
            onClick={fetchVideoUrls}
            className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            🔄 Retry
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.4, ease: 'easeOut' }}
      className="mt-12 w-full max-w-6xl mx-auto"
    >
      <video 
        className="w-full h-auto rounded-lg shadow-2xl"
        autoPlay
        muted
        loop
        playsInline
        preload="auto"
        style={{ pointerEvents: 'none' }}
        onLoadStart={() => console.log('🎬 Video loading started')}
        onCanPlay={() => console.log('✅ Video can play')}
        onPlay={() => console.log('▶️ Video started playing')}
        onError={(e) => {
          console.error('❌ Video error:', e);
          console.error('Video src:', e.currentTarget.src);
          setError('Video playback failed');
        }}
        onLoadedData={() => console.log('📊 Video data loaded')}
        onLoadedMetadata={() => console.log('📋 Video metadata loaded')}
      >
        {videoUrls.mp4 && (
          <source 
            src={videoUrls.mp4}
            type="video/mp4" 
            onError={() => {
              console.error('❌ MP4 source failed:', videoUrls.mp4);
            }}
          />
        )}
        {videoUrls.webm && (
          <source 
            src={videoUrls.webm}
            type="video/webm" 
            onError={() => {
              console.error('❌ WebM source failed:', videoUrls.webm);
            }}
          />
        )}
        {/* Fallback content */}
        <div className="bg-muted rounded-lg p-8 text-center">
          <p className="text-muted-foreground mb-4">🎬 Your browser doesn't support video playback</p>
          <p className="text-sm text-muted-foreground">
            Please try a modern browser like Chrome, Firefox, or Safari
          </p>
        </div>
      </video>
    </motion.div>
  );
}