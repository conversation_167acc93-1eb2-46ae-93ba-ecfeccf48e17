// knowledge:payment-form-v3:start
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Order } from '@/lib/db/v4/schemas/order-schema';
import { Clock, X, Calendar, User, CreditCard, Beaker, Trash2, Percent, DollarSign } from 'lucide-react';
import { format } from 'date-fns';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { PrintPreviewDialog } from './print/PrintPreviewDialog';
import { kitchenPrintService } from '@/lib/services/kitchen-print-service';
import { updateOrder } from '@/lib/db/v4/operations/order-ops';
import { explodeMenuItemToStockConsumption, addWasteLog } from '@/lib/db/v4/operations/inventory-ops';
import { createCashTransaction } from '@/lib/db/v4';
import { toast } from 'sonner';

interface PaymentFormProps {
  order: Order;
  onSubmit: (receivedAmount: number, wasteData?: WasteProcessingData, discountData?: DiscountData) => void;
  onCancel: () => void;
  isProcessing?: boolean;
  errorMsg?: string;
}

interface WasteItem {
  itemIndex: number;
  quantity: number;
}

// 🚀 NEW: Interface for passing waste data to payment processor
export interface WasteProcessingData {
  wasteItems: WasteItem[];
  wasteReason: string;
  totalWasteValue: number;
  wastedMenuItems: Array<{
    name: string;
    quantity: number;
    originalQuantity: number;
    price: number;
    addons?: any[];
    menuItemId?: string; // 🚀 Add menuItemId for cost calculation
  }>;
}

// 💸 NEW: Interface for passing discount data to payment processor
export interface DiscountData {
  discountType: 'percentage' | 'fixed_amount';
  discountValue: number;
  discountAmount: number;
  discountReason: string;
  subtotal: number;
}

export default function PaymentForm({ order, onSubmit, onCancel, isProcessing, errorMsg }: PaymentFormProps) {
  const [input, setInput] = useState('');
  const [wasteItems, setWasteItems] = useState<WasteItem[]>([]);
  const [wasteReason, setWasteReason] = useState('');
  const [showWasteMode, setShowWasteMode] = useState(false);
  
  // 💸 NEW: Discount state - showDiscountMode is removed as discount UI is always visible
  const [discountType, setDiscountType] = useState<'percentage' | 'fixed_amount'>('fixed_amount');
  const [discountValue, setDiscountValue] = useState<number>(0);
  const [discountReason, setDiscountReason] = useState('');
  
  // Calculate adjusted totals with waste and discount
  const wastedAmount = wasteItems.reduce((total, wasteItem) => {
    const item = order.items[wasteItem.itemIndex];
    if (!item) return total;
    
    let itemPrice = item.price;
    if (item.addons && item.addons.length > 0) {
      itemPrice += item.addons.reduce((sum, addon) => sum + addon.price, 0);
    }
    
    return total + (itemPrice * wasteItem.quantity);
  }, 0);
  
  // Calculate subtotal (after waste but before discount)
  const subtotalAfterWaste = order.total - wastedAmount;
  
  // Calculate discount amount
  let discountAmount = 0;
  if (discountValue > 0) {
    if (discountType === 'percentage') {
      discountAmount = (subtotalAfterWaste * discountValue) / 100;
    } else if (discountType === 'fixed_amount') {
      discountAmount = Math.min(discountValue, subtotalAfterWaste); // Can't discount more than subtotal
    }
  }
  
  // Final total after waste and discount
  const adjustedTotal = Math.max(0, subtotalAfterWaste - discountAmount);
  
  const received = parseFloat(input) || 0;
  const change = Math.max(0, received - adjustedTotal);
  const canPay = received >= adjustedTotal;
  
  const [showPrintPreview, setShowPrintPreview] = useState(false);
  const [printJob, setPrintJob] = useState<{ title: string; content: string; type: 'kitchen' | 'receipt' | 'report' | 'expo' } | null>(null);

  const handleKey = (key: string) => {
    if (key === 'C') {
      setInput('');
    } else if (key === '⌫') {
      setInput(prev => prev.slice(0, -1));
    } else if (input.length < 8) {
      setInput(prev => prev + key);
    }
  };

  const handleQuickAdd = (amount: number) => {
    setInput(prev => ((parseFloat(prev) || 0) + amount).toString());
  };

  const handleWasteItemChange = (itemIndex: number, checked: boolean) => {
    if (checked) {
      const item = order.items[itemIndex];
      setWasteItems(prev => [...prev.filter(w => w.itemIndex !== itemIndex), {
        itemIndex,
        quantity: item.quantity
      }]);
    } else {
      setWasteItems(prev => prev.filter(w => w.itemIndex !== itemIndex));
    }
  };

  // 🚀 NEW: Modified handleSubmit to pass waste data to payment processor
  const handleSubmit = () => {
    if (!canPay || isProcessing) return;

    // Validate discount if applied
    if (discountValue > 0) {
      // Optional: still validate basic numeric rules
      if (discountValue <= 0) {
        toast.error('La valeur de la remise doit être supérieure à 0');
        return;
      }
      if (discountType === 'percentage' && discountValue > 100) {
        toast.error('La remise ne peut pas dépasser 100%');
        return;
      }
    }

    let wasteData: WasteProcessingData | undefined;

    // If there are waste items, prepare the waste data
    if (wasteItems.length > 0) {
      if (!wasteReason.trim()) {
        toast.error('Please provide a reason for waste');
        return;
      }

      // Prepare waste data for processing after payment
      wasteData = {
        wasteItems,
        wasteReason: wasteReason.trim(),
        totalWasteValue: wastedAmount,
        wastedMenuItems: wasteItems.map(w => {
          const item = order.items[w.itemIndex];
          return {
            name: item.name,
            quantity: w.quantity,
            originalQuantity: item.quantity,
            price: item.price,
            addons: item.addons,
            menuItemId: item.menuItemId // 🚀 Add menuItemId for cost calculation
          };
        })
      };
    }

    // 💸 NEW: Prepare discount data
    const discountData = discountValue > 0 ? {
      discountType,
      discountValue,
      discountAmount,
      discountReason: discountReason.trim(),
      subtotal: subtotalAfterWaste
    } : undefined;

    // Call onSubmit with received amount, waste data, and discount data
    onSubmit(received, wasteData, discountData);
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      const amountInput = document.getElementById('payment-amount');
      if (amountInput) amountInput.focus();
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const handleClosePreview = () => {
    setShowPrintPreview(false);
    setPrintJob(null);
  };

  const isItemWasted = (itemIndex: number) => {
    return wasteItems.some(w => w.itemIndex === itemIndex);
  };

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Header */}
      <div className="flex items-center justify-between px-5 py-3 border-b bg-muted/30">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
            <Clock className="h-3.5 w-3.5" />
            {format(new Date(order.createdAt), 'HH:mm')}
          </div>
          <Separator orientation="vertical" className="h-4" />
          <span className="text-sm font-semibold">#{(order.id || order._id).slice(-6)}</span>
          <Badge variant="secondary" className="text-xs px-2 py-0.5">{order.status}</Badge>
        </div>
      </div>

      <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 min-h-0">
        {/* Left Panel - Order Details */}
        <div className="flex flex-col h-full border-r">
          {/* Customer Info */}
          <div className="px-5 py-2.5 bg-muted/20 border-b">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center gap-1.5">
                <User className="h-3.5 w-3.5" />
                {order.customer?.name || 'Client'}
              </div>
              <div className="flex items-center gap-1.5">
                <Calendar className="h-3.5 w-3.5" />
                {format(new Date(order.createdAt), 'dd/MM/yyyy')}
              </div>
            </div>
          </div>

          {/* Items List - Scrollable Only */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-5 space-y-3">

              {order.items.map((item, idx) => {
                const isWasted = isItemWasted(idx);
                const totalItemPrice = item.price + (item.addons?.reduce((sum, addon) => sum + addon.price, 0) || 0);
                
                return (
                  <div key={idx} className={`flex items-start gap-3 ${isWasted ? 'opacity-50' : ''}`}>
                    {showWasteMode && (
                      <Checkbox
                        checked={isWasted}
                        onCheckedChange={(checked) => handleWasteItemChange(idx, checked as boolean)}
                        disabled={isProcessing}
                        className="mt-1 data-[state=checked]:bg-destructive data-[state=checked]:border-destructive"
                      />
                    )}
                    
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-xs font-semibold text-primary">
                      {item.quantity}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-1.5 mb-0.5">
                        <h4 className="font-medium text-sm">{item.name}</h4>
                        {isWasted && (
                          <Badge variant="destructive" className="text-xs px-1.5 py-0.5">
                            WASTE
                          </Badge>
                        )}
                      </div>
                      
                      {item.addons && item.addons.length > 0 && (
                        <div className="space-y-0.5 mb-1">
                          {item.addons.map((addon, addonIdx) => (
                            <div key={addonIdx} className="flex items-center gap-1.5 text-xs text-muted-foreground">
                              <Beaker className="h-2.5 w-2.5" />
                              <span>{addon.name}</span>
                              {addon.price > 0 && (
                                <span className="font-medium">+{addon.price} DA</span>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                      
                      <div className="text-xs text-muted-foreground">
                        {item.quantity} × {totalItemPrice} DA
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="text-base font-semibold">
                        {item.quantity * totalItemPrice} DA
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
            
          {/* 🚀 NEW: Waste Selection Panel - Shows selected items and reason */}
          {showWasteMode && (
            <div className="border-t bg-destructive/5 p-5 shrink-0">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Trash2 className="h-4 w-4 text-destructive" />
                  <h3 className="font-medium text-sm text-destructive">Select Items for Waste</h3>
                </div>
                
                <Textarea
                  placeholder="Reason for waste (required)..."
                  value={wasteReason}
                  onChange={(e) => setWasteReason(e.target.value)}
                  className="min-h-[70px] text-sm"
                  disabled={isProcessing}
                />

                {wasteItems.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center p-2.5 bg-destructive/10 rounded-md">
                      <span className="text-xs font-medium text-destructive">
                        {wasteItems.length} item(s) selected for waste
                      </span>
                      <span className="font-semibold text-destructive">-{wastedAmount} DA</span>
                    </div>
                    
                    <div className="text-xs text-muted-foreground bg-amber-50 border border-amber-200 rounded-md p-2">
                      💡 Waste will be processed automatically after payment completion
                    </div>
                  </div>
                )}

                <Button
                  variant="outline"
                  onClick={() => {
                    setWasteItems([]);
                    setWasteReason('');
                    setShowWasteMode(false);
                  }}
                  disabled={isProcessing}
                  className="w-full h-9 text-sm"
                >
                  Done Selecting
                </Button>
              </div>
            </div>
          )}

          {/* 💸 NEW: Discount Section - Always visible and simplified */}
          <div className="border-t py-4 px-5 space-y-3"> {/* Adjusted padding and removed specific background */}
            <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground"> {/* Adjusted text color */}
              <Percent className="h-4 w-4" /> {/* Removed specific text color */}
              Apply Discount (REMISE)
            </div>
            
            <div className="grid grid-cols-2 gap-2">
                          <Select
              value={discountType}
              onValueChange={(value: 'percentage' | 'fixed_amount') => {
                setDiscountType(value);
              }}
              disabled={isProcessing}
            >
              <SelectTrigger className="h-9 text-sm">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="percentage">Pourcentage (%)</SelectItem>
                <SelectItem value="fixed_amount">Montant fixe (DA)</SelectItem>
              </SelectContent>
            </Select>
              
              <div className="relative">
                <Input
                  type="number"
                  placeholder="Valeur"
                  value={discountValue || ''}
                  onChange={(e) => setDiscountValue(parseFloat(e.target.value) || 0)}
                  disabled={isProcessing}
                  className="h-9 text-sm pr-8 text-right" // Align text to right
                  min="0"
                  max={discountType === 'percentage' ? 100 : subtotalAfterWaste}
                  step={discountType === 'percentage' ? 1 : 10}
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                  {discountType === 'percentage' ? '%' : 'DA'}
                </div>
              </div>
            </div>
            
            <Textarea
              placeholder="Reason for discount (required)..."
              value={discountReason}
              onChange={(e) => setDiscountReason(e.target.value)}
              className="min-h-[70px] text-sm"
              disabled={isProcessing}
            />

            {discountValue > 0 && discountAmount > 0 && (
              <div className="space-y-1">
                <div className="flex flex-col p-2 bg-blue-50/50 rounded-lg"> {/* Reduced padding, adjusted background, border-radius */}
                  <div className="flex justify-between items-center text-sm font-medium text-blue-700"> {/* Adjusted font size */}
                    <span>{discountType === 'percentage' ? `${discountValue}% discount` : `${discountValue} DA discount`}</span>
                    <span className="font-semibold">-{discountAmount.toFixed(0)} DA</span>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    💰 New total: {adjustedTotal} DA (was {subtotalAfterWaste} DA)
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Waste Toggle Button - Fixed at Bottom (only visible if waste mode is off) */}
          {!showWasteMode && (
            <div className="p-5 border-t shrink-0">
              <Button
                variant="outline"
                onClick={() => setShowWasteMode(true)}
                disabled={isProcessing}
                className="w-full h-9 text-sm"
              >
                <Trash2 className="h-3.5 w-3.5 mr-1.5" />
                Mark Items as Waste
              </Button>
            </div>
          )}
        </div>

        {/* Right Panel - Payment */}
        <div className="flex flex-col h-full">
          {/* Amount Due */}
          <div className="p-4 text-center border-b shrink-0">
            <p className="text-sm text-muted-foreground">Amount Due</p>
            
            {/* Show breakdown if there's waste or discount */}
            {(wastedAmount > 0 || discountAmount > 0) && (
              <div className="space-y-1 mb-1"> {/* Adjusted margin-bottom to make it more compact */}
                <p className="text-sm text-muted-foreground line-through">
                  Original: {order.total} DA
                </p>
                {wastedAmount > 0 && (
                  <p className="text-xs text-destructive">
                    💸 Waste: -{wastedAmount} DA
                  </p>
                )}
                {discountAmount > 0 && (
                  <p className="text-xs text-blue-600">
                    💰 Discount: -{discountAmount.toFixed(0)} DA
                    {discountType === 'percentage' ? ` (${discountValue}%)` : ''}
                  </p>
                )}
              </div>
            )}
            
            <p className="text-3xl font-bold tracking-tight">{adjustedTotal} DA</p>
            
            {/* Removed Simplified summary line for compactness and to remove redundancy */}
          </div>

          {/* Scrollable Payment Details */}
          <div className="flex-1 overflow-y-auto">
            {/* Payment Input */}
            <div className="p-4 border-b">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <CreditCard className="h-4 w-4" />
                  Amount Received
                </div>
                <div className="relative">
                  <Input
                    id="payment-amount"
                    type="text"
                    inputMode="numeric"
                    className="text-right text-4xl font-bold h-12 pr-10 border-2 focus:border-primary"
                    value={input}
                    onChange={(e) => setInput(e.target.value.replace(/[^0-9]/g, ''))}
                    disabled={isProcessing}
                    placeholder="0"
                  />
                  <div className="absolute right-3 top-1/2 -translate-y-1/2 text-base text-muted-foreground font-semibold">DA</div>
                </div>
              </div>
            </div>

            {/* Numpad */}
            <div className="p-4 border-b">
              <div className="grid grid-cols-3 gap-2 mb-2">
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 'C', 0, '⌫'].map((key) => (
                  <Button
                    key={key}
                    variant="outline"
                    className="h-12 text-xl font-semibold hover:bg-primary hover:text-primary-foreground transition-all"
                    onClick={() => handleKey(key.toString())}
                    disabled={isProcessing}
                  >
                    {key}
                  </Button>
                ))}
              </div>

              <div className="grid grid-cols-4 gap-1.5">
                {[10, 20, 50, 100, 200, 500, 1000, 2000].map(amount => (
                  <Button
                    key={amount}
                    variant="secondary"
                    size="sm"
                    onClick={() => handleQuickAdd(amount)}
                    disabled={isProcessing}
                    className="h-9 text-base"
                  >
                    +{amount}
                  </Button>
                ))}
              </div>
            </div>

            {/* Summary */}
            <div className="p-4 border-b">
              <div className="space-y-1.5">
                <div className="flex justify-between text-base">
                  <span className="text-muted-foreground">Received:</span>
                  <span className="font-semibold text-green-600">{received} DA</span>
                </div>
                <div className="flex justify-between text-base">
                  <span className="text-muted-foreground">Change:</span>
                  <span className="font-semibold text-red-600">{change} DA</span>
                </div>
              </div>
            </div>

            {errorMsg && (
              <div className="p-4 border-b">
                <div className="text-sm text-destructive bg-destructive/10 p-2 rounded-lg">
                  {errorMsg}
                </div>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="p-4 shrink-0">
            <div className="space-y-2">
              <Button
                onClick={handleSubmit}
                disabled={
                  isProcessing || 
                  !canPay || 
                  (wasteItems.length > 0 && !wasteReason.trim()) ||
                  false
                }
                className="w-full h-12 text-xl font-semibold"
                size="lg"
              >
                {isProcessing ? (
                  <>
                    <div className="mr-2 h-5 w-5 animate-spin rounded-full border-3 border-current border-t-transparent" />
                    Processing...
                  </>
                ) : (
                  "Complete Payment"
                )}
              </Button>
              
              <Button
                variant="outline"
                onClick={onCancel}
                disabled={isProcessing}
                className="w-full h-10 text-base"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </div>

      {showPrintPreview && printJob && (
        <PrintPreviewDialog 
          open={showPrintPreview} 
          onOpenChange={setShowPrintPreview} 
          printJob={printJob} 
          onPrint={() => {}} 
        />
      )}
    </div>
  );
}
// knowledge:payment-form-v3:end