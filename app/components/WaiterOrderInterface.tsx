"use client";

import { useState, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>, use<PERSON>em<PERSON> } from "react";
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { useTableDB } from "@/lib/hooks/useTableDB";
import { useMenuV4 } from "@/lib/hooks/use-menu-v4";
import { useOrderV4 } from "@/lib/hooks/use-order-v4";
import { cn } from "@/lib/utils";

// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { ArrowLeft, Check, ChevronRight, Edit2, Info, MinusCircle, PlusCircle, Search, ShoppingCart, Table as TableIcon, Trash2, X } from "lucide-react";

// Types and interfaces
import { OrderItem, Order } from "@/lib/types";
import type { MenuItem, Addon, Category } from "@/lib/db/v4-menu-service";
import { TableLayout as Table } from "@/lib/db/table-db";
import { useToast } from "@/components/ui/use-toast";

export default function WaiterOrderInterface() {
  // Auth and database hooks
  const { user } = useAuth();
  const { tables, isLoading: tablesLoading, error: tablesError, isReady: tablesReady } = useTableDB();
  const { categories, isLoading: menuLoading, error: menuError, isReady: menuReady } = useMenuV4();
  const { createOrder, isLoading: ordersLoading, error: ordersError, isReady: ordersReady } = useOrderV4();
  const { toast } = useToast();

  // UI State
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [isTableDialogOpen, setIsTableDialogOpen] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [selectedItemForAddons, setSelectedItemForAddons] = useState<string | null>(null);
  const [selectedAddons, setSelectedAddons] = useState<Record<string, Set<string>>>({});
  const [itemNotes, setItemNotes] = useState<Record<string, string>>({});
  const [editingItem, setEditingItem] = useState<OrderItem | null>(null);

  // Order State
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [selectedItemSizes, setSelectedItemSizes] = useState<{[key: string]: string}>({});
  const [currentOrder, setCurrentOrder] = useState<Order>({
    id: "",
    type: "table",
    status: "pending",
    tableId: "",
    items: [],
    total: 0,
    createdAt: new Date()
  });

  // Loading states
  const [isOperationLoading, setIsOperationLoading] = useState(false);
  const isLoading = menuLoading && tablesLoading && ordersLoading; // Only loading if ALL are loading
  const error = menuError || tablesError || ordersError;
  const isReady = menuReady || (categories.length > 0 && tables.length >= 0); // More flexible ready state

  // Set selected category when categories are loaded
  useEffect(() => {
    if (categories.length > 0 && !selectedCategory) {
      setSelectedCategory(categories[0].id);
    }
  }, [categories, selectedCategory]);

  // Calculate total price for the order
  const calculateTotal = useCallback((items: OrderItem[]) => {
    return items.reduce((total, item) => {
      const itemTotal = item.price * item.quantity;
      const addonTotal = item.addons ? item.addons.reduce((sum, addon) => sum + addon.price, 0) * item.quantity : 0;
      return total + itemTotal + addonTotal;
    }, 0);
  }, []);

  // Get key for addon selections
  const getAddonKey = useCallback((itemId: string, size: string) => `${itemId}-${size}`, []);

  // Check if addon is selected
  const isAddonSelected = useCallback((itemId: string, size: string, addonId: string) => {
    const key = getAddonKey(itemId, size);
    return selectedAddons[key]?.has(addonId) || false;
  }, [selectedAddons, getAddonKey]);

  // Toggle addon selection
  const toggleAddonSelection = useCallback((itemId: string, size: string, addonId: string) => {
    const key = getAddonKey(itemId, size);

    setSelectedAddons(prev => {
      // Ensure we have a set for this item
      const currentSelections = prev[key] || new Set<string>();
      const newSelections = new Set(currentSelections);

      // Toggle the selection
      if (newSelections.has(addonId)) {
        newSelections.delete(addonId);
      } else {
        newSelections.add(addonId);
      }

      return {
        ...prev,
        [key]: newSelections
      };
    });
  }, [getAddonKey]);

  // Get available addons for an item
  const getAddonsForMenuItem = useCallback((menuItemId: string): Addon[] => {
    const category = categories.find(c =>
      c.items.some(item => item.id === menuItemId)
    );
    if (category && category.addons) {
      return category.addons;
    }
    return [];
  }, [categories]);

  // Handle table selection
  const handleTableSelect = useCallback((table: Table) => {
    setSelectedTable(table);
    setCurrentOrder(prev => ({
      ...prev,
      tableId: table.id
    }));
    setIsTableDialogOpen(false);
  }, []);

  // Handle adding item to order
  const handleAddItem = useCallback((item: MenuItem, size?: string) => {
    // Determine the correct size and price
    const selectedSize = size || Object.keys(item.prices)[0] || 'default';
    const price = item.prices[selectedSize] || Object.values(item.prices)[0] || 0;

    // Set the size selection for this item
    setSelectedItemSizes(prev => ({...prev, [item.id]: selectedSize}));

    // Get selected addons
    const addonKey = getAddonKey(item.id, selectedSize);
    const selectedAddonIds = Array.from(selectedAddons[addonKey] || new Set<string>());

    // Get addon details
    const availableAddons = getAddonsForMenuItem(item.id);
    const selectedAddonObjects = availableAddons
      .filter(addon => selectedAddonIds.includes(addon.id))
      .map(addon => ({
        id: addon.id,
        name: addon.name,
        price: addon.price
      }));

    // Get notes for this item
    const notes = itemNotes[item.id] || '';

    // Check if we're editing an existing item
    if (editingItem) {
      // Update the existing item
      const updatedItems = currentOrder.items.map(orderItem => {
        if (orderItem.id === editingItem.id) {
          return {
            ...orderItem,
            size: selectedSize,
            price: price,
            addons: selectedAddonObjects,
            notes: notes
          };
        }
        return orderItem;
      });

      setCurrentOrder(prev => ({
        ...prev,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      }));

      // Clear editing state
      setEditingItem(null);
    } else {
      // Look for identical item
      const existingItemIndex = currentOrder.items.findIndex(orderItem => {
        // Check if menuItem, size are the same
        if (orderItem.menuItemId !== item.id || orderItem.size !== selectedSize) {
          return false;
        }

        // Check if addons are the same
        const existingAddonIds = (orderItem.addons || []).map(a => a.id).sort();
        const newAddonIds = selectedAddonObjects.map(a => a.id).sort();
        if (JSON.stringify(existingAddonIds) !== JSON.stringify(newAddonIds)) {
          return false;
        }

        // Check if notes are the same
        if ((orderItem.notes || '') !== notes) {
          return false;
        }

        return true;
      });

      if (existingItemIndex !== -1) {
        // Increment quantity of existing item
        const updatedItems = [...currentOrder.items];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + 1
        };

        setCurrentOrder(prev => ({
          ...prev,
          items: updatedItems,
          total: calculateTotal(updatedItems)
        }));
      } else {
        // Add new item
        const newItem: OrderItem = {
          id: `${Date.now()}-${Math.random()}`,
          menuItemId: item.id,
          name: item.name,
          price: price,
          quantity: 1,
          size: selectedSize,
          addons: selectedAddonObjects,
          notes: notes
        };

        const updatedItems = [...currentOrder.items, newItem];
        setCurrentOrder(prev => ({
          ...prev,
          items: updatedItems,
          total: calculateTotal(updatedItems)
        }));
      }
    }

    // Clear selections
    setSelectedItemForAddons(null);
    setSelectedAddons(prev => ({
      ...prev,
      [addonKey]: new Set()
    }));
    setItemNotes(prev => ({
      ...prev,
      [item.id]: ''
    }));
  }, [currentOrder.items, selectedAddons, itemNotes, selectedItemSizes, getAddonKey, getAddonsForMenuItem, calculateTotal, editingItem]);

  // Handle editing an item
  const handleEditItem = useCallback((item: OrderItem) => {
    setEditingItem(item);
    
    // Find the menu item
    const menuItem = categories
      .flatMap(cat => cat.items)
      .find(mi => mi.id === item.menuItemId);
    
    if (menuItem) {
      setSelectedItemForAddons(item.menuItemId);
      setSelectedItemSizes(prev => ({
        ...prev,
        [item.menuItemId]: item.size || 'default'
      }));
      
      // Set addons
      const addonKey = getAddonKey(item.menuItemId, item.size || 'default');
      const addonSet = new Set((item.addons || []).map(addon => addon.id));
      setSelectedAddons(prev => ({
        ...prev,
        [addonKey]: addonSet
      }));
      
      // Set notes
      setItemNotes(prev => ({
        ...prev,
        [item.menuItemId]: item.notes || ''
      }));
    }
  }, [categories, getAddonKey]);

  // Handle updating quantity
  const handleUpdateQuantity = useCallback((itemId: string, change: number) => {
    const updatedItems = currentOrder.items.map(item => {
      if (item.id === itemId) {
        const newQuantity = Math.max(0, item.quantity + change);
        return newQuantity > 0 ? { ...item, quantity: newQuantity } : null;
      }
      return item;
    }).filter(Boolean) as OrderItem[];

    setCurrentOrder(prev => ({
      ...prev,
      items: updatedItems,
      total: calculateTotal(updatedItems)
    }));
  }, [currentOrder.items, calculateTotal]);

  // Handle placing order
  const handlePlaceOrder = useCallback(async () => {
    if (!selectedTable) {
      toast({
        title: "Table Required",
        description: "Please select a table before placing the order.",
        variant: "destructive"
      });
      return;
    }

    if (currentOrder.items.length === 0) {
      toast({
        title: "Empty Order",
        description: "Please add items to the order before placing it.",
        variant: "destructive"
      });
      return;
    }

    setIsOperationLoading(true);

    try {
      const newOrder = {
        orderType: "dine-in" as const,
        status: "pending" as const,
        tableId: selectedTable.id,
        items: currentOrder.items,
        total: currentOrder.total,
        notes: ""
      };

      await createOrder(newOrder);

      toast({
        title: "Order Placed",
        description: `Order for table ${selectedTable.name} has been placed successfully.`,
      });

      // Reset order
      setCurrentOrder({
        id: "",
        type: "table",
        status: "pending",
        tableId: "",
        items: [],
        total: 0,
        createdAt: new Date()
      });
      setSelectedTable(null);
      setSelectedItemForAddons(null);
      setSelectedAddons({});
      setItemNotes({});
      setSelectedItemSizes({});

    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to place order. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsOperationLoading(false);
    }
  }, [selectedTable, currentOrder, createOrder, toast]);

  // Filter menu items based on search and category
  const filteredMenuItems = useCallback((categoryId: string) => {
    const category = categories.find(c => c.id === categoryId);
    if (!category) return [];

    return category.items.filter(item =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [categories, searchQuery]);

  // Handle going back (for mobile)
  const handleGoBack = useCallback(() => {
    // This could navigate back or close the interface
    window.history.back();
  }, []);

  // Handle errors
  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <h2 className="text-lg font-semibold text-destructive mb-2">Error Loading Data</h2>
              <p className="text-muted-foreground mb-4">{error.message}</p>
              <Button onClick={() => window.location.reload()}>
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Handle loading state
  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col items-center justify-center p-8 gap-4">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
              <p className="text-sm text-muted-foreground">Loading menu and tables...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header - Minimalistic */}
      <div className="bg-white py-3 px-4 flex items-center justify-between sticky top-0 z-20 shadow-sm">
        <div className="flex items-center gap-2">
          <Button size="icon" variant="ghost" onClick={handleGoBack} className="h-9 w-9 rounded-full">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="font-medium text-lg">Waiter Interface</h1>
        </div>

        <div className="flex items-center gap-2">
          {selectedTable && (
            <div className="text-sm mr-2 text-muted-foreground">
              Table {selectedTable.name}
            </div>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsTableDialogOpen(true)}
            className="h-8"
          >
            <TableIcon className="h-4 w-4 mr-1" />
            {selectedTable ? `Table ${selectedTable.name}` : 'Select Table'}
          </Button>
          <Sheet open={isCartOpen} onOpenChange={setIsCartOpen}>
            <SheetTrigger asChild>
              <Button size="sm" className="h-8 relative">
                <ShoppingCart className="h-4 w-4 mr-1" />
                Cart
                {currentOrder.items.length > 0 && (
                  <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                    {currentOrder.items.reduce((sum, item) => sum + item.quantity, 0)}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[400px] sm:w-[540px]">
              <SheetHeader>
                <SheetTitle>Order Summary</SheetTitle>
              </SheetHeader>
              <div className="flex flex-col h-full">
                <ScrollArea className="flex-1 mt-4">
                  {currentOrder.items.length === 0 ? (
                    <div className="text-center text-muted-foreground py-8">
                      No items in cart
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {currentOrder.items.map((item) => (
                        <Card key={item.id} className="p-3">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="font-medium">{item.name}</h4>
                              {item.size && item.size !== 'default' && (
                                <p className="text-sm text-muted-foreground">Size: {item.size}</p>
                              )}
                              {item.addons && item.addons.length > 0 && (
                                <p className="text-sm text-muted-foreground">
                                  Addons: {item.addons.map(a => a.name).join(', ')}
                                </p>
                              )}
                              {item.notes && (
                                <p className="text-sm text-muted-foreground">Notes: {item.notes}</p>
                              )}
                              <p className="text-sm font-medium">{item.price} DA × {item.quantity}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                size="icon"
                                variant="outline"
                                className="h-7 w-7"
                                onClick={() => handleUpdateQuantity(item.id, -1)}
                              >
                                <MinusCircle className="h-4 w-4" />
                              </Button>
                              <span className="w-8 text-center">{item.quantity}</span>
                              <Button
                                size="icon"
                                variant="outline"
                                className="h-7 w-7"
                                onClick={() => handleUpdateQuantity(item.id, 1)}
                              >
                                <PlusCircle className="h-4 w-4" />
                              </Button>
                              <Button
                                size="icon"
                                variant="ghost"
                                className="h-7 w-7 text-destructive"
                                onClick={() => handleEditItem(item)}
                              >
                                <Edit2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  )}
                </ScrollArea>
                <SheetFooter className="border-t pt-4">
                  <div className="w-full space-y-3">
                    <div className="flex justify-between items-center text-lg font-semibold">
                      <span>Total:</span>
                      <span>{currentOrder.total} DA</span>
                    </div>
                    <Button
                      className="w-full"
                      onClick={handlePlaceOrder}
                      disabled={currentOrder.items.length === 0 || !selectedTable || isOperationLoading}
                    >
                      {isOperationLoading ? "Placing Order..." : "Place Order"}
                    </Button>
                  </div>
                </SheetFooter>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Search bar */}
        <div className="bg-white px-4 py-2 border-b">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search menu items..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Categories - Enhanced for tight screens */}
        <div className="flex-shrink-0 border-b bg-white sticky top-0 z-10 shadow-sm">
          <div className="overflow-x-auto py-3 px-4">
            <div className="flex gap-2 min-w-max">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant="ghost"
                  className={`rounded-md text-sm font-medium px-4 h-9 whitespace-nowrap ${selectedCategory === category.id ? 'bg-primary/10 text-primary hover:bg-primary/15' : 'hover:bg-muted/50'}`}
                  onClick={() => setSelectedCategory(category.id)}
                >
                  {category.emoji && <span className="mr-1.5">{category.emoji}</span>}
                  {category.name}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Menu Items */}
        <div className="flex-1 overflow-auto p-2">
          <div className="grid gap-2">
            {filteredMenuItems(selectedCategory).map((item) => {
              const sizes = Object.keys(item.prices);
              const defaultSize = sizes[0] || 'default';
              const defaultPrice = item.prices[defaultSize] || 0;

              // Get addons for this item
              const addons = getAddonsForMenuItem(item.id);
              const hasAddons = addons.length > 0;

              // Is this item currently selected for customization?
              const isSelected = selectedItemForAddons === item.id;

              // Current selected size
              const selectedSize = selectedItemSizes[item.id] || defaultSize;

              return (
                <Card
                  key={item.id}
                  id={`menu-item-${item.id}`}
                  className={cn(
                    "cursor-pointer hover:bg-gray-50 border",
                    isSelected && "border-primary ring-1 ring-primary",
                    editingItem?.menuItemId === item.id && "border-primary ring-1 ring-primary bg-primary/5"
                  )}
                  onClick={() => {
                    if (hasAddons) {
                      setSelectedItemForAddons(isSelected ? null : item.id);
                    } else {
                      handleAddItem(item, defaultSize);
                    }
                  }}
                >
                  <CardContent className={cn("p-3", !isSelected && "flex justify-between items-center")}>
                    {!isSelected ? (
                      <>
                        <div className="flex-1">
                          <div className="font-medium">{item.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {defaultPrice} DA
                          </div>
                        </div>
                        {!hasAddons ? (
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-7 w-7 rounded-full bg-primary/10 text-primary hover:text-primary hover:bg-primary/20"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAddItem(item, defaultSize);
                            }}
                          >
                            <PlusCircle className="h-5 w-5" />
                          </Button>
                        ) : (
                          <ChevronRight className="h-5 w-5 text-muted-foreground" />
                        )}
                      </>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">{item.name}</h3>
                            <p className="text-xs text-muted-foreground">Customize your order</p>
                          </div>
                          <div className="font-medium">{item.prices[selectedSize]} DA</div>
                        </div>

                        {/* Size Selection - Refined */}
                        {sizes.length > 1 && (
                          <div className="pt-3 border-t">
                            <h4 className="text-sm font-medium mb-2">Size</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {sizes.map(size => {
                                // Only highlight if this item is selected AND this size is selected
                                const isSizeSelected = selectedItemForAddons === item.id && selectedItemSizes[item.id] === size;

                                return (
                                  <Button
                                    key={size}
                                    variant={isSizeSelected ? "default" : "outline"}
                                    size="sm"
                                    className={`h-9 py-1 text-sm rounded ${
                                      isSizeSelected
                                        ? "bg-gray-900 text-white border-gray-900"
                                        : "bg-white text-gray-700 hover:bg-gray-100 border-gray-200"
                                    }`}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedItemSizes(prev => ({...prev, [item.id]: size}));
                                    }}
                                  >
                                    <div className="flex justify-between w-full items-center">
                                      <span>{size === "default" ? "Standard" : size}</span>
                                      <span className={`font-medium ${isSizeSelected ? "text-white" : "text-primary"}`}>{item.prices[size]} DA</span>
                                    </div>
                                  </Button>
                                );
                              })}
                            </div>
                          </div>
                        )}

                        {/* Addons */}
                        {hasAddons && (
                          <div className="pt-2 border-t">
                            <h4 className="text-sm font-medium mb-1.5">Add-ons</h4>
                            <div className="grid gap-1.5">
                              {addons.map(addon => {
                                const isAddonChecked = isAddonSelected(item.id, selectedSize, addon.id);

                                return (
                                  <div
                                    key={addon.id}
                                    className={cn(
                                      "flex items-center justify-between p-2 rounded-md cursor-pointer border",
                                      isAddonChecked ? "border-primary bg-primary/10" : "border-muted hover:bg-muted/50"
                                    )}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toggleAddonSelection(item.id, selectedSize, addon.id);
                                    }}
                                  >
                                    <div className="flex items-center gap-2">
                                      <div className={cn(
                                        "w-4 h-4 rounded-sm flex items-center justify-center",
                                        isAddonChecked ? "bg-primary text-white" : "border border-muted-foreground"
                                      )}>
                                        {isAddonChecked && <Check className="h-3 w-3" />}
                                      </div>
                                      <span className="text-sm">{addon.name}</span>
                                    </div>
                                    <span className="text-sm font-medium">+{addon.price} DA</span>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        )}

                        {/* Special Instructions */}
                        <div className="pt-2 border-t mt-2">
                          <h4 className="text-sm font-medium mb-1.5">Special Instructions</h4>
                          <Textarea
                            placeholder="Add instructions..."
                            className="resize-none h-[80px] text-sm border-primary/20 focus:border-primary focus:ring-primary"
                            value={itemNotes[item.id] || ''}
                            onChange={(e) => setItemNotes(prev => ({
                              ...prev,
                              [item.id]: e.target.value
                            }))}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2 pt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedItemForAddons(null);
                              setEditingItem(null);
                            }}
                          >
                            Cancel
                          </Button>
                          <Button
                            size="sm"
                            className="flex-1"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAddItem(item, selectedSize);
                            }}
                          >
                            {editingItem ? 'Update Item' : 'Add to Order'}
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </div>

      {/* Table Selection Dialog */}
      <Dialog open={isTableDialogOpen} onOpenChange={setIsTableDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select Table</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-3 gap-3 py-4">
            {tables.map((table) => (
              <Button
                key={table.id}
                variant={selectedTable?.id === table.id ? "default" : "outline"}
                className="h-16 flex flex-col"
                onClick={() => handleTableSelect(table)}
              >
                <TableIcon className="h-6 w-6 mb-1" />
                <span className="text-sm">{table.name}</span>
              </Button>
            ))}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsTableDialogOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 