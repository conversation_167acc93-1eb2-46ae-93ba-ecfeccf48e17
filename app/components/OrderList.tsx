// knowledge:order-list:start
import React, { useState, useMemo, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { useOrderV4 } from "@/lib/hooks/use-order-v4";
import { format } from "date-fns";
import { 
  Loader2, 
  Search, 
  UserRound, 
  ChefHat, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Pencil, 
  X, 
  CreditCard, 
  Utensils,
  Truck,
  Package,
  AlertTriangle,
  TruckIcon,
  DollarSign,
  Send
} from "lucide-react";
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { Order } from "@/lib/db/v4/schemas/order-schema";
import PaymentForm, { WasteProcessingData, DiscountData } from './PaymentForm';
import DeliveryFailureHandler from './DeliveryFailureHandler';
import { OrderCancellationHandler } from './OrderCancellationHandler';
import { useRouter } from 'next/navigation';
import { useEditOrder } from '@/components/providers/EditOrderContext';
import { PrintPreviewDialog } from './print/PrintPreviewDialog';
import { kitchenPrintService } from '@/lib/services/kitchen-print-service';
import { useSimplifiedOrderFinance } from '@/lib/services/simplified-order-finance';
import { kitchenQueueService } from '@/lib/services/kitchen-queue-service';
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

const statusFilters = [
  { value: "all", label: "Toutes les Commandes", icon: null, color: "text-muted-foreground" },
  { value: "pending", label: "En Attente", icon: Clock, color: "text-amber-600" },
  { value: "served", label: "Servi", icon: Utensils, color: "text-violet-600" },
  { value: "completed", label: "Terminé", icon: CheckCircle, color: "text-emerald-600" },
  { value: "cancelled", label: "Annulé", icon: XCircle, color: "text-red-600" },
];

const getStatusConfig = (status: string) => {
  const config = statusFilters.find(s => s.value === status);
  return config || statusFilters[0];
};

const getRowColorClass = (status: string) => {
  switch (status) {
    case "pending":
      return "bg-amber-50/50 border-l-4 border-l-amber-400 hover:bg-amber-50/80";
    case "served":
      return "bg-violet-50/50 border-l-4 border-l-violet-400 hover:bg-violet-50/80";
    case "completed":
      return "bg-emerald-50/50 border-l-4 border-l-emerald-400 hover:bg-emerald-50/80";
    case "cancelled":
      return "bg-red-50/50 border-l-4 border-l-red-400 hover:bg-red-50/80";
    default:
      return "hover:bg-muted/20";
  }
};

const StatusBadge = ({ status }: { status: string }) => {
  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <Badge variant="outline" className={cn("text-xs font-medium", config.color)}>
      {Icon && <Icon className="h-3 w-3 mr-1.5" />}
      {config.label}
    </Badge>
  );
};



const TypeBadge = ({ type }: { type: string }) => {
  const getTypeConfig = (type: string) => {
    switch (type) {
      case "dine-in":
        return { icon: UserRound, label: "Sur Place", color: "text-blue-600" };
      case "delivery":
        return { icon: Truck, label: "Livraison", color: "text-purple-600" };
      case "takeaway":
      case "takeout":
        return { icon: Package, label: "À Emporter", color: "text-orange-600" };
      default:
        return { icon: UserRound, label: type, color: "text-muted-foreground" };
    }
  };

  const config = getTypeConfig(type);
  const Icon = config.icon;

  return (
    <Badge variant="outline" className={cn("text-xs", config.color)}>
      <Icon className="h-3 w-3 mr-1" />
      {config.label}
    </Badge>
  );
};

// 🚚 NEW: Delivery Status Badge Component
const DeliveryStatusBadge = ({ deliveryStatus }: { deliveryStatus?: string }) => {
  if (!deliveryStatus) return null;
  
  const getDeliveryStatusConfig = (status: string) => {
    switch (status) {
      case 'delivered':
        return { icon: CheckCircle, label: "Livré", color: "text-green-700 bg-green-50 border-green-200" };
      case 'failed':
        return { icon: XCircle, label: "Échec", color: "text-red-700 bg-red-50 border-red-200" };
      case 'partially_delivered':
        return { icon: AlertTriangle, label: "Partiel", color: "text-yellow-700 bg-yellow-50 border-yellow-200" };
      case 'out_for_delivery':
        return { icon: TruckIcon, label: "En cours", color: "text-blue-700 bg-blue-50 border-blue-200" };
      case 'pending':
        return { icon: Clock, label: "En attente", color: "text-gray-700 bg-gray-50 border-gray-200" };
      default:
        return { icon: CheckCircle, label: "Livré", color: "text-green-700 bg-green-50 border-green-200" };
    }
  };
  
  const config = getDeliveryStatusConfig(deliveryStatus);
  const Icon = config.icon;
  
  return (
    <Badge variant="outline" className={cn("text-xs font-medium", config.color)}>
      <Icon className="h-3 w-3 mr-1" />
      {config.label}
    </Badge>
  );
};

// 💰 NEW: Collection Status Badge Component  
const CollectionStatusBadge = ({ order }: { order: Order }) => {
  if (order.orderType !== 'delivery' || !order.deliveryPerson) return null;
  
  // Only show for collection-based drivers (staff or freelance collection model)
  const needsCollection = order.deliveryPerson.type === 'staff' || 
                         (order.deliveryPerson.type === 'freelance' && order.deliveryPerson.paymentModel === 'collection');
  
  if (!needsCollection) return null;
  
  // Only show "Collecté" badge when collection is completed
  const isCollected = order.collectionStatus?.isPending === false;
  
  if (isCollected) {
    return (
      <Badge variant="outline" className="text-xs font-medium text-green-700 bg-green-50 border-green-200">
        <CheckCircle className="h-3 w-3 mr-1" />
        Collecté
      </Badge>
    );
  }
  
  return null;
};

// Helper function to check if order is a staff order
const isStaffOrder = (order: Order): boolean => {
  return order.notes?.includes('🍽️ Staff meal') || 
         order.notes?.includes('Commande Équipe') ||
         order.notes?.includes('Staff meal') ||
         order.customer?.name?.includes('Commande Équipe');
};

interface OrderListProps {
  setTab?: (tab: string) => void;
}

const OrderList: React.FC<OrderListProps> = ({ setTab }) => {
  const { orders, isLoading, error, refreshOrders, updateOrder } = useOrderV4();
  const { processOrderPayment } = useSimplifiedOrderFinance();
  const { user } = useAuth();
  const [status, setStatus] = useState("all");
  const [search, setSearch] = useState("");

  const [payOrder, setPayOrder] = useState<Order | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [receivedAmount, setReceivedAmount] = useState("");
  const [errorMsg, setErrorMsg] = useState("");
  const router = useRouter();
  const { setEditOrder, clearEditOrder } = useEditOrder();
  const [showPrintPreview, setShowPrintPreview] = useState(false);
  const [printJob, setPrintJob] = useState<{ title: string; content: string; type: 'kitchen' | 'receipt' | 'report' | 'expo' } | null>(null);
  const { toast } = useToast();
  const [selectedOrderIds, setSelectedOrderIds] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<"detailed" | "summary">("detailed");

  // 🔒 Set staff session start time when component mounts for staff users
  useEffect(() => {
    if (user?.role === 'staff' && !localStorage.getItem('staff_session_start')) {
      localStorage.setItem('staff_session_start', new Date().toISOString());
    }
  }, [user?.role]);

  // Better loading logic: Only show loading if we have no data AND it's loading
  const shouldShowLoading = isLoading && orders.length === 0;
  
  // Handle errors
  if (error && orders.length === 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-4">
            <XCircle className="h-12 w-12 text-red-500 mx-auto" />
            <div>
              <h3 className="text-lg font-semibold text-red-600">Error Loading Orders</h3>
              <p className="text-muted-foreground mt-1">{error.message}</p>
            </div>
            <Button onClick={() => window.location.reload()} variant="outline">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }
  
  const filteredOrders = useMemo(() => {
    let filtered = orders;
    
    // 🔒 Staff session filtering: Only show orders from current active session
    if (user?.role === 'staff') {
      // Get current session start time (you might need to adjust this based on your session management)
      const sessionStartTime = localStorage.getItem('staff_session_start');
      if (sessionStartTime) {
        const sessionStart = new Date(sessionStartTime);
        filtered = filtered.filter(order => {
          const orderTime = new Date(order.createdAt);
          return orderTime >= sessionStart;
        });
      }
    }
    
    if (status !== "all") filtered = filtered.filter(o => o.status === status);
    if (search.trim()) {
      const s = search.toLowerCase();
      filtered = filtered.filter(o =>
        o.id.toLowerCase().includes(s) ||
        (o.customer?.name?.toLowerCase().includes(s) ?? false) ||
        (o.items?.some(item => item.name.toLowerCase().includes(s)))
      );
    }
    // Sort by createdAt descending (most recent first)
    return filtered.slice().sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [orders, status, search, user?.role]);

  // 🍽️ Mark order as served - updates queue context immediately
  const handleMarkAsServed = async (order: Order) => {
    setIsProcessing(true);
    setErrorMsg("");
    try {
      await updateOrder(order.id, { status: 'served' });
      await refreshOrders();
      await kitchenQueueService.completeOrder(order.id);
    } catch (e) {
      setErrorMsg("Failed to mark order as served");
    } finally {
      setIsProcessing(false);
    }
  };

  // 🚚 NEW: Combined Serve + Send for Delivery (collection-based orders)
  const handleServeAndSendForDelivery = async (order: Order) => {
    setIsProcessing(true);
    try {
      // Mark order as served AND completed AND ready for collection
      await updateOrder(order.id, {
        status: 'completed',
        paymentStatus: 'paid' // Mark as paid to trigger collection system
      });
      
      // Initialize delivery status and collection status (default to delivered)
      const { updateDeliveryStatus } = await import('@/lib/db/v4/operations/order-ops');
      await updateDeliveryStatus(order.id, 'delivered', {
        attemptedBy: order.deliveryPerson?.name || 'Staff',
        notes: 'Served and sent for delivery - collection-based payment (auto-served and delivered)'
      });
      
      await refreshOrders();
      await kitchenQueueService.completeOrder(order.id);
      toast({
        title: "🚚 Servi et envoyé en livraison",
        description: `Commande #${order.id.slice(-6)} servie et envoyée avec ${order.deliveryPerson?.name}`
      });
    } catch (error) {
      console.error('Error serving and sending for delivery:', error);
      toast({
        variant: "destructive",
        title: "❌ Erreur",
        description: "Impossible de servir et envoyer la commande en livraison"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // 🚚 NEW: Send for delivery (collection-based orders)
  const handleSendForDelivery = async (order: Order) => {
    setIsProcessing(true);
    try {
      // Mark order as served AND completed AND ready for collection
      await updateOrder(order.id, {
        status: 'completed',
        paymentStatus: 'paid' // Mark as paid to trigger collection system
      });
      
      // Initialize delivery status and collection status (default to delivered)
      const { updateDeliveryStatus } = await import('@/lib/db/v4/operations/order-ops');
      await updateDeliveryStatus(order.id, 'delivered', {
        attemptedBy: order.deliveryPerson?.name || 'Staff',
        notes: 'Sent for delivery - collection-based payment (auto-served and delivered)'
      });
      
      await refreshOrders();
      toast({
        title: "🚚 Envoyé en livraison",
        description: `Commande #${order.id.slice(-6)} servie et envoyée avec ${order.deliveryPerson?.name}`
      });
    } catch (error) {
      console.error('Error sending for delivery:', error);
      toast({
        variant: "destructive",
        title: "❌ Erreur",
        description: "Impossible d'envoyer la commande en livraison"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // 🚚 NEW: Mark delivery as completely failed
  const handleMarkDeliveryFailed = async (order: Order) => {
    setIsProcessing(true);
    try {
      const { processDeliveryFailure } = await import('@/lib/db/v4/operations/order-ops');
      
      // Mark all items as failed
      const failedItems = order.items.map((_, index) => ({
        itemIndex: index,
        quantity: order.items[index].quantity
      }));
      
      await processDeliveryFailure(
        order.id,
        failedItems,
        'Livraison complètement échouée - tous les articles retournés',
        order.deliveryPerson?.name || 'Staff'
      );
      
      await refreshOrders();
      toast({
        title: "❌ Livraison échouée",
        description: `Commande #${order.id.slice(-6)} marquée comme échec total - articles gaspillés`
      });
    } catch (error) {
      console.error('Error marking delivery as failed:', error);
      toast({
        variant: "destructive",
        title: "❌ Erreur", 
        description: "Impossible de marquer la livraison comme échouée"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // 🎯 Helper: Check if order needs collection-based workflow
  const isCollectionBasedDelivery = (order: Order) => {
    return order.orderType === 'delivery' && 
           order.deliveryPerson && 
           (order.deliveryPerson.type === 'staff' || 
            (order.deliveryPerson.type === 'freelance' && order.deliveryPerson.paymentModel === 'collection'));
  };

  // 🎯 Helper: Check if money has been actually collected for an order
  const isMoneyActuallyCollected = (order: Order) => {
    // For dine-in and takeaway orders, payment status 'paid' means money is actually collected
    if (order.orderType !== 'delivery') {
      return order.paymentStatus === 'paid';
    }

    // For delivery orders, distinguish between "ready for collection" and "actually collected"
    if (order.orderType === 'delivery' && order.deliveryPerson) {
      const needsCollection = order.deliveryPerson.type === 'staff' ||
                             (order.deliveryPerson.type === 'freelance' && order.deliveryPerson.paymentModel === 'collection');

      if (needsCollection) {
        // For collection-based delivery:
        // - paymentStatus 'paid' + collectionStatus.isPending true = ready for collection (show failure buttons)
        // - paymentStatus 'paid' + collectionStatus.isPending false = actually collected (hide failure buttons)
        return order.paymentStatus === 'paid' && order.collectionStatus?.isPending === false;
      } else {
        // For prepaid freelance delivery, payment status 'paid' means money is actually collected
        return order.paymentStatus === 'paid';
      }
    }

    return false;
  };

  // 💰 Payment Tag Component - Shows actual payment status
  const PaymentTag = ({ order }: { order: Order }) => {
    const isActuallyPaid = isMoneyActuallyCollected(order);

    if (!isActuallyPaid) return null;

    return (
      <Badge variant="outline" className="text-xs font-medium text-green-700 bg-green-50 border-green-200">
        <DollarSign className="h-3 w-3 mr-1" />
        Payé
      </Badge>
    );
  };

  // 🎯 Helper: Check if order needs regular payment workflow
  const needsRegularPayment = (order: Order) => {
    // Non-delivery orders always need payment
    if (order.orderType !== 'delivery') return true;
    
    // Delivery orders with prepaid freelancers need payment
    if (order.deliveryPerson?.type === 'freelance' && order.deliveryPerson.paymentModel === 'prepaid') return true;
    
    // Collection-based deliveries don't need payment workflow
    return false;
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div className="space-y-6">
        {/* Header & Filters */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold tracking-tight">Commandes</h1>
            <div className="text-sm text-muted-foreground bg-muted/50 px-3 py-1.5 rounded-full">
              {filteredOrders.length} sur {orders.length} commandes
            </div>
          </div>
          
          {/* Search & Status Filters */}
          <div className="flex flex-col lg:flex-row gap-6">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Rechercher des commandes..."
                value={search}
                onChange={e => setSearch(e.target.value)}
                className="pl-9 h-11"
              />
            </div>
            
            <div className="flex gap-2 overflow-x-auto pb-2 lg:pb-0">
              {statusFilters.map(filter => {
                const Icon = filter.icon;
                const isActive = status === filter.value;
                const count = filter.value === "all" 
                  ? orders.length 
                  : orders.filter(o => o.status === filter.value).length;
                
                return (
                  <Button
                    key={filter.value}
                    variant={isActive ? "default" : "outline"}
                    size="default"
                    onClick={() => setStatus(filter.value)}
                    className={cn(
                      "whitespace-nowrap h-11 px-4",
                      !isActive && filter.color
                    )}
                  >
                    {Icon && <Icon className="h-4 w-4 mr-2" />}
                    {filter.label}
                    <Badge variant="secondary" className="ml-2 text-xs">
                      {count}
                    </Badge>
                  </Button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="space-y-3">
          {shouldShowLoading ? (
            <div className="flex items-center justify-center py-16">
              <div className="text-center space-y-4">
                <Loader2 className="h-10 w-10 animate-spin mx-auto text-muted-foreground" />
                <p className="text-muted-foreground">Loading orders...</p>
              </div>
            </div>
          ) : filteredOrders.length === 0 ? (
            <div className="text-center py-16">
              <div className="space-y-4">
                <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mx-auto">
                  <Search className="h-8 w-8 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">Aucune commande trouvée</h3>
                  <p className="text-muted-foreground mt-1">
                    {search ? "Essayez d'ajuster votre recherche" : "Aucune commande ne correspond au filtre sélectionné"}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            filteredOrders.map(order => (
              <div
                key={order.id}
                className={cn(
                  "relative p-3 rounded-lg shadow-sm transition-all duration-200 ease-in-out border",
                  order.status === 'cancelled' && "opacity-70 border-dashed border-red-300 bg-red-50/20",
                  order.status === 'completed' && "bg-emerald-50/30 border-emerald-200 dark:bg-emerald-950/20 dark:border-emerald-800",
                  order.status === 'served' && "bg-violet-50/30 border-violet-200 dark:bg-violet-950/20 dark:border-violet-800",
                  order.status === 'pending' && "border-amber-200 bg-amber-50/20 dark:bg-amber-950/20 dark:border-amber-800",
                  isStaffOrder(order) && "bg-orange-50/50 border-orange-200 dark:bg-orange-950/50 dark:border-orange-800"
                )}
              >
                <div className="flex w-full items-start justify-between gap-3">
                  <div className="flex items-center gap-4 flex-1 min-w-0">
                    {/* Order ID & Type */}
                    <div className="flex flex-col gap-1 min-w-0 items-start">
                      <div className="font-mono text-sm font-bold">
                        #{order.id.slice(-6)}
                      </div>
                      <div className="flex gap-1 items-center">
                        <TypeBadge type={order.orderType} />
                        {isStaffOrder(order) && (
                          <Badge variant="secondary" className="bg-orange-100 text-orange-800 text-xs">
                            👥 Commande Équipe
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    {/* Status & Time */}
                    <div className="flex flex-col gap-1 min-w-0 items-start">
                      <div className="flex gap-1 items-center">
                        <StatusBadge status={order.status} />
                        <PaymentTag order={order} />
                      </div>
                      <div className="text-xs text-muted-foreground font-medium">
                        {format(new Date(order.createdAt), 'HH:mm')}
                      </div>
                    </div>
                    
                    {/* Items */}
                    <div className="flex-1 min-w-0 flex flex-col justify-center">
                      <div className="text-sm font-semibold truncate">
                        {order.items?.map(item => item.name).join(", ")}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {order.items?.length} article{order.items?.length !== 1 ? 's' : ''}
                      </div>
                    </div>

                    {/* Customer Info - For takeaway and delivery orders */}
                    {(order.orderType === 'takeaway' || order.orderType === 'delivery') && order.customer && (
                      <div className="flex flex-col gap-1 min-w-0 items-start">
                        {order.customer.name && (
                          <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                            <UserRound className="h-3 w-3" />
                            <span className="font-semibold">{order.customer.name}</span>
                          </div>
                        )}
                        {order.customer.phone && (
                          <div className="text-xs text-muted-foreground flex items-center gap-1">
                            <span className="text-muted-foreground">📞</span>
                            <span className="font-medium">{order.customer.phone}</span>
                          </div>
                        )}
                        {order.customer.address && (
                          <div className="text-xs text-muted-foreground flex items-center gap-1">
                            <span className="text-muted-foreground">📍</span>
                            <span className="font-medium truncate max-w-32" title={order.customer.address}>
                              {order.customer.address}
                            </span>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Delivery Person Info + Status */}
                    {order.orderType === 'delivery' && order.deliveryPerson && (
                      <div className="flex flex-col gap-1 min-w-0 items-start">
                        <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                          <Truck className="h-3 w-3" />
                          <span>
                            {order.deliveryPerson.type === 'staff' ? 'Staff' : 'Freelance'}: 
                            <span className="font-semibold ml-1">{order.deliveryPerson.name}</span>
                          </span>
                        </div>
                        <div className="text-xs text-muted-foreground flex items-center gap-1">
                          <span className="text-muted-foreground">📞</span>
                          <span className="font-medium">{order.deliveryPerson.phone}</span>
                        </div>
                        {/* 🚚 NEW: Delivery Status Badges */}
                        <div className="flex gap-1 mt-1">
                          <DeliveryStatusBadge deliveryStatus={order.deliveryStatus || 'delivered'} />
                          <CollectionStatusBadge order={order} />
                        </div>
                      </div>
                    )}
                    
                    {/* Total */}
                    <div className="text-right min-w-0">
                      <div className="text-xl font-bold">
                        {order.total} DA
                      </div>
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex items-center gap-2 ml-4">
                    {/* Edit Action */}
                    {(order.status !== 'cancelled' && order.status !== 'completed') && (
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="h-9 px-3 text-blue-600 border-blue-200 hover:bg-blue-50 hover:border-blue-300" 
                        onClick={() => {
                        console.log('🔧 [OrderList] Edit button clicked for order:', order.id || order._id);
                        // 🎯 Enhanced edit order with session management
                        setEditOrder(order);
                        if (setTab) setTab('ordering');
                      }}
                      >
                        <Pencil className="h-4 w-4 mr-1.5" />
                        Modifier
                      </Button>
                    )}
                    
                    {/* Mark as Served Action - Only for non-collection delivery orders */}
                    {(order.status === 'pending' && !isCollectionBasedDelivery(order)) && (
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="h-9 px-3 text-violet-600 border-violet-200 hover:bg-violet-50 hover:border-violet-300" 
                        onClick={() => handleMarkAsServed(order)}
                        disabled={isProcessing}
                      >
                        <Utensils className="h-4 w-4 mr-1.5" />
                        Servi
                      </Button>
                    )}
                    
                    {/* 🚚 NEW: Combined Serve + Send for Delivery (Collection-based) */}
                    {isCollectionBasedDelivery(order) && order.status === 'pending' && (
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="h-9 px-3 text-purple-600 border-purple-200 hover:bg-purple-50 hover:border-purple-300" 
                        onClick={() => handleServeAndSendForDelivery(order)}
                        disabled={isProcessing}
                      >
                        <Send className="h-4 w-4 mr-1.5" />
                        Servir & Livrer
                      </Button>
                    )}
                    
                    {/* 🚚 NEW: Delivery Actions - Only for completed delivery orders */}
                    {order.orderType === 'delivery' && order.status === 'completed' && order.deliveryPerson && (
                      <>
                        {/* Delivery Failure Handler - For partial failures */}
                        {(!order.deliveryStatus || order.deliveryStatus === 'pending' || order.deliveryStatus === 'out_for_delivery' || order.deliveryStatus === 'delivered') && !isMoneyActuallyCollected(order) && (
                          <DeliveryFailureHandler
                            order={order}
                            onSuccess={() => {
                              refreshOrders();
                              toast({
                                title: "⚠️ Livraison partielle",
                                description: `Articles échoués traités pour commande #${order.id.slice(-6)}`
                              });
                            }}
                            trigger={
                              <Button
                                size="sm"
                                variant="outline"
                                className="h-9 px-3 text-yellow-600 border-yellow-200 hover:bg-yellow-50 hover:border-yellow-300"
                                disabled={isProcessing}
                              >
                                <AlertTriangle className="h-4 w-4 mr-1.5" />
                                Échec Partiel
                              </Button>
                            }
                          />
                        )}

                        {/* Mark as Completely Failed */}
                        {(!order.deliveryStatus || order.deliveryStatus === 'pending' || order.deliveryStatus === 'out_for_delivery' || order.deliveryStatus === 'delivered') && !isMoneyActuallyCollected(order) && (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                size="sm"
                                variant="outline"
                                className="h-9 px-3 text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
                                disabled={isProcessing}
                              >
                                <XCircle className="h-4 w-4 mr-1.5" />
                                Échec Total
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Confirmer l'échec total de livraison</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Êtes-vous sûr de vouloir marquer cette commande comme un échec total de livraison ?
                                  Tous les articles seront marqués comme gaspillage et cette action ne peut pas être annulée.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Annuler</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleMarkDeliveryFailed(order)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Confirmer l'échec total
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                      </>
                    )}
                    
                    {/* 💰 Payment Action - Only for orders that need regular payment */}
                    {needsRegularPayment(order) && (order.status !== 'completed' && order.status !== 'cancelled') && (
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="h-9 px-3 text-emerald-600 border-emerald-200 hover:bg-emerald-50 hover:border-emerald-300" 
                        onClick={() => setPayOrder(order)}
                      >
                        <CreditCard className="h-4 w-4 mr-1.5" />
                        Payer
                      </Button>
                    )}
                    
                    {/* Cancel Action - Enhanced with waste tracking */}
                    {(order.status !== 'cancelled' && order.status !== 'completed') && (
                      <OrderCancellationHandler
                        order={order}
                        onSuccess={() => {
                          refreshOrders();
                          toast({
                            title: "Commande annulée",
                            description: `Commande #${order.id.slice(-6)} a été annulée`
                          });
                        }}
                        trigger={
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-9 px-3 text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
                          >
                            <X className="h-4 w-4 mr-1.5" />
                            Annuler
                          </Button>
                        }
                      />
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Dialogs */}

        {/* Payment Dialog - Only for orders that need regular payment */}
        <Dialog open={!!payOrder} onOpenChange={open => { if (!open) setPayOrder(null); }}>
          <DialogContent className="max-w-5xl w-[95vw] max-h-[90vh] p-0 overflow-hidden">
            <DialogHeader className="sr-only">
              <DialogTitle>Traitement du Paiement</DialogTitle>
            </DialogHeader>
            {payOrder && (
              <PaymentForm
                order={payOrder}
                isProcessing={isProcessing}
                errorMsg={errorMsg}
                onCancel={() => {
                  setPayOrder(null);
                  setReceivedAmount("");
                }}
                onSubmit={async (received, wasteData?: WasteProcessingData, discountData?: DiscountData) => {
                  setIsProcessing(true);
                  setErrorMsg("");
                  try {
                    console.log('Processing payment for order:', payOrder);
                    
                    // 🚀 Process waste BEFORE payment if there's waste data (reusing PaymentForm logic)
                    if (wasteData) {
                      console.log('Processing waste before payment:', wasteData);
                      
                      // Import the waste processing functions
                      const { processMenuItemWaste, processWastedMenuItemStockConsumption } = await import('@/lib/db/v4/operations/inventory-ops');
                      const { updateOrder } = await import('@/lib/db/v4/operations/order-ops');
                      
                      // Process menu item waste (what shows in UI)
                      await processMenuItemWaste(
                        payOrder.id || payOrder._id,
                        wasteData.wastedMenuItems,
                        wasteData.wasteReason,
                        wasteData.totalWasteValue
                      );
                      
                      // Process actual stock consumption behind the scenes (including supplements!)
                      await processWastedMenuItemStockConsumption(
                        wasteData.wasteItems,
                        payOrder.items.map(item => ({
                          menuItemId: item.menuItemId,
                          name: item.name,
                          quantity: item.quantity,
                          size: item.size,
                          addons: item.addons
                        }))
                      );
                      
                      // Update order to remove wasted items and adjust total
                      const updatedItems = payOrder.items.map((item, index) => {
                        const wasteItem = wasteData.wasteItems.find(w => w.itemIndex === index);
                        if (wasteItem) {
                          const remainingQuantity = item.quantity - wasteItem.quantity;
                          if (remainingQuantity > 0) {
                            return { ...item, quantity: remainingQuantity };
                          } else {
                            return null;
                          }
                        }
                        return item;
                      }).filter(item => item !== null);

                      const newTotal = updatedItems.reduce((total, item) => {
                        let itemPrice = item.price;
                        if (item.addons && item.addons.length > 0) {
                          itemPrice += item.addons.reduce((sum, addon) => sum + addon.price, 0);
                        }
                        return total + (itemPrice * item.quantity);
                      }, 0);

                      await updateOrder(payOrder.id || payOrder._id, {
                        items: updatedItems,
                        total: newTotal
                      });

                      // Update the local order object for payment processing
                      payOrder.items = updatedItems;
                      payOrder.total = newTotal;
                    }

                    // 💸 NEW: Process discount if provided
                    if (discountData) {
                      console.log('Processing discount:', discountData);
                      
                      // Update order with discount information
                      await updateOrder(payOrder.id || payOrder._id, {
                        discountType: discountData.discountType,
                        discountValue: discountData.discountValue,
                        discountAmount: discountData.discountAmount,
                        discountReason: discountData.discountReason,
                        subtotal: payOrder.total, // Current total becomes subtotal
                        total: payOrder.total - discountData.discountAmount // Apply discount to total
                      });
                      
                      // Update the local order object for payment processing
                      payOrder.total = payOrder.total - discountData.discountAmount;
                      payOrder.discountType = discountData.discountType;
                      payOrder.discountValue = discountData.discountValue;
                      payOrder.discountAmount = discountData.discountAmount;
                      payOrder.discountReason = discountData.discountReason;
                      payOrder.subtotal = discountData.subtotal;
                    }

                    // Process payment with potentially updated order using finance service
                    console.log('🔧 [OrderList] Calling finance service processOrderPayment...');
                    const paymentResult = await processOrderPayment(payOrder, 'cash', received);
                    console.log('🔧 [OrderList] Payment result:', paymentResult);
                    
                    if (paymentResult.success) {
                      console.log('🔧 [OrderList] Payment successful, registered in caisse:', paymentResult.registeredInCaisse);
                      setPayOrder(null);
                      setReceivedAmount("");
                      await refreshOrders();
                    } else {
                      throw new Error(paymentResult.error || 'Payment failed');
                    }
                  } catch (e) {
                    console.error('Payment processing error:', e);
                    setErrorMsg(e instanceof Error ? e.message : "Payment failed");
                  } finally {
                    setIsProcessing(false);
                  }
                }}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* Print Preview Dialog */}
        {showPrintPreview && printJob && (
          <PrintPreviewDialog
            open={showPrintPreview}
            onOpenChange={(open) => {
              if (!open) {
                setShowPrintPreview(false);
                setPrintJob(null);
              }
            }}
            printJob={printJob}
            onPrint={() => {}}
          />
        )}
      </div>
    </div>
  );
};

export default OrderList;
// knowledge:order-list:end