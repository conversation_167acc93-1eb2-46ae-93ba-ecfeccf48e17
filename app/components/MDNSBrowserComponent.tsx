'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react';

// Use the globally declared DiscoveredService interface from electron.d.ts
// If you want to be explicit, you can declare it again here
/*
interface DiscoveredService {
  name: string;
  host: string;
  port: number;
  type: string;
  addresses: string[];
  txtRecord?: Record<string, string>;
  fullname?: string;
}
*/

// Different statuses for the browser
type BrowserStatus = 'idle' | 'browsing' | 'error';

// Props for the component
interface MDNSBrowserProps {
  serviceType?: string;
  domain?: string;
  className?: string;
  onServiceSelected?: (service: DiscoveredService) => void;
}

/**
 * A component that can browse for mDNS services on the local network
 * using Electron's main process for the actual discovery.
 */
export default function MDNSBrowserComponent({
  serviceType = '_http._tcp',
  domain = 'local',
  className,
  onServiceSelected
}: MDNSBrowserProps) {
  // State for discovered services and browser status
  const [services, setServices] = useState<DiscoveredService[]>([]);
  const [status, setStatus] = useState<BrowserStatus>('idle');
  const [error, setError] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [isElectron, setIsElectron] = useState(false);
  
  // Check if we're in Electron
  useEffect(() => {
    setIsClient(true);
    
    // Check if Electron API is available
    const electronAvailable = typeof window !== 'undefined' && 
      'electronAPI' in window && 
      window.electronAPI?.mdnsDiscovery !== undefined;
      
    setIsElectron(electronAvailable);
    
    if (!electronAvailable) {
      setError('mDNS discovery is only available in Electron app. This feature won\'t work in the browser.');
    }
  }, []);
  
  // Function to start browsing for services
  const startBrowsing = async () => {
    try {
      if (!isElectron) {
        setError('mDNS discovery is only available in the Electron app');
        return;
      }
      
      setStatus('browsing');
      setError(null);
      setServices([]);
      setIsScanning(true);
      
      console.log(`🔍 Starting to browse for ${serviceType} services via Electron IPC...`);
      
      // Start discovery via Electron IPC
      const result = await window.electronAPI.mdnsDiscovery.startDiscovery(serviceType);
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to start discovery');
      }
      
      // Get initial services (if any were discovered before we set up the listener)
      const initialServices = await window.electronAPI.mdnsDiscovery.getDiscoveredServices();
      if (initialServices && initialServices.length > 0) {
        setServices(initialServices);
      }
      
      // Set a timeout to stop the "scanning" animation after 10 seconds
      // (services will continue to be discovered, but we won't show the scanning animation forever)
      setTimeout(() => {
        setIsScanning(false);
      }, 10000);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error('❌ Failed to start browsing:', errorMessage);
      setError(`Failed to start browsing: ${errorMessage}`);
      setStatus('error');
      setIsScanning(false);
    }
  };
  
  // Set up listener for service updates
  useEffect(() => {
    if (!isElectron) return;
    
    let cleanup: (() => void) | undefined;
    
    // Set up the event listener for service updates
    if (window.electronAPI?.mdnsDiscovery) {
      cleanup = window.electronAPI.mdnsDiscovery.onServiceUpdated((updatedServices) => {
        console.log('Received service update:', updatedServices);
        setServices(updatedServices);
      });
    }
    
    // Start browsing when component mounts
    startBrowsing();
    
    // Clean up when component unmounts
    return () => {
      if (cleanup) cleanup();
      
      // Stop discovery when component unmounts
      if (window.electronAPI?.mdnsDiscovery) {
        window.electronAPI.mdnsDiscovery.stopDiscovery()
          .catch(err => console.error('Error stopping discovery:', err));
      }
    };
  }, [isElectron, serviceType]); // Re-run when serviceType changes

  // Clear error and restart browsing
  const handleRetry = () => {
    startBrowsing();
  };

  // Handle service selection
  const handleServiceClick = (service: DiscoveredService) => {
    if (onServiceSelected) {
      onServiceSelected(service);
    }
  };

  // Format for display
  const getServiceDisplayAddress = (service: DiscoveredService): string => {
    if (service.addresses && service.addresses.length > 0) {
      return `${service.addresses[0]}:${service.port}`;
    }
    return `${service.host}:${service.port}`;
  };

  // Get clickable URL
  const getServiceUrl = (service: DiscoveredService): string | null => {
    if (!service.addresses || service.addresses.length === 0) return null;
    
    const address = service.addresses[0];
    const protocol = service.type.includes('https') ? 'https' : 'http';
    return `${protocol}://${address}:${service.port}`;
  };

  // Server-side rendering protection
  if (!isClient) {
    return null;
  }

  // Non-Electron fallback
  if (!isElectron) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="bg-yellow-100 border border-yellow-300 text-yellow-700 p-4 rounded-md">
          <h3 className="text-lg font-medium mb-2 flex items-center">
            <WifiOff className="mr-2 h-5 w-5" />
            mDNS Browser Unavailable
          </h3>
          <p>
            The mDNS browser is only available in the Electron app. Network service discovery requires Node.js 
            functionality that's not available in web browsers.
          </p>
          <p className="mt-2">
            Please use the desktop app to access this feature.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium flex items-center">
          {isScanning ? 
            <Wifi className="mr-2 h-5 w-5 text-green-500 animate-pulse" /> : 
            <Wifi className="mr-2 h-5 w-5" />
          }
          mDNS Browser {serviceType}
          {isScanning && <span className="text-green-500 ml-2 text-sm">Scanning...</span>}
        </h3>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleRetry}
          disabled={isScanning}
          className="flex items-center gap-1"
        >
          <RefreshCw className={cn("h-3 w-3", isScanning && "animate-spin")} />
          Rescan
        </Button>
      </div>
      
      {/* Error display */}
      {error && (
        <div className="bg-red-100 border border-red-300 text-red-500 p-2 rounded-md text-sm flex items-center">
          <WifiOff className="h-4 w-4 mr-2" />
          {error}
        </div>
      )}
      
      {/* Service list */}
      <div className="space-y-2">
        {services.length === 0 ? (
          <div className="text-center p-4 text-muted-foreground text-sm border rounded-md">
            {isScanning 
              ? '🔍 Searching for services on the network...' 
              : 'No services found. Try rescanning.'}
          </div>
        ) : (
          services.map((service, idx) => (
            <Card 
              key={`${service.name}-${service.host}-${idx}`}
              className="p-3 cursor-pointer hover:bg-muted/50 transition-colors"
              onClick={() => handleServiceClick(service)}
            >
              <div className="flex flex-col gap-1">
                <div className="font-medium">{service.name}</div>
                <div className="text-sm text-blue-500 underline">
                  {getServiceDisplayAddress(service)}
                </div>
                <div className="text-xs text-muted-foreground">
                  Type: {service.type}
                </div>
                {service.txtRecord && Object.keys(service.txtRecord).length > 0 && (
                  <div className="text-xs text-muted-foreground mt-1">
                    TXT: {Object.entries(service.txtRecord).map(([key, value]) => (
                      <span key={key} className="mr-2">
                        {key}={value}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </Card>
          ))
        )}
      </div>
    </div>
  );
} 