"use client"

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { InfoTooltip } from "@/app/components/ui/info-tooltip"
import { cn } from "@/lib/utils"

interface FinancialHealthIndicatorProps {
  profitMargin: number // as a percentage
  cashFlowTrend: 'positive' | 'negative' | 'neutral'
  cashReserve: number // days of operation that can be covered
  className?: string
}

export default function FinancialHealthIndicator({
  profitMargin,
  cashFlowTrend,
  cashReserve,
  className
}: FinancialHealthIndicatorProps) {
  // Calculate overall health score (0-100)
  const calculateHealthScore = (): number => {
    // Profit margin contributes 40% to the score (healthy: >15%)
    const profitScore = Math.min(100, Math.max(0, (profitMargin / 20) * 100)) * 0.4

    // Cash flow trend contributes 30% to the score
    const cashFlowScore =
      cashFlowTrend === 'positive' ? 100 * 0.3 :
      cashFlowTrend === 'neutral' ? 50 * 0.3 : 0

    // Cash reserve contributes 30% to the score (healthy: >14 days)
    const reserveScore = Math.min(100, Math.max(0, (cashReserve / 14) * 100)) * 0.3

    return Math.round(profitScore + cashFlowScore + reserveScore)
  }

  const healthScore = calculateHealthScore()

  // Determine health status and color
  const getHealthStatus = (): { label: string; color: string } => {
    if (healthScore >= 70) {
      return { label: 'Healthy', color: 'green' }
    } else if (healthScore >= 40) {
      return { label: 'Moderate', color: 'amber' }
    } else {
      return { label: 'Needs Attention', color: 'red' }
    }
  }

  const healthStatus = getHealthStatus()

  // Generate improvement suggestions
  const getImprovementSuggestions = (): string[] => {
    const suggestions: string[] = []

    if (profitMargin < 15) {
      suggestions.push('Consider reviewing pricing or reducing expenses to improve profit margin.')
    }

    if (cashFlowTrend === 'negative') {
      suggestions.push('Monitor cash flow closely and identify areas to improve income or reduce expenses.')
    }

    if (cashReserve < 7) {
      suggestions.push('Work on building cash reserves to improve financial stability.')
    }

    return suggestions.length > 0 ? suggestions : ['Your financial health looks good. Continue monitoring key metrics.']
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">Financial Health</CardTitle>
            <CardDescription>
              Overall financial status
              <InfoTooltip
                content={
                  <div>
                    <p>Financial health is calculated based on:</p>
                    <ul className="list-disc pl-4 mt-1 space-y-1">
                      <li>Profit margin (40%)</li>
                      <li>Cash flow trend (30%)</li>
                      <li>Cash reserve adequacy (30%)</li>
                    </ul>
                  </div>
                }
                iconClassName="h-3.5 w-3.5"
              />
            </CardDescription>
          </div>
          <div className={cn(
            "px-2 py-1 rounded-full text-xs font-medium",
            healthStatus.color === 'green' ? "bg-green-100 text-green-800" :
            healthStatus.color === 'amber' ? "bg-amber-100 text-amber-800" :
            "bg-red-100 text-red-800"
          )}>
            {healthStatus.label}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Health gauge */}
          <div className="relative h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className={cn(
                "absolute top-0 left-0 h-full rounded-full",
                healthScore >= 70 ? "bg-green-500" :
                healthScore >= 40 ? "bg-amber-500" : "bg-red-500"
              )}
              style={{ width: `${healthScore}%` }}
            />
          </div>

          {/* Key metrics */}
          <div className="grid grid-cols-3 gap-2 text-center text-sm">
            <div>
              <p className="text-muted-foreground text-xs">Profit Margin</p>
              <p className={cn(
                "font-medium",
                profitMargin >= 15 ? "text-green-600" :
                profitMargin >= 5 ? "text-amber-600" : "text-red-600"
              )}>
                {profitMargin.toFixed(1)}%
              </p>
            </div>
            <div>
              <p className="text-muted-foreground text-xs">Cash Flow</p>
              <p className={cn(
                "font-medium",
                cashFlowTrend === 'positive' ? "text-green-600" :
                cashFlowTrend === 'neutral' ? "text-amber-600" : "text-red-600"
              )}>
                {cashFlowTrend === 'positive' ? 'Positive' :
                 cashFlowTrend === 'neutral' ? 'Stable' : 'Negative'}
              </p>
            </div>
            <div>
              <p className="text-muted-foreground text-xs">Cash Reserve</p>
              <p className={cn(
                "font-medium",
                cashReserve >= 14 ? "text-green-600" :
                cashReserve >= 7 ? "text-amber-600" : "text-red-600"
              )}>
                {cashReserve} days
              </p>
            </div>
          </div>

          {/* Suggestions */}
          <div className="text-xs space-y-1 mt-2">
            <p className="font-medium">Suggestions:</p>
            <ul className="list-disc pl-4 space-y-1 text-muted-foreground">
              {getImprovementSuggestions().map((suggestion, index) => (
                <li key={index}>{suggestion}</li>
              ))}
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
