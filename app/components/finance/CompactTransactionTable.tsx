"use client"

import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { format, parseISO, isValid } from 'date-fns';
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ArrowUpIcon,
  ArrowDownIcon,
  RefreshCwIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  BanknoteIcon,
  CreditCardIcon,
  DollarSignIcon,
  UserIcon,
  ClockIcon,
  Loader2,
  Trash2,
  ShoppingCart,
  Truck,
  Receipt,
  Calculator
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils/currency';
import { Badge } from "@/components/ui/badge";
import { cn } from '@/lib/utils';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

export interface UnifiedTransaction {
  id: string;
  type: 'sales' | 'manual_in' | 'manual_out' | 'expense' | 'order' | 'opening' | 'closing' | 'count';
  amount: number;
  description: string;
  time: string;
  performedBy: string;
  category?: string;
  notes?: string;
  relatedDocId?: string;
  sessionId?: string;
  metadata?: {
    transactionCategory?: 'order_payment' | 'delivery_collection' | 'manual_entry' | 'expense';
    orderType?: 'delivery' | 'takeaway' | 'dine-in' | 'table';
    orderSummary?: {
      id: string;
      total: number;
      items: string;
      orderType: string;
      customer: string;
      tableId?: string;
      deliveryPerson?: string;
      createdAt: string;
    };
    staffId?: string;
    staffName?: string;
    orderCount?: number;
    orderIds?: string[];
    paymentMethod?: string;
    receivedAmount?: number;
    change?: number;
    itemCount?: number;
    customer?: string;
    deliveryPerson?: string;
    tableId?: string;
    expectedAmount?: number;
    actualAmount?: number;
    discrepancy?: number;
    discrepancyReason?: string;
  };
}

interface CompactTransactionTableProps {
  transactions: UnifiedTransaction[];
  onRefresh: () => void;
  onViewDetails?: (transaction: UnifiedTransaction) => void;
  isLoading?: boolean;
  enableHourlyGrouping?: boolean;
  isRefreshing?: boolean;
}

export default function CompactTransactionTable({
  transactions,
  onRefresh,
  onViewDetails,
  isLoading = false,
  enableHourlyGrouping = false,
  isRefreshing = false
}: CompactTransactionTableProps) {
  const [activeFilter, setActiveFilter] = useState<'all' | 'income' | 'outcome'>('all');
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});
  const [expandedDays, setExpandedDays] = useState<Record<string, boolean>>({});

  // Toggle group expansion
  const toggleGroupExpanded = (groupId: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };

  // Toggle day expansion  
  const toggleDayExpanded = (dateStr: string) => {
    setExpandedDays(prev => ({
      ...prev,
      [dateStr]: !prev[dateStr]
    }));
  };

  // Get transaction icon
  const getTransactionIcon = (type: string, metadata?: UnifiedTransaction['metadata']) => {
    if (metadata?.transactionCategory === 'order_payment') {
      return <Receipt className="h-3.5 w-3.5 text-green-600" />;
    }
    if (metadata?.transactionCategory === 'delivery_collection') {
      return <Truck className="h-3.5 w-3.5 text-purple-600" />;
    }
    if (type === 'expense') {
      return <Trash2 className="h-3.5 w-3.5 text-red-600" />;
    }
    
    switch (type) {
      case 'sales':
      case 'order':
        return <CreditCardIcon className="h-3.5 w-3.5 text-green-600" />;
      case 'manual_in':
        return <ArrowUpIcon className="h-3.5 w-3.5 text-green-600" />;
      case 'manual_out':
        return <ArrowDownIcon className="h-3.5 w-3.5 text-red-600" />;
      case 'opening':
        return <BanknoteIcon className="h-3.5 w-3.5 text-blue-600" />;
      case 'closing':
      case 'count':
        return <Calculator className="h-3.5 w-3.5 text-orange-600" />;
      default:
        return <DollarSignIcon className="h-3.5 w-3.5 text-gray-600" />;
    }
  };

  // Get transaction type label
  const getTransactionTypeLabel = (type: string, metadata?: UnifiedTransaction['metadata']) => {
    if (metadata?.transactionCategory === 'order_payment') {
      return 'Commande';
    }
    if (metadata?.transactionCategory === 'delivery_collection') {
      return 'Collecte';
    }
    if (type === 'expense') {
      return 'Gaspillage';
    }
    
    switch (type) {
      case 'sales':
      case 'order':
        return 'Vente';
      case 'manual_in':
        return 'Dépôt';
      case 'manual_out':
        return 'Retrait';
      case 'opening':
        return 'Ouverture';
      case 'closing':
      case 'count':
        return 'Comptage';
      default:
        return type;
    }
  };

  // Format time
  const formatTransactionTime = (time: string) => {
    try {
      const date = new Date(time);
      if (!isValid(date)) return 'Invalid';
      return format(date, 'HH:mm');
    } catch (e) {
      return 'Invalid';
    }
  };

  // Format date
  const formatTransactionDate = (time: string) => {
    try {
      const date = new Date(time);
      if (!isValid(date)) return 'Invalid Date';
      return format(date, 'dd/MM/yyyy');
    } catch (e) {
      return 'Invalid Date';
    }
  };

  // Format hour range
  const formatHourRange = (hour: number) => {
    const nextHour = (hour + 1) % 24;
    return `${hour.toString().padStart(2, '0')}:00 - ${nextHour.toString().padStart(2, '0')}:00`;
  };

  // Filter transactions
  const getFilteredTransactions = () => {
    return transactions.filter(transaction => {
      if (activeFilter === 'income') {
        return transaction.amount > 0;
      } else if (activeFilter === 'outcome') {
        return transaction.amount < 0;
      }
      return true;
    });
  };

  // Group transactions by date and hour
  const groupedTransactions = useMemo(() => {
    const filteredTransactions = getFilteredTransactions();
    const grouped: Record<string, Record<string, UnifiedTransaction[]>> = {};
    
    filteredTransactions.forEach(transaction => {
      try {
        const txDate = new Date(transaction.time);
        if (!isValid(txDate)) return;
        
        const dateStr = format(txDate, 'yyyy-MM-dd');
        
        if (!grouped[dateStr]) {
          grouped[dateStr] = {};
        }
        
        if (enableHourlyGrouping) {
          const hour = txDate.getHours();
          const hourKey = `${hour}`;
          
          if (!grouped[dateStr][hourKey]) {
            grouped[dateStr][hourKey] = [];
          }
          
          grouped[dateStr][hourKey].push(transaction);
        } else {
          const allDayKey = 'all';
          if (!grouped[dateStr][allDayKey]) {
            grouped[dateStr][allDayKey] = [];
          }
          grouped[dateStr][allDayKey].push(transaction);
        }
      } catch (error) {
        console.error("Error grouping transaction:", error);
      }
    });
    
    return Object.entries(grouped)
      .sort(([dateA], [dateB]) => new Date(dateB).getTime() - new Date(dateA).getTime());
  }, [getFilteredTransactions, enableHourlyGrouping]);

  // Calculate group summary
  const calculateGroupSummary = (transactions: UnifiedTransaction[]) => {
    const total = transactions.reduce((sum, tx) => sum + tx.amount, 0);
    const inflow = transactions.filter(tx => tx.amount > 0).reduce((sum, tx) => sum + tx.amount, 0);
    const outflow = Math.abs(transactions.filter(tx => tx.amount < 0).reduce((sum, tx) => sum + tx.amount, 0));
    const count = transactions.length;
    
    return { total, inflow, outflow, count };
  };

  // Get order details for display
  const getOrderDetails = (transaction: UnifiedTransaction) => {
    const metadata = transaction.metadata;
    if (!metadata?.orderSummary) return null;
    
    const order = metadata.orderSummary;
    return {
      orderId: order.id ? (order.id.split('-').pop() || order.id) : 'N/A', // Get daily sequence safely
      customer: order.customer || 'Client',
      orderType: order.orderType || 'N/A',
      items: order.items || 'N/A',
      tableId: order.tableId,
      deliveryPerson: order.deliveryPerson
    };
  };

  return (
    <div className="border rounded-lg overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-2 border-b bg-muted/20">
        <div className="flex items-center gap-2">
          <DollarSignIcon className="h-4 w-4 text-primary" />
          <h3 className="font-semibold">💰 Historique des Transactions</h3>
        </div>
        <div className="flex items-center gap-2">
          {/* Filter buttons */}
          <Button 
            variant={activeFilter === 'all' ? "default" : "outline"} 
            size="sm"
            onClick={() => setActiveFilter('all')}
            className="h-7 text-xs"
          >
            Tout
          </Button>
          <Button 
            variant={activeFilter === 'income' ? "default" : "outline"} 
            size="sm"
            onClick={() => setActiveFilter('income')}
            className="h-7 text-xs"
          >
            💰 Entrées
          </Button>
          <Button 
            variant={activeFilter === 'outcome' ? "default" : "outline"} 
            size="sm"
            onClick={() => setActiveFilter('outcome')}
            className="h-7 text-xs"
          >
            💸 Sorties
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onRefresh} 
            className="h-7"
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <Loader2 className="h-3.5 w-3.5 animate-spin" />
            ) : (
              <RefreshCwIcon className="h-3.5 w-3.5" />
            )}
          </Button>
        </div>
      </div>

      {/* Content */}
      <ScrollArea className="h-[600px]">
        {groupedTransactions.length === 0 ? (
          <div className="py-8 text-center text-muted-foreground text-sm">
            Aucune transaction trouvée
          </div>
        ) : (
          <div>
            {groupedTransactions.map(([date, hourGroups], index) => {
              const isDayExpanded = expandedDays[date] ?? (index === 0); // Only expand the most recent day by default
              const dayTransactionCount = Object.values(hourGroups).reduce((total, txs) => total + txs.length, 0);
              const dayTotal = Object.values(hourGroups).flat().reduce((sum, tx) => sum + tx.amount, 0);
              
              return (
                <div key={date}>
                  {/* Date header - Simple with blue color coding */}
                  <button 
                    className="w-full bg-blue-50 hover:bg-blue-100 p-2 border-b border-blue-200 text-left"
                    onClick={() => toggleDayExpanded(date)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <ChevronDownIcon className={cn("h-4 w-4", isDayExpanded && "rotate-180")} />
                        <span className="text-sm font-medium">📅 {formatTransactionDate(date)}</span>
                        <span className="text-xs text-muted-foreground">
                          {dayTransactionCount} transaction{dayTransactionCount > 1 ? 's' : ''}
                        </span>
                      </div>
                      <span className={cn(
                        "text-sm font-medium",
                        dayTotal >= 0 ? 'text-green-600' : 'text-red-600'
                      )}>
                        {dayTotal >= 0 ? '+' : ''}{formatCurrency(dayTotal)}
                      </span>
                    </div>
                  </button>
                
                  {/* Hour groups - Simple */}
                  {isDayExpanded && (
                    <div>
                      {Object.entries(hourGroups)
                        .sort(([hourA], [hourB]) => parseInt(hourB) - parseInt(hourA))
                        .map(([hour, txs]) => {
                          const groupId = `${date}_${hour}`;
                          const isGroupExpanded = expandedGroups[groupId] ?? true;
                          const summary = calculateGroupSummary(txs);
                          
                          return (
                            <div key={groupId}>
                              {/* Hour group header - Simple with orange color coding */}
                              {enableHourlyGrouping && (
                                <button 
                                  className="w-full bg-orange-50 hover:bg-orange-100 px-3 py-1.5 border-b border-orange-200 text-left"
                                  onClick={() => toggleGroupExpanded(groupId)}
                                >
                                  <div className="flex justify-between items-center">
                                    <div className="flex items-center gap-2">
                                      <ChevronDownIcon className={cn("h-3.5 w-3.5", isGroupExpanded && "rotate-180")} />
                                      <ClockIcon className="h-3.5 w-3.5" />
                                      <span className="text-sm">
                                        {formatHourRange(parseInt(hour))}
                                      </span>
                                      <span className="text-xs text-muted-foreground">
                                        {summary.count} transaction{summary.count > 1 ? 's' : ''}
                                      </span>
                                    </div>
                                    <div className="text-xs">
                                      <span className="font-medium">{formatCurrency(summary.total)}</span>
                                      <span className="ml-2 text-green-600">+{formatCurrency(summary.inflow)}</span>
                                      <span className="ml-1 text-red-600">-{formatCurrency(summary.outflow)}</span>
                                    </div>
                                  </div>
                                </button>
                              )}
                              
                              {/* Transactions table - Clean and simple */}
                              {isGroupExpanded && (
                                <Table>
                                  <TableHeader>
                                    <TableRow className="h-8">
                                      <TableHead className="text-xs py-1 px-2">Commande</TableHead>
                                      <TableHead className="text-xs py-1 px-2">Client</TableHead>
                                      <TableHead className="text-xs py-1 px-2">Type</TableHead>
                                      <TableHead className="text-xs py-1 px-2">Articles</TableHead>
                                      <TableHead className="text-xs py-1 px-2">Heure</TableHead>
                                      <TableHead className="text-xs py-1 px-2 text-right">Montant</TableHead>
                                      <TableHead className="text-xs py-1 px-2">Staff</TableHead>
                                    </TableRow>
                                  </TableHeader>
                                  <TableBody>
                                    {txs.map((transaction) => {
                                      const orderDetails = getOrderDetails(transaction);
                                      const isOrder = transaction.metadata?.transactionCategory === 'order_payment';
                                      const isDelivery = transaction.metadata?.transactionCategory === 'delivery_collection';
                                      
                                      return (
                                        <TableRow 
                                          key={transaction.id} 
                                          className="h-8 hover:bg-muted/20 cursor-pointer"
                                          onClick={() => onViewDetails?.(transaction)}
                                        >
                                            {/* Order ID */}
                                            <TableCell className="py-1 px-2">
                                              {isOrder && orderDetails ? (
                                                <span className="font-mono text-xs font-bold">
                                                  #{orderDetails.orderId}
                                                </span>
                                              ) : isDelivery ? (
                                                <Badge variant="outline" className="text-xs px-1 py-0 bg-purple-50 text-purple-700 border-purple-200">
                                                  🚚 Collecte
                                                </Badge>
                                              ) : (
                                                <Badge 
                                                  variant={transaction.amount >= 0 ? "default" : "destructive"} 
                                                  className="text-xs px-1.5 py-0"
                                                >
                                                  {getTransactionTypeLabel(transaction.type, transaction.metadata)}
                                                </Badge>
                                              )}
                                            </TableCell>
                                            
                                            {/* Customer */}
                                            <TableCell className="py-1 px-2">
                                              {isOrder && orderDetails ? (
                                                <span className="text-xs font-medium">{orderDetails.customer}</span>
                                              ) : isDelivery ? (
                                                <span className="text-xs text-muted-foreground">Livreur</span>
                                              ) : (
                                                <span className="text-xs text-muted-foreground">-</span>
                                              )}
                                            </TableCell>
                                            
                                            {/* Order Type */}
                                            <TableCell className="py-1 px-2">
                                              {isOrder && orderDetails ? (
                                                <Badge 
                                                  variant="secondary" 
                                                  className={cn(
                                                    "text-xs px-1 py-0",
                                                    orderDetails.orderType === 'delivery' && "bg-purple-100 text-purple-700",
                                                    orderDetails.orderType === 'takeaway' && "bg-blue-100 text-blue-700",
                                                    orderDetails.orderType === 'dine-in' && "bg-green-100 text-green-700"
                                                  )}
                                                >
                                                  {orderDetails.orderType === 'delivery' && '🚚 Livraison'}
                                                  {orderDetails.orderType === 'takeaway' && '📦 À Emporter'}
                                                  {orderDetails.orderType === 'dine-in' && '🍽️ Sur Place'}
                                                  {!['delivery', 'takeaway', 'dine-in'].includes(orderDetails.orderType) && orderDetails.orderType}
                                                </Badge>
                                              ) : isDelivery ? (
                                                <Badge variant="outline" className="text-xs px-1 py-0">
                                                  Collecte
                                                </Badge>
                                              ) : (
                                                <Badge variant="outline" className="text-xs px-1 py-0">
                                                  {transaction.type === 'manual_in' && 'Dépôt'}
                                                  {transaction.type === 'manual_out' && 'Retrait'}
                                                  {transaction.type === 'opening' && 'Ouverture'}
                                                  {transaction.type === 'closing' && 'Comptage'}
                                                  {transaction.type === 'count' && 'Comptage'}
                                                  {transaction.type === 'expense' && 'Gaspillage'}
                                                  {!['manual_in', 'manual_out', 'opening', 'closing', 'count', 'expense'].includes(transaction.type) && 'Autre'}
                                                </Badge>
                                              )}
                                            </TableCell>
                                            
                                            {/* Articles */}
                                            <TableCell className="py-1 px-2">
                                              {isOrder && transaction.metadata?.itemCount ? (
                                                <span className="text-xs">
                                                  {transaction.metadata.itemCount} article{transaction.metadata.itemCount > 1 ? 's' : ''}
                                                </span>
                                              ) : isDelivery && transaction.metadata?.orderCount ? (
                                                <span className="text-xs">
                                                  {transaction.metadata.orderCount} commande{transaction.metadata.orderCount > 1 ? 's' : ''}
                                                </span>
                                              ) : (
                                                <span className="text-xs text-muted-foreground">-</span>
                                              )}
                                            </TableCell>
                                            
                                            {/* Time */}
                                            <TableCell className="py-1 px-2 text-xs text-muted-foreground">
                                              {formatTransactionTime(transaction.time)}
                                            </TableCell>
                                            
                                            {/* Amount */}
                                            <TableCell className="py-1 px-2 text-right">
                                              <span className={cn(
                                                "text-sm font-semibold",
                                                transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'
                                              )}>
                                                {transaction.amount >= 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                                              </span>
                                            </TableCell>
                                            
                                            {/* Staff */}
                                            <TableCell className="py-1 px-2">
                                              <span className="text-xs font-medium">
                                                {transaction.performedBy}
                                              </span>
                                            </TableCell>
                                          </TableRow>
                                        );
                                      })}
                                    </TableBody>
                                  </Table>
                              )}
                            </div>
                          );
                        })}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </ScrollArea>
    </div>
  );
} 