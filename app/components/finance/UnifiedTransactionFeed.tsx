"use client"

import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { format, parseISO, isValid } from 'date-fns';
import { Button } from "@/components/ui/button";
import {
  ArrowUpIcon,
  ArrowDownIcon,
  RefreshCwIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  BanknoteIcon,
  CreditCardIcon,
  DollarSignIcon,
  UserIcon,
  ClockIcon,
  Loader2,
  Trash2,
  TruckIcon
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils/currency';
import { getOrderTypeLabel, getOrderTypeUIConfig } from '@/lib/types/order-types';

import { Badge } from "@/components/ui/badge";
import { cn } from '@/lib/utils';
import { Skeleton } from "@/components/ui/skeleton";

export interface UnifiedTransaction {
  id: string;
  type: 'sales' | 'manual_in' | 'manual_out' | 'expense' | 'order' | 'opening' | 'closing' | 'count';
  amount: number;
  description: string;
  time: string;
  performedBy: string;
  category?: string;
  notes?: string;
  relatedDocId?: string;
  sessionId?: string;
  metadata?: {
    transactionCategory?: 'order_payment' | 'delivery_collection' | 'manual_entry' | 'expense';
    orderType?: 'delivery' | 'takeaway' | 'dine-in' | 'table';
    orderSummary?: {
      id: string;
      total: number;
      items: string;
      orderType: string;
      customer: string;
      tableId?: string;
      deliveryPerson?: string;
      createdAt: string;
    };
    staffId?: string;
    staffName?: string;
    orderCount?: number;
    orderIds?: string[];
    paymentMethod?: string;
    receivedAmount?: number;
    change?: number;
    itemCount?: number;
    customer?: string;
    deliveryPerson?: string;
    tableId?: string;
    expectedAmount?: number;
    actualAmount?: number;
    discrepancy?: number;
    discrepancyReason?: string;
    // Note: Waste transactions removed - caisse only tracks actual money flows
  };
}

interface UnifiedTransactionFeedProps {
  transactions: UnifiedTransaction[];
  onRefresh: () => void;
  onViewDetails?: (transaction: UnifiedTransaction) => void;
  isLoading?: boolean;
  enableHourlyGrouping?: boolean;
  isRefreshing?: boolean;
}

interface OrderDetails {
  id: string;
  orderNumber: string;
  orderType?: 'delivery' | 'takeaway' | 'dine-in' | 'table';
  tableNumber?: string;
  customerInfo?: string;
  staffName?: string;
  status?: 'pending' | 'preparing' | 'served' | 'completed' | 'cancelled';
}

export default function UnifiedTransactionFeed({
  transactions,
  onRefresh,
  onViewDetails,
  isLoading = false,
  enableHourlyGrouping = false,
  isRefreshing = false
}: UnifiedTransactionFeedProps) {
  // State for expanded transaction cards and groups
  const [expandedTransactions, setExpandedTransactions] = useState<Record<string, boolean>>({});
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});
  
  // State for active filter
  const [activeFilter, setActiveFilter] = useState<string>('all');

  // 🔍 DEBUG: Log transactions with metadata
  React.useEffect(() => {
    const transactionsWithMetadata = transactions.filter(tx => tx.metadata);
    if (transactionsWithMetadata.length > 0) {
      console.log('🔍 [UnifiedTransactionFeed] Found transactions with metadata:', transactionsWithMetadata.length);
      console.log('🔍 [UnifiedTransactionFeed] Sample metadata:', transactionsWithMetadata[0]?.metadata);
    } else {
      console.log('🔍 [UnifiedTransactionFeed] No transactions with metadata found');
    }
  }, [transactions]);

  // Toggle expanded state for a transaction
  const toggleExpanded = (id: string) => {
    setExpandedTransactions(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Toggle expanded state for a group
  const toggleGroupExpanded = (groupId: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };

  // Function to get transaction icon with enhanced categories
  const getTransactionIcon = (type: string, metadata?: UnifiedTransaction['metadata']) => {
    // Check metadata for more specific icons
    if (metadata?.transactionCategory === 'delivery_collection') {
      return <UserIcon className="h-4 w-4 text-blue-500" />;
    }
    if (metadata?.transactionCategory === 'order_payment') {
      return getOrderTypeIcon(metadata.orderType);
    }
    // 🚀 REMOVED: waste_loss transactions no longer exist
    
    // Default icons based on type
    switch (type) {
      case 'opening': return <BanknoteIcon className="h-4 w-4 text-blue-500" />;
      case 'closing': return <BanknoteIcon className="h-4 w-4 text-blue-500" />;
      case 'sales': return <CreditCardIcon className="h-4 w-4 text-green-500" />;
      case 'manual_in': return <ArrowUpIcon className="h-4 w-4 text-green-500" />;
      case 'manual_out': return <ArrowDownIcon className="h-4 w-4 text-red-500" />;
      case 'expense': return <ArrowDownIcon className="h-4 w-4 text-red-500" />;
      case 'order': return <DollarSignIcon className="h-4 w-4 text-green-500" />;
      case 'count': return <RefreshCwIcon className="h-4 w-4 text-amber-500" />;
      default: return <BanknoteIcon className="h-4 w-4" />;
    }
  };

  // Enhanced transaction type label with metadata
  const getTransactionTypeLabel = (type: string, metadata?: UnifiedTransaction['metadata']) => {
    if (metadata?.transactionCategory === 'delivery_collection') {
      return 'Collecte Livreur';
    }
    if (metadata?.transactionCategory === 'order_payment') {
      return getOrderTypeText(metadata.orderType);
    }
    // 🚀 REMOVED: waste_loss transactions no longer exist
    
    switch (type) {
      case 'opening': return 'Ouverture';
      case 'closing': return 'Fermeture';
      case 'sales': return 'Vente Caisse';
      case 'manual_in': return 'Dépôt Caisse';
      case 'manual_out': return 'Retrait Caisse';
      case 'expense': return 'Dépense';
      case 'order': return 'Commande';
      case 'count': return 'Comptage';
      default: return type;
    }
  };

  // Format transaction time
  const formatTransactionTime = (time: string) => {
    if (!time) return '';
    let date: Date;
    // Try parsing as ISO first
    try {
      date = parseISO(time);
      if (!isValid(date)) {
        // Fallback: try native Date
        date = new Date(time);
      }
    } catch {
      date = new Date(time);
    }
    if (!isValid(date)) return '';
    return format(date, 'HH:mm');
  };

  // Format transaction date
  const formatTransactionDate = (time: string) => {
    if (!time) return '';
    let date: Date;
    try {
      date = parseISO(time);
      if (!isValid(date)) {
        date = new Date(time);
      }
    } catch {
      date = new Date(time);
    }
    if (!isValid(date)) return '';
    return format(date, 'dd/MM/yyyy');
  };

  // Format hour range for hourly grouping
  const formatHourRange = (hour: number) => {
    const startHour = `${hour.toString().padStart(2, '0')}:00`;
    const endHour = `${hour.toString().padStart(2, '0')}:59`;
    return `${startHour} - ${endHour}`;
  };

  // Filter transactions based on active filter
  const getFilteredTransactions = () => {
    if (activeFilter === 'all') return transactions;
    
    return transactions.filter(transaction => {
      switch (activeFilter) {
        case 'income':
          // 🔧 NOTE: 'sales' transactions are created when orders are paid
          // 'order' type is legacy, 'manual_in' is manual cash entries
          return transaction.type === 'sales' || transaction.type === 'order' || transaction.type === 'manual_in';
        case 'outcome':
          return transaction.type === 'manual_out' || transaction.type === 'expense';
        default:
          return true;
      }
    });
  };

  // Group transactions by date and hour (if hourly grouping is enabled)
  const groupedTransactions = useMemo(() => {
    const filteredTransactions = getFilteredTransactions();
    const grouped: Record<string, Record<string, UnifiedTransaction[]>> = {};
    
    filteredTransactions.forEach(transaction => {
      try {
        // Parse the transaction time
        const txDate = new Date(transaction.time);
        if (!isValid(txDate)) return;
        
        // Extract date string (YYYY-MM-DD)
        const dateStr = format(txDate, 'yyyy-MM-dd');
        
        // Initialize the date group if it doesn't exist
        if (!grouped[dateStr]) {
          grouped[dateStr] = {};
        }
        
        if (enableHourlyGrouping) {
          // Extract hour for hourly grouping
          const hour = txDate.getHours();
          const hourKey = `${hour}`;
          
          // Initialize the hour group if it doesn't exist
          if (!grouped[dateStr][hourKey]) {
            grouped[dateStr][hourKey] = [];
          }
          
          // Add transaction to the appropriate hour group
          grouped[dateStr][hourKey].push(transaction);
        } else {
          // Without hourly grouping, use a single bucket for the day
          const allDayKey = 'all';
          if (!grouped[dateStr][allDayKey]) {
            grouped[dateStr][allDayKey] = [];
          }
          grouped[dateStr][allDayKey].push(transaction);
        }
      } catch (error) {
        console.error("Error grouping transaction:", error);
      }
    });
    
    // Sort everything by most recent first
    return Object.entries(grouped)
      .sort(([dateA], [dateB]) => new Date(dateB).getTime() - new Date(dateA).getTime());
  }, [getFilteredTransactions, enableHourlyGrouping]);

  // Calculate summary for a group of transactions
  const calculateGroupSummary = (transactions: UnifiedTransaction[]) => {
    const total = transactions.reduce((sum, tx) => sum + tx.amount, 0);
    const inflow = transactions
      .filter(tx => tx.amount > 0)
      .reduce((sum, tx) => sum + tx.amount, 0);
    const outflow = transactions
      .filter(tx => tx.amount < 0)
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
    
    return {
      count: transactions.length,
      total,
      inflow,
      outflow
    };
  };
  
  // Get additional badges for transaction
  const getTransactionBadges = (transaction: UnifiedTransaction) => {
    const badges = [];
    
    if (transaction.metadata?.transactionCategory === 'delivery_collection') {
      badges.push(
        <Badge key="delivery" variant="outline" className="text-[10px] px-1.5 py-0 bg-blue-50 text-blue-700">
          {transaction.metadata.staffName}
        </Badge>
      );
      if (transaction.metadata.orderCount) {
        badges.push(
          <Badge key="orders" variant="outline" className="text-[10px] px-1.5 py-0">
            {transaction.metadata.orderCount} cmd
          </Badge>
        );
      }
    }
    
    if (transaction.metadata?.transactionCategory === 'order_payment') {
      if (transaction.metadata.customer && transaction.metadata.customer !== 'Client') {
        badges.push(
          <Badge key="customer" variant="outline" className="text-[10px] px-1.5 py-0">
            {transaction.metadata.customer}
          </Badge>
        );
      }
      if (transaction.metadata.deliveryPerson) {
        badges.push(
          <Badge key="driver" variant="outline" className="text-[10px] px-1.5 py-0 bg-purple-50 text-purple-700">
            {transaction.metadata.deliveryPerson}
          </Badge>
        );
      }
      if (transaction.metadata.tableId) {
        badges.push(
          <Badge key="table" variant="outline" className="text-[10px] px-1.5 py-0">
            Table {transaction.metadata.tableId}
          </Badge>
        );
      }
    }
    

    
    return badges;
  };

  const getOrderTypeIcon = (type: string | undefined) => {
    const config = getOrderTypeUIConfig(type);
    switch (config.icon) {
      case 'Truck': return <TruckIcon className="h-4 w-4 text-green-500" />;
      case 'Package': return <CreditCardIcon className="h-4 w-4 text-orange-500" />;
      case 'Utensils': return <CreditCardIcon className="h-4 w-4 text-green-500" />;
      default: return <CreditCardIcon className="h-4 w-4 text-blue-500" />;
    }
  };

  const getOrderTypeText = (type: string | undefined) => {
    return getOrderTypeLabel(type);
  };

  // Render loading skeleton
  if (isLoading) {
    return (
      <Card className="border-border shadow-sm">
        <CardHeader className="pb-0">
          <CardTitle className="text-lg font-medium">Mouvements de caisse</CardTitle>
        </CardHeader>
        <div className="flex items-center gap-2 p-3 border-b">
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-20" />
        </div>
        <CardContent className="p-0">
          <div className="p-4 space-y-4">
            <Skeleton className="h-6 w-32 mb-2" />
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div>
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-40 mt-1" />
                    </div>
                  </div>
                  <Skeleton className="h-5 w-20" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-border shadow-sm">
      <CardHeader className="pb-0">
        <CardTitle className="text-lg font-medium">Mouvements de caisse</CardTitle>
      </CardHeader>
      
      {/* Filter chips */}
      <div className="flex items-center gap-2 p-3 border-b">
        <Button 
          variant={activeFilter === 'all' ? "default" : "outline"} 
          size="sm"
          onClick={() => setActiveFilter('all')}
          className="h-8"
        >
          Tout
        </Button>
        <Button 
          variant={activeFilter === 'income' ? "default" : "outline"} 
          size="sm"
          onClick={() => setActiveFilter('income')}
          className="h-8"
        >
          💰 Entrées
        </Button>
        <Button 
          variant={activeFilter === 'outcome' ? "default" : "outline"} 
          size="sm"
          onClick={() => setActiveFilter('outcome')}
          className="h-8"
        >
          💸 Sorties
        </Button>
        <div className="ml-auto">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onRefresh} 
            className="h-8"
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <Loader2 className="h-3.5 w-3.5 mr-1 animate-spin" />
            ) : (
              <RefreshCwIcon className="h-3.5 w-3.5 mr-1" />
            )}
            Actualiser
          </Button>
        </div>
      </div>
      
      <CardContent className="p-0">
        <ScrollArea className="h-[500px]">
          {groupedTransactions.length === 0 ? (
            <div className="py-6 text-center text-muted-foreground text-sm">
              Aucune transaction trouvée
            </div>
          ) : (
            <div className="space-y-4 py-4">
              {groupedTransactions.map(([date, hourGroups]) => (
                <div key={date} className="px-4">
                  <h3 className="text-sm font-medium text-muted-foreground mb-2 sticky top-0 bg-background py-1">
                    {formatTransactionDate(date)}
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(hourGroups)
                      .sort(([hourA], [hourB]) => parseInt(hourB) - parseInt(hourA))
                      .map(([hour, txs]) => {
                        const groupId = `${date}_${hour}`;
                        const isGroupExpanded = enableHourlyGrouping ? expandedGroups[groupId] : true;
                        const summary = calculateGroupSummary(txs);
                        
                        return (
                          <div key={groupId} className="border rounded-md overflow-hidden bg-card">
                            {/* Hour group header - only show if hourly grouping is enabled */}
                            {enableHourlyGrouping && (
                              <div 
                                onClick={() => toggleGroupExpanded(groupId)}
                                className="flex justify-between items-center px-3 py-2 bg-muted/40 cursor-pointer hover:bg-muted/60 transition-colors"
                              >
                                <div className="flex items-center gap-2">
                                  <ClockIcon className="h-4 w-4 text-muted-foreground" />
                                  <span className="text-sm font-medium">
                                    {formatHourRange(parseInt(hour))}
                                  </span>
                                  <Badge variant="outline" className="ml-2">
                                    {summary.count} transaction{summary.count > 1 ? 's' : ''}
                                  </Badge>
                                </div>
                                <div className="flex items-center gap-3">
                                  <div className="text-right">
                                    <span className="text-sm font-medium">
                                      {formatCurrency(summary.total)}
                                    </span>
                                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                      <span className="text-green-600">+{formatCurrency(summary.inflow)}</span>
                                      <span className="text-red-600">-{formatCurrency(summary.outflow)}</span>
                                    </div>
                                  </div>
                                  <Button variant="ghost" size="icon" className="h-6 w-6">
                                    {isGroupExpanded ? (
                                      <ChevronUpIcon className="h-4 w-4" />
                                    ) : (
                                      <ChevronDownIcon className="h-4 w-4" />
                                    )}
                                  </Button>
                                </div>
                              </div>
                            )}
                            
                            {/* Transactions in this group */}
                            {isGroupExpanded && (
                              <div className="space-y-0.5 p-2">
                                {txs.map((transaction) => (
                                  <div 
                                    key={transaction.id} 
                                    className={cn(
                                      "bg-card rounded-md border shadow-sm hover:bg-accent/5 transition-colors",
                                      expandedTransactions[transaction.id] ? "p-3" : "p-2"
                                    )}
                                  >
                                    {/* Transaction header - always visible */}
                                    <div className="flex justify-between items-center cursor-pointer" onClick={() => toggleExpanded(transaction.id)}>
                                      <div className="flex items-center gap-2">
                                        <div className={cn(
                                          "p-1.5 rounded-full",
                                          transaction.amount >= 0 ? "bg-green-100/80" : "bg-red-100/80"
                                        )}>
                                          {getTransactionIcon(transaction.type, transaction.metadata)}
                                        </div>
                                        <div>
                                          <div className="flex items-center gap-1.5 flex-wrap">
                                            <Badge 
                                              variant={transaction.amount >= 0 ? "default" : "destructive"} 
                                              className="text-[10px] px-1.5 py-0"
                                            >
                                              {getTransactionTypeLabel(transaction.type, transaction.metadata)}
                                            </Badge>
                                            {getTransactionBadges(transaction)}
                                            {transaction.category && (
                                              <Badge variant="outline" className="text-[10px] px-1.5 py-0">
                                                {transaction.category}
                                              </Badge>
                                            )}
                                            {(transaction.metadata?.transactionCategory === 'order_payment' && transaction.metadata.orderSummary) && (
                                              <Badge variant="outline" className="text-[10px] px-1.5 py-0 bg-blue-50 text-blue-700 border-blue-200">
                                                📋 Détails
                                              </Badge>
                                            )}
                                            {(transaction.metadata?.transactionCategory === 'delivery_collection') && (
                                              <Badge variant="outline" className="text-[10px] px-1.5 py-0 bg-purple-50 text-purple-700 border-purple-200">
                                                🚚 Collecte
                                              </Badge>
                                            )}

                                            <span className="text-[10px] text-muted-foreground">
                                              {formatTransactionTime(transaction.time)}
                                            </span>
                                          </div>
                                          <p className="text-xs font-medium mt-0.5">{transaction.description}</p>
                                        </div>
                                      </div>

                                      <div className="flex items-center gap-2">
                                        <span className={`text-sm font-medium ${transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                          {transaction.amount >= 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                                        </span>
                                        <Button 
                                          variant="ghost" 
                                          size="icon" 
                                          className="h-6 w-6"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            toggleExpanded(transaction.id);
                                          }}
                                        >
                                          {expandedTransactions[transaction.id] ? (
                                            <ChevronUpIcon className="h-3.5 w-3.5" />
                                          ) : (
                                            <ChevronDownIcon className="h-3.5 w-3.5" />
                                          )}
                                        </Button>
                                      </div>
                                    </div>

                                    {/* 🚀 ENHANCED: Expanded content with order details */}
                                    {expandedTransactions[transaction.id] && (
                                      <div className="mt-2 pt-2 border-t text-xs space-y-1">
                                        <div className="grid grid-cols-2 gap-2">
                                          <div>
                                            <span className="text-muted-foreground">Effectué par:</span>
                                            <span className="ml-1">{transaction.performedBy}</span>
                                          </div>
                                          <div>
                                            <span className="text-muted-foreground">Date:</span>
                                            <span className="ml-1">{formatTransactionDate(transaction.time)} {formatTransactionTime(transaction.time)}</span>
                                          </div>
                                        </div>
                                        {/* Minimalistic order details */}
                                        {transaction.metadata?.transactionCategory === 'order_payment' && transaction.metadata.orderSummary && (
                                          <div className="flex flex-col gap-1 mt-1">
                                            <div className="flex flex-wrap gap-2 items-center">
                                              <span className="text-muted-foreground">Commande:</span>
                                              <span className="font-mono">{transaction.metadata.orderSummary.id}</span>
                                              <span className="text-muted-foreground">{transaction.metadata.orderSummary.orderType}</span>
                                              <span className="text-muted-foreground">{transaction.metadata.orderSummary.customer}</span>
                                              {transaction.metadata.orderSummary.tableId && (
                                                <span className="text-muted-foreground">Table {transaction.metadata.orderSummary.tableId}</span>
                                              )}
                                              {transaction.metadata.orderSummary.deliveryPerson && (
                                                <span className="text-muted-foreground">Livreur: {transaction.metadata.orderSummary.deliveryPerson}</span>
                                              )}
                                            </div>
                                            <div className="flex flex-wrap gap-2 items-center">
                                              <span className="text-muted-foreground">Articles:</span>
                                              <span className="font-mono truncate max-w-xs">{transaction.metadata.orderSummary.items}</span>
                                            </div>
                                            <div className="flex flex-wrap gap-2 items-center">
                                              <span className="text-muted-foreground">Total:</span>
                                              <span className="font-semibold">{formatCurrency(transaction.metadata.orderSummary.total)}</span>
                                              {transaction.metadata.paymentMethod && (
                                                <span className="text-muted-foreground">{transaction.metadata.paymentMethod}</span>
                                              )}
                                              {transaction.metadata.change && transaction.metadata.change > 0 && (
                                                <span className="text-muted-foreground">Monnaie rendue:</span>
                                              )}
                                              {transaction.metadata.change && transaction.metadata.change > 0 && (
                                                <span className="font-mono">{formatCurrency(transaction.metadata.change)}</span>
                                              )}
                                            </div>
                                          </div>
                                        )}
                                        {/* Minimalistic delivery collection details */}
                                        {transaction.metadata?.transactionCategory === 'delivery_collection' && (
                                          <div className="flex flex-col gap-1 mt-1">
                                            <div className="flex flex-wrap gap-2 items-center">
                                              <span className="text-muted-foreground">Livreur:</span>
                                              <span>{transaction.metadata.staffName}</span>
                                              <span className="text-muted-foreground">{transaction.metadata.orderCount} livraison{(transaction.metadata.orderCount || 0) > 1 ? 's' : ''}</span>
                                            </div>
                                            <div className="flex flex-wrap gap-2 items-center">
                                              <span className="text-muted-foreground">Attendu:</span>
                                              <span className="font-mono">{formatCurrency(transaction.metadata.expectedAmount || 0)}</span>
                                              <span className="text-muted-foreground">Reçu:</span>
                                              <span className="font-mono">{formatCurrency(transaction.metadata.actualAmount || 0)}</span>
                                              {transaction.metadata.discrepancy !== 0 && (
                                                <>
                                                  <span className="text-muted-foreground">Écart:</span>
                                                  <span className={`font-mono ${(transaction.metadata.discrepancy || 0) > 0 ? 'text-green-600' : 'text-red-600'}`}>{(transaction.metadata.discrepancy || 0) > 0 ? '+' : ''}{formatCurrency(transaction.metadata.discrepancy || 0)}</span>
                                                  {transaction.metadata.discrepancyReason && (
                                                    <span className="text-muted-foreground italic">({transaction.metadata.discrepancyReason})</span>
                                                  )}
                                                </>
                                              )}
                                            </div>
                                            {transaction.metadata.orderIds && transaction.metadata.orderIds.length > 0 && (
                                              <div className="flex flex-wrap gap-2 items-center">
                                                <span className="text-muted-foreground">Commandes:</span>
                                                <span className="font-mono truncate max-w-xs">{transaction.metadata.orderIds.join(', ')}</span>
                                              </div>
                                            )}
                                          </div>
                                        )}

                                        {transaction.notes && (
                                          <div className="text-muted-foreground mt-1">Notes: {transaction.notes}</div>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        );
                      })}
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
