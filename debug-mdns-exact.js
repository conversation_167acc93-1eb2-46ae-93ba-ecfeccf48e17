const bonjour = require('bonjour')();
const os = require('os');

console.log('🔍 Testing EXACT app configuration...\n');

// Test exact same config as the app
const SERVICE_TYPE = '_http._tcp.';
const SERVICE_DOMAIN = 'local.';
const hostname = os.hostname();
const localDeviceId = '832d5fc6-ff8b-4589-bd58-f7699c33badd'; // From your logs
const serverPort = 49270; // From your logs

// Exact configuration from your app
const appServiceConfig = {
  domain: SERVICE_DOMAIN,
  name: `PouchDB-${hostname}-${localDeviceId.substring(0, 8)}`,
  type: SERVICE_TYPE,
  port: serverPort,
  txt: {
    id: localDeviceId,
    version: '1.0',
    platform: 'desktop'
  }
};

console.log('🧪 Publishing with EXACT app config:');
console.log(JSON.stringify(appServiceConfig, null, 2));

// Test 1: Exact app config
console.log('\n🎯 TEST 1: Exact app configuration');
const appService = bonjour.publish(appServiceConfig);

appService.on('up', () => {
  console.log('✅ App config service published!');
  console.log(`   Name: ${appService.name}`);
  console.log(`   Type: ${appService.type}`);
  console.log(`   Port: ${appService.port}`);
  console.log(`   Host: ${appService.host}`);
});

appService.on('error', (err) => {
  console.error('❌ App config service error:', err);
});

// Test 2: Without domain property
console.log('\n🎯 TEST 2: Same config WITHOUT domain property');
const noDomainConfig = { ...appServiceConfig };
delete noDomainConfig.domain;

const noDomainService = bonjour.publish(noDomainConfig);

noDomainService.on('up', () => {
  console.log('✅ No-domain service published!');
  console.log(`   Name: ${noDomainService.name}`);
  console.log(`   Type: ${noDomainService.type}`);
  console.log(`   Port: ${noDomainService.port}`);
  console.log(`   Host: ${noDomainService.host}`);
});

noDomainService.on('error', (err) => {
  console.error('❌ No-domain service error:', err);
});

// Test 3: With simplified type
console.log('\n🎯 TEST 3: With simplified type "http"');
const simpleTypeConfig = {
  ...appServiceConfig,
  type: 'http'
};
delete simpleTypeConfig.domain; // Also remove domain

const simpleTypeService = bonjour.publish(simpleTypeConfig);

simpleTypeService.on('up', () => {
  console.log('✅ Simple type service published!');
  console.log(`   Name: ${simpleTypeService.name}`);
  console.log(`   Type: ${simpleTypeService.type}`);
  console.log(`   Port: ${simpleTypeService.port}`);
  console.log(`   Host: ${simpleTypeService.host}`);
});

simpleTypeService.on('error', (err) => {
  console.error('❌ Simple type service error:', err);
});

// Browse for all services
console.log('\n👀 Browsing for ALL HTTP services...');
const allBrowser = bonjour.find({ type: 'http' });
const fullBrowser = bonjour.find({ type: '_http._tcp.' });

allBrowser.on('up', (service) => {
  console.log(`📢 Found HTTP service: ${service.name} at ${service.host}:${service.port}`);
  if (service.name.includes('PouchDB')) {
    console.log('🎉 Found our PouchDB service via HTTP browser!');
  }
});

fullBrowser.on('up', (service) => {
  console.log(`📢 Found _http._tcp. service: ${service.name} at ${service.host}:${service.port}`);
  if (service.name.includes('PouchDB')) {
    console.log('🎉 Found our PouchDB service via _http._tcp. browser!');
  }
});

// Keep running for 30 seconds
console.log('\n⏰ Running for 30 seconds to test discovery...');
setTimeout(() => {
  console.log('\n🔚 Test complete. Results:');
  console.log('If you saw "🎉 Found our PouchDB service", then that config works!');
  console.log('Cleaning up...');
  
  appService.stop();
  noDomainService.stop();
  simpleTypeService.stop();
  allBrowser.stop();
  fullBrowser.stop();
  bonjour.destroy();
  process.exit(0);
}, 30000);

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping...');
  appService.stop();
  noDomainService.stop();
  simpleTypeService.stop();
  allBrowser.stop();
  fullBrowser.stop();
  bonjour.destroy();
  process.exit(0);
}); 