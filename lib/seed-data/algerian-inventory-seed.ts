// Enhanced Algerian Suppliers
export const enhancedAlgerianSuppliers = [
  { 
    name: "<PERSON>ula<PERSON><PERSON>", 
    phoneNumber: "0550123456", 
    category: "Ingredients", 
    balance: 0, 
    isActive: true, 
    notes: "Fournisseur principal pour pain et semoule." 
  },
  { 
    name: "Grossiste Boissons El Bahdja", 
    phoneNumber: "0771987654", 
    category: "Beverages", 
    balance: 15000, 
    isActive: true, 
    notes: "Livraison chaque Lundi." 
  },
  { 
    name: "<PERSON> J<PERSON> (Légumes & Fruits)", 
    phoneNumber: "0662112233", 
    category: "Ingredients", 
    balance: 0, 
    isActive: true,
    notes: "Produits frais locaux."
  },
  { 
    name: "Boucherie Atlas", 
    phoneNumber: "0555667788", 
    category: "Ingredients", 
    balance: 35000, 
    isActive: true, 
    notes: "Viande Halal. Paiement fin de semaine." 
  },
  { 
    name: "Hygiène Plus Distribution", 
    phoneNumber: "0790554433", 
    category: "Cleaning", 
    balance: 0, 
    isActive: true,
    notes: "Produits d'entretien et papier."
  },
  { 
    name: "Fromagerie Djurdjura", 
    phoneNumber: "0550998877", 
    category: "Ingredients", 
    balance: 12000, 
    isActive: true, 
    notes: "Fromages locaux et importés." 
  },
  { 
    name: "Épicerie Moderne", 
    phoneNumber: "0661223344", 
    category: "Ingredients", 
    balance: 8000, 
    isActive: true, 
    notes: "Épices, huiles et conserves." 
  }
];

// Enhanced Algerian Stock Items
export const enhancedAlgerianStockItems = [
  // Basic Ingredients for Dough
  { name: "Farine à Pizza T00", category: "Ingredients", unit: "kg", quantity: 50, minLevel: 15, costPerUnit: 120 },
  { name: "Farine T55", category: "Ingredients", unit: "kg", quantity: 25, minLevel: 10, costPerUnit: 90 },
  { name: "Levure Fraîche", category: "Ingredients", unit: "g", quantity: 1000, minLevel: 300, costPerUnit: 0.5 },
  { name: "Levure Sèche", category: "Ingredients", unit: "g", quantity: 500, minLevel: 100, costPerUnit: 1.2 },
  { name: "Sel Fin", category: "Ingredients", unit: "kg", quantity: 5, minLevel: 1, costPerUnit: 60 },
  { name: "Sucre", category: "Ingredients", unit: "kg", quantity: 10, minLevel: 2, costPerUnit: 110 },
  { name: "Huile d'Olive Extra Vierge", category: "Ingredients", unit: "L", quantity: 10, minLevel: 3, costPerUnit: 900 },
  
  // Sauce Ingredients
  { name: "Tomates Pelées en Conserve", category: "Ingredients", unit: "kg", quantity: 30, minLevel: 10, costPerUnit: 180 },
  { name: "Concentré de Tomate", category: "Ingredients", unit: "kg", quantity: 10, minLevel: 3, costPerUnit: 250 },
  { name: "Ail Frais", category: "Ingredients", unit: "kg", quantity: 2, minLevel: 0.5, costPerUnit: 400 },
  { name: "Origan Séché", category: "Ingredients", unit: "kg", quantity: 1, minLevel: 0.2, costPerUnit: 800 },
  { name: "Basilic Séché", category: "Ingredients", unit: "kg", quantity: 1, minLevel: 0.2, costPerUnit: 900 },
  { name: "Thym Séché", category: "Ingredients", unit: "kg", quantity: 0.5, minLevel: 0.1, costPerUnit: 700 },
  
  // Cheese and Dairy
  { name: "Mozzarella Râpée", category: "Ingredients", unit: "kg", quantity: 20, minLevel: 5, costPerUnit: 1200 },
  { name: "Fromage Bleu", category: "Ingredients", unit: "kg", quantity: 5, minLevel: 1, costPerUnit: 1500 },
  { name: "Fromage de Chèvre", category: "Ingredients", unit: "kg", quantity: 5, minLevel: 1, costPerUnit: 1400 },
  { name: "Emmental Râpé", category: "Ingredients", unit: "kg", quantity: 10, minLevel: 2, costPerUnit: 1300 },
  
  // Meats
  { name: "Poulet Mariné", category: "Ingredients", unit: "kg", quantity: 15, minLevel: 3, costPerUnit: 800 },
  { name: "Viande Hachée Boeuf", category: "Ingredients", unit: "kg", quantity: 15, minLevel: 3, costPerUnit: 1200 },
  { name: "Merguez", category: "Ingredients", unit: "kg", quantity: 10, minLevel: 2, costPerUnit: 900 },
  { name: "Thon en Conserve", category: "Ingredients", unit: "kg", quantity: 8, minLevel: 2, costPerUnit: 1100 },
  
  // Vegetables
  { name: "Champignons Frais", category: "Ingredients", unit: "kg", quantity: 8, minLevel: 2, costPerUnit: 600 },
  { name: "Poivrons", category: "Ingredients", unit: "kg", quantity: 10, minLevel: 2, costPerUnit: 350 },
  { name: "Oignons", category: "Ingredients", unit: "kg", quantity: 15, minLevel: 3, costPerUnit: 120 },
  { name: "Olives Noires", category: "Ingredients", unit: "kg", quantity: 5, minLevel: 1, costPerUnit: 450 },
  { name: "Olives Vertes", category: "Ingredients", unit: "kg", quantity: 5, minLevel: 1, costPerUnit: 400 },
  { name: "Tomates Fraîches", category: "Ingredients", unit: "kg", quantity: 20, minLevel: 5, costPerUnit: 200 },
  { name: "Salade Verte", category: "Ingredients", unit: "kg", quantity: 10, minLevel: 2, costPerUnit: 250 },
  
  // Bread and Sandwich Ingredients
  { name: "Pain à Sandwich", category: "Ingredients", unit: "pcs", quantity: 50, minLevel: 10, costPerUnit: 40 },
  { name: "Mayonnaise", category: "Ingredients", unit: "kg", quantity: 5, minLevel: 1, costPerUnit: 500 },
  { name: "Ketchup", category: "Ingredients", unit: "kg", quantity: 5, minLevel: 1, costPerUnit: 400 },
  { name: "Sauce Algérienne", category: "Ingredients", unit: "kg", quantity: 3, minLevel: 0.5, costPerUnit: 600 },
  { name: "Harissa", category: "Ingredients", unit: "kg", quantity: 2, minLevel: 0.5, costPerUnit: 450 },
  
  // Beverages
  { name: "Coca-Cola (Bouteille)", category: "Beverages", unit: "pcs", quantity: 100, minLevel: 20, costPerUnit: 60 },
  { name: "Fanta (Bouteille)", category: "Beverages", unit: "pcs", quantity: 80, minLevel: 15, costPerUnit: 60 },
  { name: "Sprite (Bouteille)", category: "Beverages", unit: "pcs", quantity: 80, minLevel: 15, costPerUnit: 60 },
  { name: "Eau Minérale", category: "Beverages", unit: "pcs", quantity: 150, minLevel: 30, costPerUnit: 30 },
  { name: "Thé à la Menthe (Sachets)", category: "Beverages", unit: "pcs", quantity: 200, minLevel: 50, costPerUnit: 2 },
  { name: "Café en Grains", category: "Beverages", unit: "kg", quantity: 5, minLevel: 1, costPerUnit: 2500 }
];
