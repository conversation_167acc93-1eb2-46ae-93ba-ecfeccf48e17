// knowledge: universal v4 seed for suppliers, inventory, menu, sub-recipes, and menu item recipes
import { addSupplier } from '../db/v4/operations/supplier-ops';
import { addStockItem } from '../db/v4/operations/inventory-ops';
import { addCategory, updateMenu, getMenu } from '../db/v4/operations/menu-ops';
import { createSubRecipe } from '../db/v4/operations/sub-recipe-ops';
import { createMenuItemRecipe } from '../db/v4/operations/menu-item-recipe-ops';
import { createSupplement, updateCategorySupplementConfig } from '../db/v4/operations/supplement-ops';
import { updateCategoryPackaging } from '../db/v4/operations/packaging-ops';
import { ensureDefaultSettings } from '../db/v4/operations/settings-ops';
import { RestaurantSettings } from '../db/v4/schemas/restaurant-settings-schema';
import { Supplement } from '../db/v4/schemas/menu-schema';
import { getRandomPresetColor, PRESET_COLORS } from '../constants';
import { v4 as uuidv4 } from 'uuid';

// Minimal, real, and consistent data for a pizza restaurant
export async function runUniversalSeedV4() {
  // knowledge: wrap all in try/catch for safety
  try {
    console.log('🚀 Starting Universal Seed V4...');

    // --- RESTAURANT SETTINGS ---
    console.log('⏳ Ensuring default restaurant settings...');
    await ensureDefaultSettings();
    console.log('✅ Default restaurant settings ensured.');

    // --- SUPPLIERS ---
    console.log('⏳ Seeding suppliers...');
    const now = new Date().toISOString();
    const suppliers = [
      {
        name: 'Boulangerie Omar',
        phoneNumber: '0550123456',
        category: 'Ingredients',
        notes: 'Pain et semoule',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Fromagerie Djurdjura',
        phoneNumber: '0550998877',
        category: 'Ingredients',
        notes: 'Fromages locaux',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Grossiste Boissons El Bahdja',
        phoneNumber: '0771987654',
        category: 'Beverages',
        notes: 'Boissons',
        balance: 0,
        isActive: true,
      },
    ];
    
    const supplierIds: Record<string, string> = {};
    for (const s of suppliers) {
      const created = await addSupplier(s);
      supplierIds[s.name] = created.id;
    }
    console.log('✅ Suppliers seeded.');

    // --- INVENTORY ITEMS ---
    console.log('⏳ Seeding inventory items...');
    // Only ingredients for pizza and drinks
    const stockItems = [
      // Dough
      {
        name: 'Farine à Pizza T00',
        category: 'Ingredients',
        unit: 'kg' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 20,
        minLevel: 5,
        costPerUnit: 120,
        purchaseUnits: [
          {
            id: `pu_${uuidv4()}`,
            name: 'Sac 25kg',
            conversionToBase: 25,
            isDefault: true,
            createdAt: now,
            updatedAt: now
          },
          {
            id: `pu_${uuidv4()}`,
            name: 'Sac 50kg',
            conversionToBase: 50,
            isDefault: false,
            createdAt: now,
            updatedAt: now
          }
        ]
      },
      {
        name: 'Levure Sèche',
        category: 'Ingredients',
        unit: 'g' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 500,
        minLevel: 100,
        costPerUnit: 1.2,
        purchaseUnits: [
          {
            id: `pu_${uuidv4()}`,
            name: 'Paquet 500g',
            conversionToBase: 500,
            isDefault: true,
            createdAt: now,
            updatedAt: now
          },
          {
            id: `pu_${uuidv4()}`,
            name: 'Boîte 1kg',
            conversionToBase: 1000,
            isDefault: false,
            createdAt: now,
            updatedAt: now
          }
        ]
      },
      {
        name: 'Sel Fin',
        category: 'Ingredients',
        unit: 'kg' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 5,
        minLevel: 1,
        costPerUnit: 60,
      },
      {
        name: "Huile d'Olive Extra Vierge",
        category: 'Ingredients',
        unit: 'L' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 5,
        minLevel: 1,
        costPerUnit: 900,
      },
      // Sauce
      {
        name: 'Tomates Pelées',
        category: 'Ingredients',
        unit: 'kg' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 10,
        minLevel: 2,
        costPerUnit: 180,
      },
      {
        name: 'Origan Séché',
        category: 'Ingredients',
        unit: 'kg' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 1,
        minLevel: 0.2,
        costPerUnit: 800,
      },
      // Cheese
      {
        name: 'Mozzarella Râpée',
        category: 'Ingredients',
        unit: 'kg' as const,
        supplierId: supplierIds['Fromagerie Djurdjura'],
        quantity: 10,
        minLevel: 2,
        costPerUnit: 1200,
        purchaseUnits: [
          {
            id: `pu_${uuidv4()}`,
            name: 'Sac 2kg',
            conversionToBase: 2,
            isDefault: true,
            createdAt: now,
            updatedAt: now
          },
          {
            id: `pu_${uuidv4()}`,
            name: 'Carton 10kg',
            conversionToBase: 10,
            isDefault: false,
            createdAt: now,
            updatedAt: now
          }
        ]
      },
      // Toppings
      {
        name: 'Olives Noires',
        category: 'Ingredients',
        unit: 'kg' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 2,
        minLevel: 0.5,
        costPerUnit: 450,
        purchaseUnits: [
          {
            id: `pu_${uuidv4()}`,
            name: 'Boîte 5kg',
            conversionToBase: 5,
            isDefault: true,
            createdAt: now,
            updatedAt: now
          }
        ]
      },
      // Drinks
      {
        name: 'Coca-Cola 33cl',
        category: 'Beverages',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 30,
        minLevel: 10,
        costPerUnit: 60,
        purchaseUnits: [
          {
            id: `pu_${uuidv4()}`,
            name: 'Pack 6',
            conversionToBase: 6,
            isDefault: false,
            createdAt: now,
            updatedAt: now
          },
          {
            id: `pu_${uuidv4()}`,
            name: 'Carton 24',
            conversionToBase: 24,
            isDefault: true,
            createdAt: now,
            updatedAt: now
          }
        ]
      },
      {
        name: 'Fanta Orange 33cl',
        category: 'Beverages',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 30,
        minLevel: 10,
        costPerUnit: 60,
        purchaseUnits: [
          {
            id: `pu_${uuidv4()}`,
            name: 'Pack 6',
            conversionToBase: 6,
            isDefault: false,
            createdAt: now,
            updatedAt: now
          },
          {
            id: `pu_${uuidv4()}`,
            name: 'Carton 24',
            conversionToBase: 24,
            isDefault: true,
            createdAt: now,
            updatedAt: now
          }
        ]
      },
      {
        name: 'Eau Minérale 0.5L',
        category: 'Beverages',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 50,
        minLevel: 20,
        costPerUnit: 25,
        purchaseUnits: [
          {
            id: `pu_${uuidv4()}`,
            name: 'Pack 12',
            conversionToBase: 12,
            isDefault: true,
            createdAt: now,
            updatedAt: now
          },
          {
            id: `pu_${uuidv4()}`,
            name: 'Pack 6',
            conversionToBase: 6,
            isDefault: false,
            createdAt: now,
            updatedAt: now
          }
        ]
      },
      // Additional ingredients for supplements
      {
        name: 'Champignons Frais',
        category: 'Ingredients',
        unit: 'kg' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 3,
        minLevel: 0.5,
        costPerUnit: 350,
      },
      // Packaging items
      {
        name: 'Boîte Pizza 30cm',
        category: 'Emballage',
        unit: 'pcs' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 200,
        minLevel: 50,
        costPerUnit: 15,
      },
      {
        name: 'Boîte Pizza 40cm',
        category: 'Emballage',
        unit: 'pcs' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 150,
        minLevel: 30,
        costPerUnit: 20,
      },
      {
        name: 'Serviettes en Papier',
        category: 'Emballage',
        unit: 'pcs' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 1000,
        minLevel: 200,
        costPerUnit: 0.5,
      },
      {
        name: 'Assiettes Jetables',
        category: 'Emballage',
        unit: 'pcs' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 300,
        minLevel: 50,
        costPerUnit: 2,
      },
      {
        name: 'Gobelets 25cl',
        category: 'Emballage',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 500,
        minLevel: 100,
        costPerUnit: 1.5,
      },
      {
        name: 'Sacs Papier Kraft',
        category: 'Emballage',
        unit: 'pcs' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 400,
        minLevel: 80,
        costPerUnit: 3,
      },
      {
        name: 'Couverts Plastique Set',
        category: 'Emballage',
        unit: 'pcs' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 200,
        minLevel: 40,
        costPerUnit: 4,
      },
    ];
    
    const stockItemIds: Record<string, string> = {};
    for (const s of stockItems) {
      const created = await addStockItem(s);
      stockItemIds[s.name] = created.id;
    }
    console.log('✅ Inventory items seeded.');

    // --- SUB-RECIPES ---
    console.log('⏳ Seeding sub-recipes...');
    const subRecipes = [
      {
        name: 'Pâte à Pizza Préparée',
        ingredients: [
          { stockItemId: stockItemIds['Farine à Pizza T00'], quantity: 0.2 },
          { stockItemId: stockItemIds['Levure Sèche'], quantity: 0.002 },
          { stockItemId: stockItemIds['Sel Fin'], quantity: 0.005 },
          { stockItemId: stockItemIds["Huile d'Olive Extra Vierge"], quantity: 0.01 },
        ],
        costPerUnit: 0,
        unit: 'kg' as const,
        yield: { quantity: 1, unit: 'kg' as const },
      },
      {
        name: 'Sauce Tomate Maison',
        ingredients: [
          { stockItemId: stockItemIds['Tomates Pelées'], quantity: 0.5 },
          { stockItemId: stockItemIds['Origan Séché'], quantity: 0.001 },
          { stockItemId: stockItemIds["Huile d'Olive Extra Vierge"], quantity: 0.005 },
        ],
        costPerUnit: 0,
        unit: 'kg' as const,
        yield: { quantity: 1, unit: 'kg' as const },
      },
    ];
    
    const subRecipeIds: Record<string, string> = {};
    for (const sr of subRecipes) {
      const created = await createSubRecipe(sr);
      subRecipeIds[sr.name] = created._id;
    }
    console.log('✅ Sub-recipes seeded.');

    // --- MENU CATEGORIES & ITEMS ---
    console.log('⏳ Seeding menu categories and items...');
    
    // 🍕 PIZZA ITEMS with category-specific colors (cycling through palette)
    const pizzaItems = [
      {
        id: `item_${uuidv4()}`,
        name: 'Margherite',
        description: 'Sauce Tomate, Mozzarella, Basilic',
        prices: { Normale: 450, Mega: 600, Géante: 750 },
        color: PRESET_COLORS[0], // Pizza color 1
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Royal',
        description: 'Sauce Tomate, Cheddar, Mozzarella, Merguez, Viande Hachée, Poulet',
        prices: { Normale: 600, Mega: 650, Géante: 750 },
        color: PRESET_COLORS[1], // Pizza color 2
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Casa Nostra',
        description: 'Mozzarella, Viande Hachée, Olives',
        prices: { Normale: 750, Mega: 850, Géante: 950 },
        color: PRESET_COLORS[2], // Pizza color 3
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Végétarienne',
        description: 'Sauce Tomate, Cheddar, Mozzarella, Poivrons, Champignons, Tomate, Olives',
        prices: { Normale: 600, Mega: 750, Géante: 850 },
        color: PRESET_COLORS[3], // Pizza color 4
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Thon',
        description: 'Sauce Tomate, Cheddar, Mozzarella, Thon, Olives',
        prices: { Normale: 700, Mega: 800, Géante: 900 },
        color: PRESET_COLORS[4], // Pizza color 5
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Poppy',
        description: 'Mozzarella, Viande Hachée, Pepperoni, Poulet',
        prices: { Normale: 550, Mega: 650, Géante: 750 },
        color: PRESET_COLORS[5], // Pizza color 6
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Pepperoni',
        description: 'Sauce Tomate, Cheddar, Mozzarella, Pepperoni, Olives',
        prices: { Normale: 600, Mega: 650, Géante: 750 },
        color: PRESET_COLORS[6], // Pizza color 7
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Enjoy Luna',
        description: 'Sauce de Cheddar, Mozzarella, Poulet Fumé, Champignons, Viande Hachée',
        prices: { Normale: 1300, Mega: 1300, Géante: 1300 },
        color: PRESET_COLORS[7], // Pizza color 8
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Chicken',
        description: 'Mozzarella, Blanc de Poulet, Olives',
        prices: { Normale: 700, Mega: 850, Géante: 950 },
        color: PRESET_COLORS[8], // Pizza color 9
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Foggy',
        description: 'Sauce Tomate, Cheddar, Mozzarella, Viande Hachée, Fromage Fumé, Champignons, Olives',
        prices: { Normale: 650, Mega: 850, Géante: 1400 },
        color: PRESET_COLORS[9], // Pizza color 10
      },
      {
        id: `item_${uuidv4()}`,
        name: 'La Marinade',
        description: 'Sauce Tomate, Gruyère, Mozzarella, Poulet Mariné',
        prices: { Normale: 750, Mega: 850, Géante: 1200 },
        color: PRESET_COLORS[10], // Pizza color 11
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Quattro Formagi',
        description: 'Sauce Tomate, Cheddar, Mozzarella, Gouda, Gruyère, Camembert',
        prices: { Normale: 650, Mega: 950, Géante: 1400 },
        color: PRESET_COLORS[11], // Pizza color 12
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Forestière',
        description: 'Sauce Tomate, Cheddar, Mozzarella, Poulet, Champignons, Olives',
        prices: { Normale: 900, Mega: 950, Géante: 1500 },
        color: PRESET_COLORS[12], // Pizza color 13
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Four Seasons',
        description: 'Sauce Tomate, Cheddar, Mozzarella, Thon, Poulet Fumé, Merguez',
        prices: { Normale: 750, Mega: 950, Géante: 1350 },
        color: PRESET_COLORS[13], // Pizza color 14
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Maxi Enjoy',
        description: 'Sauce Tomate, Cheddar, Mozzarella, Poulet Fumé, Viande Hachée, Merguez, Poulet Fumé, Camembert, Gruyère',
        prices: { Normale: 1650, Mega: 2100, Géante: 2100 },
        color: PRESET_COLORS[14], // Pizza color 15
      },
      {
        id: `item_${uuidv4()}`,
        name: 'La Lionne',
        description: 'Poulet Fumé, Oui, Viande Hachée, Cheddar, Mozzarella, Cheddar',
        prices: { Normale: 1500, Mega: 1500, Géante: 1500 },
        color: PRESET_COLORS[15], // Pizza color 16
      },
    ];

    // 🌮 TACOS ITEMS with category-specific colors (cycling through palette)
    const tacosItems = [
      {
        id: `item_${uuidv4()}`,
        name: 'Tacos Poulet',
        description: 'Poulet grillé, salade, tomates, sauce blanche',
        prices: { Standard: 600 },
        color: PRESET_COLORS[0], // Tacos color 1
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Tacos Mexicain',
        description: 'Viande mexicaine épicée, salade, tomates',
        prices: { Standard: 650 },
        color: PRESET_COLORS[1], // Tacos color 2
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Tacos Poulet',
        description: 'Poulet grillé, salade, tomates, sauce blanche',
        prices: { Standard: 700 },
        color: PRESET_COLORS[2], // Tacos color 3
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Tacos Viande',
        description: 'Viande hachée épicée, salade, tomates',
        prices: { Standard: 750 },
        color: PRESET_COLORS[3], // Tacos color 4
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Tacos Mixte',
        description: 'Mélange de viandes, salade, tomates',
        prices: { Standard: 750 },
        color: PRESET_COLORS[4], // Tacos color 5
      },
    ];

    // 🥪 SANDWICH ITEMS with category-specific colors (cycling through palette)
    const sandwichItems = [
      {
        id: `item_${uuidv4()}`,
        name: 'Crunchy',
        description: 'Sandwich croustillant avec garniture spéciale',
        prices: { Normal: 450 },
        color: PRESET_COLORS[0], // Sandwich color 1
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Pepper Joe',
        description: 'Sandwich épicé au poivre',
        prices: { Normal: 450 },
        color: PRESET_COLORS[1], // Sandwich color 2
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Mexicain',
        description: 'Sandwich mexicain épicé',
        prices: { Normal: 450 },
        color: PRESET_COLORS[2], // Sandwich color 3
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Le French',
        description: 'Sandwich à la française',
        prices: { Normal: 500 },
        color: PRESET_COLORS[3], // Sandwich color 4
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Standy',
        description: 'Sandwich standard',
        prices: { Normal: 400 },
        color: PRESET_COLORS[4], // Sandwich color 5
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Fat-Man',
        description: 'Sandwich généreux',
        prices: { Normal: 500 },
        color: PRESET_COLORS[5], // Sandwich color 6
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Little Boy',
        description: 'Petit sandwich',
        prices: { Normal: 350 },
        color: PRESET_COLORS[6], // Sandwich color 7
      },
    ];

    // 🍲 GRATINS ITEMS with category-specific colors (cycling through palette)
    const gratinsItems = [
      {
        id: `item_${uuidv4()}`,
        name: 'Gratin de Poulet',
        description: 'Gratin de poulet aux légumes',
        prices: { Standard: 600 },
        color: PRESET_COLORS[0], // Gratins color 1
      },
    ];

    // 🍽️ PLATS ITEMS with category-specific colors (cycling through palette)
    const platsItems = [
      {
        id: `item_${uuidv4()}`,
        name: 'El Presidente',
        description: 'Plat signature de la maison',
        prices: { Standard: 750 },
        color: PRESET_COLORS[0], // Plats color 1
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Crunchy Camembert',
        description: 'Camembert croustillant',
        prices: { Standard: 650 },
        color: PRESET_COLORS[1], // Plats color 2
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Nuggets de Viande',
        description: 'Nuggets de viande maison',
        prices: { Standard: 600 },
        color: PRESET_COLORS[2], // Plats color 3
      },
    ];

    // 🍔 BURGERS ITEMS with category-specific colors (cycling through palette)
    const burgersItems = [
      {
        id: `item_${uuidv4()}`,
        name: 'Le P\'tit',
        description: 'Petit burger classique',
        prices: { Standard: 400 },
        color: PRESET_COLORS[0], // Burgers color 1
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Le Big',
        description: 'Grand burger généreux',
        prices: { Standard: 650 },
        color: PRESET_COLORS[1], // Burgers color 2
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Le Max',
        description: 'Burger maximum',
        prices: { Standard: 500 },
        color: PRESET_COLORS[2], // Burgers color 3
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Le Chicken',
        description: 'Burger au poulet',
        prices: { Standard: 450 },
        color: PRESET_COLORS[3], // Burgers color 4
      },
    ];

    // 🍤 SAMPLES ITEMS with category-specific colors (cycling through palette)
    const samplesItems = [
      {
        id: `item_${uuidv4()}`,
        name: '3 Pcs Crunchy + Barquette Frites',
        description: '3 pièces croustillantes avec frites',
        prices: { Standard: 650 },
        color: PRESET_COLORS[0], // Samples color 1
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Nuggets Poulets',
        description: 'Nuggets de poulet',
        prices: { Standard: 650 },
        color: PRESET_COLORS[1], // Samples color 2
      },
    ];

    // 🥤 BOISSONS ITEMS with category-specific colors (cycling through palette)
    const drinkItems = [
      {
        id: `item_${uuidv4()}`,
        name: 'Boissons 50',
        description: 'Boisson 50cl',
        prices: { Standard: 150 },
        color: PRESET_COLORS[0], // Boissons color 1
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Boissons 1L',
        description: 'Boisson 1 litre',
        prices: { Standard: 250 },
        color: PRESET_COLORS[1], // Boissons color 2
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Eau 50',
        description: 'Eau 50cl',
        prices: { Standard: 100 },
        color: PRESET_COLORS[2], // Boissons color 3
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Eau 1L',
        description: 'Eau 1 litre',
        prices: { Standard: 150 },
        color: PRESET_COLORS[3], // Boissons color 4
      },
    ];

    // 🍰 DESSERT ITEMS with category-specific colors (cycling through palette)
    const dessertItems = [
      {
        id: `item_${uuidv4()}`,
        name: 'Tiramisu',
        description: 'Tiramisu maison',
        prices: { Standard: 300 },
        color: PRESET_COLORS[0], // Dessert color 1
      },
      {
        id: `item_${uuidv4()}`,
        name: 'Cheesecake',
        description: 'Cheesecake aux fruits',
        prices: { Standard: 350 },
        color: PRESET_COLORS[1], // Dessert color 2
      },
    ];

    const menuCategories = [
      {
        id: `cat_${uuidv4()}`,
        name: 'Pizzas',
        emoji: '🍕',
        color: PRESET_COLORS[0], // Category color
        sizes: ['Normale', 'Mega', 'Géante'],
        items: pizzaItems,
        isQuarterable: true,
        quarterPricingMethod: 'max' as const,
      },
      {
        id: `cat_${uuidv4()}`,
        name: 'Tacos',
        emoji: '🌮',
        color: PRESET_COLORS[2], // Category color
        sizes: ['Simple', 'Double', 'Triple'],
        items: tacosItems,
      },
      {
        id: `cat_${uuidv4()}`,
        name: 'Sandwichs',
        emoji: '🥪',
        color: PRESET_COLORS[4], // Category color
        sizes: ['Normal'],
        items: sandwichItems,
      },
      {
        id: `cat_${uuidv4()}`,
        name: 'Gratins',
        emoji: '🍲',
        color: PRESET_COLORS[6], // Category color
        sizes: ['Standard'],
        items: gratinsItems,
      },
      {
        id: `cat_${uuidv4()}`,
        name: 'Plats',
        emoji: '🍽️',
        color: PRESET_COLORS[8], // Category color
        sizes: ['Standard'],
        items: platsItems,
      },
      {
        id: `cat_${uuidv4()}`,
        name: 'Burgers',
        emoji: '🍔',
        color: PRESET_COLORS[10], // Category color
        sizes: ['Standard'],
        items: burgersItems,
      },
      {
        id: `cat_${uuidv4()}`,
        name: 'Samples',
        emoji: '🍤',
        color: PRESET_COLORS[12], // Category color
        sizes: ['Standard'],
        items: samplesItems,
      },
      {
        id: `cat_${uuidv4()}`,
        name: 'Boissons',
        emoji: '🥤',
        color: PRESET_COLORS[14], // Category color
        sizes: ['33cl', '1L', '25cl', '50cl', 'Théière'],
        items: drinkItems,
      },
      {
        id: `cat_${uuidv4()}`,
        name: 'Dessert',
        emoji: '🍰',
        color: PRESET_COLORS[15], // Category color
        sizes: ['Standard'],
        items: dessertItems,
      },
    ];

    
    const menuCategoryIds: Record<string, string> = {};
    for (const mc of menuCategories) {
      const created = await addCategory(mc);
      menuCategoryIds[mc.name] = created.categories.find(c => c.name === mc.name)?.id || '';
    }
    console.log('✅ Menu categories and items seeded.');

    // --- MENU ITEM RECIPES ---
    console.log('⏳ Seeding menu item recipes...');
    const menuItemRecipes = [
      // 🍕 PIZZA MARGHERITA RECIPES
      {
        menuItemId: pizzaItems[0].id, // Pizza Margherita
        menuItemName: pizzaItems[0].name,
        size: 'Normale',
        ingredients: [
          { subRecipeId: subRecipeIds['Pâte à Pizza Préparée'], quantity: 0.3 },
          { subRecipeId: subRecipeIds['Sauce Tomate Maison'], quantity: 0.1 },
          { stockItemId: stockItemIds['Mozzarella Râpée'], quantity: 0.15 },
        ],
      },
      {
        menuItemId: pizzaItems[0].id, // Pizza Margherita
        menuItemName: pizzaItems[0].name,
        size: 'Mega',
        ingredients: [
          { subRecipeId: subRecipeIds['Pâte à Pizza Préparée'], quantity: 0.5 },
          { subRecipeId: subRecipeIds['Sauce Tomate Maison'], quantity: 0.2 },
          { stockItemId: stockItemIds['Mozzarella Râpée'], quantity: 0.25 },
        ],
      },
      {
        menuItemId: pizzaItems[0].id, // Pizza Margherita
        menuItemName: pizzaItems[0].name,
        size: 'Géante',
        ingredients: [
          { subRecipeId: subRecipeIds['Pâte à Pizza Préparée'], quantity: 0.8 },
          { subRecipeId: subRecipeIds['Sauce Tomate Maison'], quantity: 0.3 },
          { stockItemId: stockItemIds['Mozzarella Râpée'], quantity: 0.4 },
        ],
      },
      // 🍕 PIZZA ROYAL RECIPES
      {
        menuItemId: pizzaItems[1].id, // Pizza Royal
        menuItemName: pizzaItems[1].name,
        size: 'Normale',
        ingredients: [
          { subRecipeId: subRecipeIds['Pâte à Pizza Préparée'], quantity: 0.3 },
          { subRecipeId: subRecipeIds['Sauce Tomate Maison'], quantity: 0.1 },
          { stockItemId: stockItemIds['Mozzarella Râpée'], quantity: 0.15 },
          { stockItemId: stockItemIds['Olives Noires'], quantity: 0.05 },
        ],
      },
      {
        menuItemId: pizzaItems[1].id, // Pizza Royal
        menuItemName: pizzaItems[1].name,
        size: 'Mega',
        ingredients: [
          { subRecipeId: subRecipeIds['Pâte à Pizza Préparée'], quantity: 0.5 },
          { subRecipeId: subRecipeIds['Sauce Tomate Maison'], quantity: 0.2 },
          { stockItemId: stockItemIds['Mozzarella Râpée'], quantity: 0.25 },
          { stockItemId: stockItemIds['Olives Noires'], quantity: 0.08 },
        ],
      },
      {
        menuItemId: pizzaItems[1].id, // Pizza Royal
        menuItemName: pizzaItems[1].name,
        size: 'Géante',
        ingredients: [
          { subRecipeId: subRecipeIds['Pâte à Pizza Préparée'], quantity: 0.8 },
          { subRecipeId: subRecipeIds['Sauce Tomate Maison'], quantity: 0.3 },
          { stockItemId: stockItemIds['Mozzarella Râpée'], quantity: 0.4 },
          { stockItemId: stockItemIds['Olives Noires'], quantity: 0.12 },
        ],
      },
      // 🌮 TACOS POULET RECIPES (basic example)
      {
        menuItemId: tacosItems[0].id, // Tacos Poulet
        menuItemName: tacosItems[0].name,
        size: 'Simple',
        ingredients: [
          { stockItemId: stockItemIds['Mozzarella Râpée'], quantity: 0.1 }, // Using available ingredients
        ],
      },
      // 🥪 SANDWICH POULET RECIPE (basic example)
      {
        menuItemId: sandwichItems[0].id, // Sandwich Poulet Grillé
        menuItemName: sandwichItems[0].name,
        size: 'Normal',
        ingredients: [
          { stockItemId: stockItemIds['Mozzarella Râpée'], quantity: 0.05 }, // Using available ingredients
        ],
      },
    ];

    for (const mir of menuItemRecipes) {
      await createMenuItemRecipe(mir);
    }
    console.log('✅ Menu item recipes seeded.');

    // --- PACKAGING CONFIGURATION ---
    console.log('⏳ Configuring packaging rules...');
    
    // Configure packaging for Pizza category
    const pizzaCategory = menuCategories.find(cat => cat.name === 'Pizzas');
    if (pizzaCategory) {
      const pizzaPackagingRules = {
        'Normale': {
          'dine-in': [
            { stockItemId: stockItemIds['Assiettes Jetables'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes en Papier'], quantity: 2 }
          ],
          'takeaway': [
            { stockItemId: stockItemIds['Boîte Pizza 30cm'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes en Papier'], quantity: 2 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Boîte Pizza 30cm'], quantity: 1 },
            { stockItemId: stockItemIds['Sacs Papier Kraft'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes en Papier'], quantity: 3 }
          ]
        },
        'Mega': {
          'dine-in': [
            { stockItemId: stockItemIds['Assiettes Jetables'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes en Papier'], quantity: 3 }
          ],
          'takeaway': [
            { stockItemId: stockItemIds['Boîte Pizza 40cm'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes en Papier'], quantity: 3 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Boîte Pizza 40cm'], quantity: 1 },
            { stockItemId: stockItemIds['Sacs Papier Kraft'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes en Papier'], quantity: 4 }
          ]
        },
        'Géante': {
          'dine-in': [
            { stockItemId: stockItemIds['Assiettes Jetables'], quantity: 2 },
            { stockItemId: stockItemIds['Serviettes en Papier'], quantity: 4 }
          ],
          'takeaway': [
            { stockItemId: stockItemIds['Boîte Pizza 40cm'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes en Papier'], quantity: 4 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Boîte Pizza 40cm'], quantity: 1 },
            { stockItemId: stockItemIds['Sacs Papier Kraft'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes en Papier'], quantity: 5 }
          ]
        }
      };

      // Apply pizza packaging rules
      for (const [size, orderTypes] of Object.entries(pizzaPackagingRules)) {
        for (const [orderType, packaging] of Object.entries(orderTypes)) {
          await updateCategoryPackaging(pizzaCategory.id, size, orderType as any, packaging);
        }
      }
    }

    // Configure packaging for Drinks category
    const drinksCategory = menuCategories.find(cat => cat.name === 'Boissons');
    if (drinksCategory) {
      const drinksPackagingRules = {
        '33cl': {
          'dine-in': [
            { stockItemId: stockItemIds['Gobelets 25cl'], quantity: 1 }
          ],
          'takeaway': [
            { stockItemId: stockItemIds['Gobelets 25cl'], quantity: 1 },
            { stockItemId: stockItemIds['Sacs Papier Kraft'], quantity: 1 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Gobelets 25cl'], quantity: 1 },
            { stockItemId: stockItemIds['Sacs Papier Kraft'], quantity: 1 }
          ]
        },
        '1L': {
          'dine-in': [
            { stockItemId: stockItemIds['Gobelets 25cl'], quantity: 2 }
          ],
          'takeaway': [
            { stockItemId: stockItemIds['Gobelets 25cl'], quantity: 2 },
            { stockItemId: stockItemIds['Sacs Papier Kraft'], quantity: 1 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Gobelets 25cl'], quantity: 2 },
            { stockItemId: stockItemIds['Sacs Papier Kraft'], quantity: 1 }
          ]
        }
      };

      // Apply drinks packaging rules
      for (const [size, orderTypes] of Object.entries(drinksPackagingRules)) {
        for (const [orderType, packaging] of Object.entries(orderTypes)) {
          await updateCategoryPackaging(drinksCategory.id, size, orderType as any, packaging);
        }
      }
    }
    console.log('✅ Packaging rules configured.');

    // --- CATEGORY-SPECIFIC SUPPLEMENTS ---
    console.log('⏳ Seeding category-specific supplements...');
    
    // Pizza supplements
    const pizzaSupplements: Supplement[] = [
      {
        id: `sup_${uuidv4()}`,
        name: 'Bourrage Fromage',
        description: 'Double portion de fromage',
        stockConsumption: {
          stockItemId: stockItemIds['Mozzarella Râpée'],
          quantities: { Normale: 0.05, Mega: 0.08, Géante: 0.1 },
        },
        isActive: true,
      },
      {
        id: `sup_${uuidv4()}`,
        name: 'Cheddar',
        description: 'Ajout de cheddar',
        stockConsumption: {
          stockItemId: stockItemIds['Mozzarella Râpée'],
          quantities: { Normale: 0.03, Mega: 0.05, Géante: 0.07 },
        },
        isActive: true,
      },
      {
        id: `sup_${uuidv4()}`,
        name: 'Poulet Fumé',
        description: 'Ajout de poulet fumé',
        stockConsumption: {
          stockItemId: stockItemIds['Mozzarella Râpée'],
          quantities: { Normale: 0.04, Mega: 0.06, Géante: 0.08 },
        },
        isActive: true,
      },
      {
        id: `sup_${uuidv4()}`,
        name: 'Camembert',
        description: 'Ajout de camembert',
        stockConsumption: {
          stockItemId: stockItemIds['Mozzarella Râpée'],
          quantities: { Normale: 0.03, Mega: 0.05, Géante: 0.07 },
        },
        isActive: true,
      },
      {
        id: `sup_${uuidv4()}`,
        name: 'Viande Hachée',
        description: 'Ajout de viande hachée',
        stockConsumption: {
          stockItemId: stockItemIds['Mozzarella Râpée'],
          quantities: { Normale: 0.05, Mega: 0.08, Géante: 0.1 },
        },
        isActive: true,
      },
      {
        id: `sup_${uuidv4()}`,
        name: 'Gruyère',
        description: 'Ajout de gruyère',
        stockConsumption: {
          stockItemId: stockItemIds['Mozzarella Râpée'],
          quantities: { Normale: 0.03, Mega: 0.05, Géante: 0.07 },
        },
        isActive: true,
      },
      {
        id: `sup_${uuidv4()}`,
        name: 'Olives',
        description: 'Ajout d\'olives',
        stockConsumption: {
          stockItemId: stockItemIds['Olives Noires'],
          quantities: { Normale: 0.02, Mega: 0.03, Géante: 0.05 },
        },
        isActive: true,
      },
      {
        id: `sup_${uuidv4()}`,
        name: 'Champignons',
        description: 'Ajout de champignons frais',
        stockConsumption: {
          stockItemId: stockItemIds['Champignons Frais'],
          quantities: { Normale: 0.03, Mega: 0.05, Géante: 0.07 },
        },
        isActive: true,
      },
    ];

    // Create supplements for Pizza category
    const pizzaCategoryId = menuCategoryIds['Pizzas'];
    if (pizzaCategoryId) {
      for (const supplement of pizzaSupplements) {
        await createSupplement(pizzaCategoryId, supplement);
      }
      
      // Configure pricing for Pizza supplements
      await updateCategorySupplementConfig(pizzaCategoryId, {
        globalPricing: { Normale: 100, Mega: 150, Géante: 200 },
        isEnabled: true,
      });
    }

    // Drinks supplements (different supplements for drinks)
    const drinkSupplements: Supplement[] = [
      {
        id: `sup_${uuidv4()}`,
        name: 'Glaçons Extra',
        description: 'Portion supplémentaire de glaçons',
        stockConsumption: {
          stockItemId: stockItemIds['Eau Minérale 0.5L'], // Using water as proxy for ice
          quantities: { '33cl': 0.05, '1L': 0.1 },
        },
        isActive: true,
      },
    ];

    // Create supplements for Drinks category
    const drinksCategoryId = menuCategoryIds['Boissons'];
    if (drinksCategoryId) {
      for (const supplement of drinkSupplements) {
        await createSupplement(drinksCategoryId, supplement);
      }
      
      // Configure pricing for Drinks supplements (different pricing structure)
      await updateCategorySupplementConfig(drinksCategoryId, {
        globalPricing: { '33cl': 10, '1L': 20 },
        isEnabled: true,
      });
    }

    console.log('✅ Category-specific supplements seeded and configured.');

    console.log('🎉 Universal seed V4 completed successfully!');
  } catch (error) {
    console.error('❌ Error running universal seed V4:', error);
    throw error;
  }
}
// endknowledge
