import { v4 as uuidv4 } from 'uuid';

// Helper function to create unique IDs
const createId = () => uuidv4();

// Define pizza sizes with Algerian names
export const pizzaSizes = ['Petite', 'Moyenne', 'Grande'];

// Define pizza addons with Algerian prices
export const pizzaAddons = [
  { id: createId(), name: 'Fromage Extra', price: 150 },
  { id: createId(), name: 'Champignons', price: 100 },
  { id: createId(), name: 'Olives', price: 80 },
  { id: createId(), name: 'Poulet', price: 200 },
  { id: createId(), name: 'Viande Hachée', price: 250 }
];

// Define sandwich sizes
export const sandwichSizes = ['Simple', 'Double'];

// Define sandwich addons
export const sandwichAddons = [
  { id: createId(), name: 'Fromage', price: 80 },
  { id: createId(), name: 'Oeuf', price: 100 },
  { id: createId(), name: 'Frites', price: 80 },
  { id: createId(), name: '<PERSON>uce <PERSON>', price: 50 }
];

// Define drink sizes
export const drinkSizes = ['Petit', 'Moyen', 'Grand'];

// Create sample Algerian menu data structure
export const algerianMenu = [
  {
    id: createId(),
    name: 'Pizzas',
    emoji: '🍕',
    sizes: pizzaSizes,
    items: [
      {
        id: createId(),
        name: 'Margherita',
        prices: {
          'Petite': 450,
          'Moyenne': 650,
          'Grande': 850
        }
      },
      {
        id: createId(),
        name: 'Quatre Fromages',
        prices: {
          'Petite': 600,
          'Moyenne': 800,
          'Grande': 1000
        }
      },
      {
        id: createId(),
        name: 'Végétarienne',
        prices: {
          'Petite': 500,
          'Moyenne': 700,
          'Grande': 900
        }
      },
      {
        id: createId(),
        name: 'Poulet',
        prices: {
          'Petite': 550,
          'Moyenne': 750,
          'Grande': 950
        }
      },
      {
        id: createId(),
        name: 'Viande Hachée',
        prices: {
          'Petite': 600,
          'Moyenne': 800,
          'Grande': 1000
        }
      }
    ]
  },
  {
    id: createId(),
    name: 'Sandwiches',
    emoji: '🥪',
    sizes: sandwichSizes,
    items: [
      {
        id: createId(),
        name: 'Kebab',
        prices: {
          'Simple': 300,
          'Double': 450
        }
      },
      {
        id: createId(),
        name: 'Poulet',
        prices: {
          'Simple': 350,
          'Double': 500
        }
      },
      {
        id: createId(),
        name: 'Merguez',
        prices: {
          'Simple': 300,
          'Double': 450
        }
      },
      {
        id: createId(),
        name: 'Kefta',
        prices: {
          'Simple': 350,
          'Double': 500
        }
      }
    ]
  },
  {
    id: createId(),
    name: 'Boissons',
    emoji: '🥤',
    sizes: drinkSizes,
    items: [
      {
        id: createId(),
        name: 'Coca-Cola',
        prices: {
          'Petit': 80,
          'Moyen': 120,
          'Grand': 160
        }
      },
      {
        id: createId(),
        name: 'Fanta',
        prices: {
          'Petit': 80,
          'Moyen': 120,
          'Grand': 160
        }
      },
      {
        id: createId(),
        name: 'Eau Minérale',
        prices: {
          'Petit': 50,
          'Moyen': 70,
          'Grand': 100
        }
      },
      {
        id: createId(),
        name: 'Thé à la Menthe',
        prices: {
          'Petit': 80,
          'Moyen': 120,
          'Grand': 160
        }
      }
    ]
  }
];
