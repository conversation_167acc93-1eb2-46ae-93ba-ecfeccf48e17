/**
 * Build utility functions to detect and handle different build targets
 */

/**
 * Check if the current build is targeting web (landing page only)
 */
export function isWebBuild(): boolean {
  return process.env.BUILD_TARGET === 'web' || 
         process.env.NEXT_PUBLIC_BUILD_TARGET === 'web';
}

/**
 * Check if the current build is targeting electron (desktop app)
 */
export function isElectronBuild(): boolean {
  return process.env.BUILD_TARGET === 'electron' || 
         process.env.NEXT_PUBLIC_BUILD_TARGET === 'electron';
}

/**
 * Check if the current build is targeting static (static export)
 */
export function isStaticBuild(): boolean {
  return process.env.BUILD_TARGET === 'static' || 
         process.env.NEXT_PUBLIC_BUILD_TARGET === 'static';
}

/**
 * Check if the current build supports full restaurant functionality
 */
export function supportsRestaurantFeatures(): boolean {
  return !isWebBuild(); // Web builds don't support restaurant features
}

/**
 * Get the appropriate redirect URL based on build target and auth state
 */
export function getAuthRedirectUrl(isAuthenticated: boolean): string {
  if (isWebBuild()) {
    // Web builds always redirect to landing page
    return '/landing';
  }
  
  if (isAuthenticated) {
    // Native builds redirect to app
    return '/menu';
  }
  
  // Not authenticated - go to auth page
  return '/auth';
}

/**
 * Get the appropriate button text based on build target and auth state
 */
export function getAuthButtonText(isAuthenticated: boolean): string {
  if (isWebBuild()) {
    return isAuthenticated ? 'تحميل التطبيق الكامل' : 'إنشاء حساب';
  }
  
  return isAuthenticated ? 'الذهاب إلى لوحة التحكم' : 'تسجيل الدخول';
}

/**
 * Get build target name for display purposes
 */
export function getBuildTargetName(): string {
  if (isWebBuild()) return 'Web (Landing Only)';
  if (isElectronBuild()) return 'Electron (Desktop)';
  if (isStaticBuild()) return 'Static (Offline)';
  return 'Development';
}