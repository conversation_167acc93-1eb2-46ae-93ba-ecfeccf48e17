import { useRouter } from 'next/navigation';
import { usePlatform } from '@/lib/context/platform-context';

// Navigation utility that works in both static and dynamic modes
export function useStaticNavigation() {
  const router = useRouter();
  const { isStatic } = usePlatform();
  
  const navigate = (path: string) => {
    // Remove leading slash for consistency
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    
    if (isStatic) {
      // Static mode: navigate to .html files produced by Next build
      console.log(`🔗 [StaticNav] Navigating to: /${cleanPath}.html`);
      window.location.href = `/${cleanPath}.html`;
    } else {
      // Dynamic mode: use Next.js router
      console.log(`🔗 [DynamicNav] Navigating to: /${cleanPath}`);
      router.push(`/${cleanPath}`);
    }
  };
  
  const replace = (path: string) => {
    // Remove leading slash for consistency
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    
    if (isStatic) {
      // Static mode: navigate to .html files
      console.log(`🔗 [StaticNav] Replacing with: /${cleanPath}.html`);
      window.location.replace(`/${cleanPath}.html`);
    } else {
      // Dynamic mode: use Next.js router
      console.log(`🔗 [DynamicNav] Replacing with: /${cleanPath}`);
      router.replace(`/${cleanPath}`);
    }
  };
  
  return { navigate, replace };
}

// Direct function for use outside of React components
export function navigateStatic(path: string) {
  // Remove leading slash for consistency
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  
  // Use the same detection logic as isStaticMode()
  const isLikelyStatic = isStaticMode();
  
  if (isLikelyStatic) {
    // Static mode with trailingSlash export: use /path/index.html
    console.log(`🔗 [StaticNav] Direct navigation to: /${cleanPath}/index.html`);
    window.location.href = `/${cleanPath}/index.html`;
  } else {
    // Dynamic mode: use window.location for simplicity
    console.log(`🔗 [DynamicNav] Direct navigation to: /${cleanPath}`);
    window.location.href = `/${cleanPath}`;
  }
}

// Helper to check if we're in static mode
export function isStaticMode(): boolean {
  if (typeof window === 'undefined') return false;
  
  // For Electron: check if we're being served by electron-serve (static files)
  // vs. dev server (localhost:3000) or CouchDB server (random localhost port)
  if (window.navigator.userAgent.toLowerCase().includes('electron')) {
    // In dev mode, we're served from localhost:3000
    // In static packaged mode, we're served from app:// protocol or file:// protocol
    const isDevMode = window.location.href.includes('localhost:3000');
    const isStaticElectron = !isDevMode;
    console.log(`🖥️ [isStaticMode] Electron detected: ${isStaticElectron ? 'STATIC' : 'DEV'} mode`);
    return isStaticElectron;
  }
  
  // For web/Capacitor: check original logic
  return window.location.protocol === 'file:' || 
         !window.location.href.includes('localhost:3000');
} 