/**
 * Environment Utility Functions
 * 
 * This file provides utility functions for detecting the runtime environment
 * and making environment-specific decisions.
 */

import { Capacitor } from '@capacitor/core';
import type { ReactNode } from 'react';

/**
 * Check if the code is running in Electron environment
 */
export function isElectronEnvironment(): boolean {
  return typeof window !== 'undefined' && (window as any).IS_DESKTOP_APP === true;
}

/**
 * Check if the code is running in a server environment (Node.js)
 */
export function isServerEnvironment(): boolean {
  return typeof window === 'undefined';
}

/**
 * Check if the code is running in a browser environment (not Electron)
 */
export function isWebBrowserEnvironment(): boolean {
  return typeof window !== 'undefined' && !isElectronEnvironment();
}

/**
 * Get environment name for logging
 */
export function getEnvironmentName(): string {
  if (isServerEnvironment()) return 'SERVER';
  if (isElectronEnvironment()) return 'ELECTRON';
  if (isMobileEnvironment()) return 'MOBILE';
  return 'WEB';
}

/**
 * Create a logger that prefixes messages with the environment name
 */
export function createEnvironmentLogger(prefix: string) {
  const envName = getEnvironmentName();
  
  return {
    info: (...args: any[]) => {
      console.log(`[${envName}] [${prefix}]`, ...args);
    },
    warn: (...args: any[]) => {
      console.warn(`[${envName}] [${prefix}]`, ...args);
    },
    error: (...args: any[]) => {
      console.error(`[${envName}] [${prefix}]`, ...args);
    },
    debug: (...args: any[]) => {
      console.debug(`[${envName}] [${prefix}]`, ...args);
    },
    log: (...args: any[]) => {
      console.log(`[${envName}] [${prefix}]`, ...args);
    }
  };
}

/**
 * Check if we're running in a mobile app via Capacitor
 */
export function isMobileApp(): boolean {
  return (
    typeof window !== 'undefined' && 
    Capacitor.isNativePlatform() && 
    (Capacitor.getPlatform() === 'android' || Capacitor.getPlatform() === 'ios')
  );
}

/**
 * Check if we're running in an Electron app
 */
export function isElectronApp(): boolean {
  return isElectronEnvironment();
}

/**
 * Check if we're running in a browser
 */
export function isBrowser(): boolean {
  return typeof window !== 'undefined' && !isElectronApp() && !isMobileApp();
}

/**
 * Get the current platform name
 */
export function getPlatformName(): 'web' | 'electron' | 'android' | 'ios' | 'unknown' {
  if (typeof window === 'undefined') return 'unknown';
  
  if (isElectronApp()) return 'electron';
  if (isMobileApp()) return Capacitor.getPlatform() as 'android' | 'ios';
  if (isBrowser()) return 'web';
  
  return 'unknown';
}

/**
 * Check if the code is running in a mobile app (Capacitor)
 */
export function isMobileEnvironment(): boolean {
  if (typeof window === 'undefined') return false;
  
  return (
    typeof (window as any).Capacitor !== 'undefined' || 
    typeof navigator === 'object' && navigator.userAgent.includes('capacitor')
  );
}

/**
 * Check if the device is currently online
 * This is more reliable than just checking navigator.onLine
 * as it can detect "lie-fi" situations where the device is connected 
 * to a network but has no actual internet access.
 */
export function isOnline(): boolean {
  // Server-side always returns true (assumes online)
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return true;
  }
  
  // Use navigator.onLine as the primary check
  return navigator.onLine;
}

/**
 * Register online/offline event listeners
 * @param callbacks Object with onOnline and onOffline functions
 * @returns Function to unregister listeners
 */
export function registerNetworkListeners(callbacks: {
  onOnline?: () => void;
  onOffline?: () => void;
}): () => void {
  if (typeof window === 'undefined') {
    return () => {}; // No-op for server-side
  }
  
  const handleOnline = () => {
    if (callbacks.onOnline) callbacks.onOnline();
  };
  
  const handleOffline = () => {
    if (callbacks.onOffline) callbacks.onOffline();
  };
  
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  // Return a function to unregister the listeners
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
} 