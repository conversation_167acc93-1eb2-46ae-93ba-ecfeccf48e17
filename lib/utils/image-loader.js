// Custom image loader for static builds
// This ensures images work correctly in electron-serve and static exports

export default function imageLoader({ src, width, quality }) {
  // For static builds, return the src as-is
  // electron-serve will handle serving from root properly
  if (process.env.BUILD_TARGET === 'static' || process.env.BUILD_TARGET === 'electron') {
    return src; // Keep original path - electron-serve handles it
  }
  
  // For dynamic builds, you could add more sophisticated image optimization here
  return src;
} 