// Build configuration and environment detection
export const BUILD_CONFIG = {
  // Detect if we're in a static build (no server-side features)
  isStatic: typeof window !== 'undefined', // Client-side = static build
  
  // Platform detection
  isElectron: typeof window !== 'undefined' && window.navigator.userAgent.includes('Electron'),
  isMobile: typeof window !== 'undefined' && /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
  
  // Environment detection - for static builds, assume production
  isDevelopment: false, // Static builds are always production
  isProduction: true,   // Static builds are always production
  
  // Remote server configuration for static builds
  remoteServerUrl: 'http://localhost:3000',
  fallbackServerUrl: 'https://bistro.icu',
  
  // Feature flags based on build target
  features: {
    // Server-side features not available in static builds
    serverAuth: false,
    apiRoutes: false,
    ssr: false,
    
    // Remote API access for static builds when online
    remoteApi: true,
    
    // Client-side features available in all builds
    offlineSync: true,
    pouchdb: true,
    localDb: true,
  }
};

// Helper to check if server-side features are available
export const canUseServerFeatures = () => {
  return BUILD_CONFIG.features.serverAuth && typeof window === 'undefined';
};

// Helper to get the appropriate database adapter
export const getDbAdapter = () => {
  if (BUILD_CONFIG.isElectron) {
    return 'http'; // For Electron with CouchDB
  }
  if (BUILD_CONFIG.isMobile) {
    return 'memory'; // For Capacitor mobile apps
  }
  return 'idb'; // For web browsers
};

// API URL helper that works in all environments
export const getApiUrl = (endpoint: string) => {
  // Static builds: try localhost first, fallback to bistro.icu
  return `${BUILD_CONFIG.remoteServerUrl}/api/${endpoint}`;
};

// Helper to get fallback URL
export const getFallbackApiUrl = (endpoint: string) => {
  return `${BUILD_CONFIG.fallbackServerUrl}/api/${endpoint}`;
};

// Helper to check if we can reach the remote server
export const checkRemoteConnectivity = async (): Promise<boolean> => {
  // Try localhost first
  try {
    const response = await fetch(`${BUILD_CONFIG.remoteServerUrl}/api/health`, {
      method: 'GET',
      mode: 'cors',
      credentials: 'omit', // Don't send cookies for health check
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(5000) // Increased timeout
    });
    if (response.ok) {
      console.log('✅ localhost:3000 is reachable');
      return true;
    }
    console.log(`❌ localhost:3000 responded with status: ${response.status}`);
  } catch (error) {
    console.log('❌ localhost:3000 not reachable, trying fallback...', error instanceof Error ? error.message : error);
  }
  
  // Fallback to bistro.icu
  try {
    const response = await fetch(`${BUILD_CONFIG.fallbackServerUrl}/api/health`, {
      method: 'GET',
      mode: 'cors',
      credentials: 'omit', // Don't send cookies for health check
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(8000) // Longer timeout for remote server
    });
    if (response.ok) {
      console.log('✅ bistro.icu fallback is reachable');
      return true;
    }
    console.log(`❌ bistro.icu responded with status: ${response.status}`);
  } catch (error) {
    console.log('❌ bistro.icu fallback also not reachable', error instanceof Error ? error.message : error);
  }
  
  console.log('🔌 Server not reachable - user can choose offline mode if needed');
  return false;
}; 