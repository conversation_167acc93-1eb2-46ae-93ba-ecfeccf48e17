"use client";

import { useState, useEffect } from 'react';
import { databaseV4 } from '@/lib/db/v4/core/db-instance';
import type { OrderDocument } from '@/lib/db/v4/schemas/order-schema';
import { getHours, parseISO } from 'date-fns';

// Types
export interface SaleItem {
  id: string;
  name: string;
  category: string;
  quantity: number;
  sales: number;
  unitPrice: number;
  profit: number;
  profitMargin: number;
  profitabilityScore?: number;
  contributionToTotalProfit?: number;
}

export interface SalesByOrderType {
  type: string;
  orderCount: number;
  totalSales: number;
  avgTicket: number;
  percentage: number;
}

export interface PeakHour {
  hour: string;
  sales: number;
  orders: number;
  avgTicket: number;
}

export interface KPIs {
  netSales: number;
  orderCount: number;
  avgTicket: number;
  avgItemsPerOrder: number;
  totalProfit?: number;
  grossProfitMargin?: number;
}

export interface OrderAnalyticsData {
  salesByItem: SaleItem[];
  salesByOrderType: SalesByOrderType[];
  peakHours: PeakHour[];
  orders: OrderDocument[];
  kpis: KPIs;
  metadata: {
    restaurantId: string;
    startDate: string;
    endDate: string;
    totalOrdersProcessed: number;
    completedOrders: number;
  };
}

/**
 * Custom hook for fetching and processing order analytics
 */
export function useOrderAnalyticsV4(startDate?: string, endDate?: string, restaurantId: string = 'default') {
  const [data, setData] = useState<OrderAnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrderData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log('🔍 [Analytics] Starting real data fetch...');
        console.log('📅 [Analytics] Date range:', { startDate, endDate });
        console.log('🏪 [Analytics] Restaurant ID:', restaurantId);

        // Ensure database is initialized
        if (!databaseV4.isInitialized) {
          console.log('🔧 [Analytics] Initializing database...');
          await databaseV4.initialize(restaurantId);
          console.log('✅ [Analytics] Database initialized');
        }

        // 🚀 Enhancement 2: Analytics indexes are now created automatically during DB initialization
        // No need to create them here anymore - they're part of the essential indexes

        // First, let's see what's actually in the database
        console.log('🔍 [Analytics] Checking database contents...');
        try {
          // Check all documents
          const allDocsResult = await databaseV4.findDocs<any>({
            selector: {},
            limit: 10
          });
          console.log('📋 [Analytics] Database contents:', {
            totalDocs: allDocsResult.docs?.length || 0,
            docTypes: allDocsResult.docs?.map(d => ({ 
              id: d._id, 
              type: d.type, 
              status: (d as any).status,
              total: (d as any).total,
              createdAt: (d as any).createdAt 
            })) || []
          });

          // Check specifically for order documents
          const orderDocsResult = await databaseV4.findDocs<any>({
            selector: { type: 'order_document' },
            limit: 10
          });
          console.log('📋 [Analytics] Order documents found:', {
            count: orderDocsResult.docs?.length || 0,
            orders: orderDocsResult.docs?.map(d => ({ 
              id: d._id, 
              status: d.status, 
              total: d.total,
              createdAt: d.createdAt,
              orderType: d.orderType,
              paymentStatus: d.paymentStatus
            })) || []
          });

          // Check for completed orders specifically
          const completedOrdersResult = await databaseV4.findDocs<any>({
            selector: { 
              type: 'order_document',
              status: 'completed'
            },
            limit: 10
          });
          console.log('✅ [Analytics] Completed orders found:', {
            count: completedOrdersResult.docs?.length || 0,
            completedOrders: completedOrdersResult.docs?.map(d => ({ 
              id: d._id, 
              status: d.status, 
              total: d.total,
              createdAt: d.createdAt,
              paymentStatus: d.paymentStatus
            })) || []
          });
        } catch (debugError) {
          console.error('❌ [Analytics] Debug query error:', debugError);
        }

        // Fetch orders with optimized query
        let orders: OrderDocument[];
        
        // Try getAllOrders function first as a fallback to see if we get any data
        console.log('🔍 [Analytics] Testing getAllOrders function...');
        try {
          const { getAllOrders } = await import('@/lib/db/v4/operations/order-ops');
          const allOrdersFromFunction = await getAllOrders();
          console.log('📊 [Analytics] getAllOrders result:', {
            count: allOrdersFromFunction.length,
            statuses: allOrdersFromFunction.reduce((acc, o) => {
              acc[o.status] = (acc[o.status] || 0) + 1;
              return acc;
            }, {} as Record<string, number>),
            completedCount: allOrdersFromFunction.filter(o => o.status === 'completed').length
          });
        } catch (getAllOrdersError) {
          console.error('❌ [Analytics] getAllOrders function error:', getAllOrdersError);
        }
        
        if (startDate && endDate) {
          console.log('📋 [Analytics] Fetching orders in date range...');
          // Try without date filtering first to see if we get orders at all
          console.log('🔍 [Analytics] First trying without date filter...');
          const testResult = await databaseV4.findDocs<OrderDocument>({
            selector: { 
              type: 'order_document'
            },
            sort: [{ type: 'asc' }, { createdAt: 'desc' }]
          });
          console.log('📊 [Analytics] Test query (no date filter):', {
            found: testResult.docs?.length || 0,
            statuses: testResult.docs?.reduce((acc, o) => {
              acc[o.status] = (acc[o.status] || 0) + 1;
              return acc;
            }, {} as Record<string, number>) || {}
          });
          
          // Now try with date range using optimized query
          const result = await databaseV4.findDocs<OrderDocument>({
            selector: { 
              type: 'order_document',
              createdAt: {
                $gte: startDate,
                $lte: endDate
              }
            },
            // 🚀 Enhancement 2: Use the analytics-optimized index for better performance
            sort: [{ type: 'asc' }, { createdAt: 'desc' }],
            use_index: ['analytics-type-status-date-idx', 'analytics-type-status-date-idx'] // Use our new composite index
          });
          orders = result.docs || [];
          console.log(`📊 [Analytics] Found ${orders.length} orders in date range`);
          console.log('📊 [Analytics] Date range query result:', {
            query: { startDate, endDate },
            found: orders.length,
            orders: orders.slice(0, 3).map(o => ({ 
              id: o._id, 
              status: o.status, 
              total: o.total, 
              createdAt: o.createdAt 
            }))
          });
        } else {
          console.log('📋 [Analytics] Fetching all orders...');
          // Optimized query for all orders
          const result = await databaseV4.findDocs<OrderDocument>({
            selector: { 
              type: 'order_document'
            },
            // 🚀 Enhancement 2: Use optimized index for all orders query too
            sort: [{ type: 'asc' }, { createdAt: 'desc' }],
            use_index: ['order-type-created-at-idx', 'order-type-created-at-idx'], // Use our type+createdAt index
            limit: 1000 // Reasonable limit for performance
          });
          orders = result.docs || [];
          console.log(`📊 [Analytics] Found ${orders.length} total orders`);
          console.log('📊 [Analytics] All orders query result:', {
            found: orders.length,
            statusBreakdown: orders.reduce((acc, o) => {
              acc[o.status] = (acc[o.status] || 0) + 1;
              return acc;
            }, {} as Record<string, number>),
            sampleOrders: orders.slice(0, 3).map(o => ({ 
              id: o._id, 
              status: o.status, 
              total: o.total, 
              createdAt: o.createdAt 
            }))
          });
        }

        // Log real data statistics
        if (orders.length > 0) {
          const completedOrders = orders.filter(o => o.status === 'completed');
          const totalSales = completedOrders.reduce((sum, o) => sum + o.total, 0);
          console.log('📈 [Analytics] Real data stats:', {
            totalOrders: orders.length,
            completedOrders: completedOrders.length,
            totalSales,
            dateRange: { startDate, endDate }
          });
        } else {
          console.warn('⚠️ [Analytics] No orders found! Trying fallback with getAllOrders...');
          
          // Fallback: use getAllOrders and filter manually
          try {
            const { getAllOrders } = await import('@/lib/db/v4/operations/order-ops');
            const allOrdersFromFunction = await getAllOrders();
            
            // Apply date filtering manually if needed
            if (startDate && endDate) {
              orders = allOrdersFromFunction.filter(order => {
                const orderDate = new Date(order.createdAt);
                const start = new Date(startDate);
                const end = new Date(endDate);
                return orderDate >= start && orderDate <= end;
              });
              console.log(`📊 [Analytics] Fallback with date filter: ${orders.length} orders`);
            } else {
              orders = allOrdersFromFunction;
              console.log(`📊 [Analytics] Fallback without date filter: ${orders.length} orders`);
            }
            
            if (orders.length > 0) {
              const completedOrders = orders.filter(o => o.status === 'completed');
              console.log('📈 [Analytics] Fallback data stats:', {
                totalOrders: orders.length,
                completedOrders: completedOrders.length,
                statuses: orders.reduce((acc, o) => {
                  acc[o.status] = (acc[o.status] || 0) + 1;
                  return acc;
                }, {} as Record<string, number>)
              });
            }
          } catch (fallbackError) {
            console.error('❌ [Analytics] Fallback getAllOrders failed:', fallbackError);
          }
        }

        // Process the real data
        if (!orders || orders.length === 0) {
          console.log('📊 [Analytics] No real orders found - returning empty structure');
          setData(createEmptyAnalyticsData(restaurantId, startDate, endDate));
          setIsLoading(false);
          return;
        }

        console.log('🔄 [Analytics] Processing real order data...');
        console.log('📊 [Analytics] Orders to process:', {
          total: orders.length,
          byStatus: orders.reduce((acc, o) => {
            acc[o.status] = (acc[o.status] || 0) + 1;
            return acc;
          }, {} as Record<string, number>),
          completedOrders: orders.filter(o => o.status === 'completed').length,
          sampleCompletedOrders: orders.filter(o => o.status === 'completed').slice(0, 2).map(o => ({
            id: o._id,
            total: o.total,
            items: o.items?.length || 0,
            createdAt: o.createdAt
          }))
        });
        
        // Process real data for analytics with performance optimizations
        const salesByItem = calculateSalesByItem(orders);
        const salesByOrderType = calculateSalesByOrderType(orders);
        const peakHours = calculatePeakHours(orders);
        const kpis = calculateKPIs(orders, salesByItem);
        
        console.log('📊 [Analytics] Processing results:', {
          salesByItemCount: salesByItem.length,
          salesByOrderTypeCount: salesByOrderType.length,
          kpis: kpis,
          sampleSalesByItem: salesByItem.slice(0, 3)
        });
        
        // Only completed orders for visualizations
        const completedOrdersForHeatmap = orders.filter(o => o.status === 'completed');

        console.log('📈 [Analytics] Real data processed:', {
          salesByItemCount: salesByItem.length,
          salesByOrderTypeCount: salesByOrderType.length,
          completedOrdersCount: completedOrdersForHeatmap.length,
          totalRevenue: kpis.netSales
        });

        setData({
          salesByItem,
          salesByOrderType,
          peakHours,
          orders: completedOrdersForHeatmap,
          kpis,
          metadata: {
            restaurantId,
            startDate: startDate || 'all',
            endDate: endDate || 'all',
            totalOrdersProcessed: orders.length,
            completedOrders: completedOrdersForHeatmap.length
          }
        });

        console.log('✅ [Analytics] Real data processing completed');
      } catch (e) {
        console.error("❌ [Analytics] Error fetching real analytics data:", e);
        setError(e instanceof Error ? e.message : 'Unknown error occurred while loading analytics data');
        setData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrderData();
  }, [startDate, endDate, restaurantId]);

  return { data, isLoading, error };
}

// 🚀 Enhancement 2: Analytics indexes are now created automatically during DB initialization
// The old createAnalyticsIndexes function has been removed since indexes are part of essential indexes

// Helper to create empty data structure
function createEmptyAnalyticsData(restaurantId: string, startDate?: string, endDate?: string): OrderAnalyticsData {
  const emptyPeakHours = Array(24).fill(null).map((_, i) => ({ 
    hour: `${String(i).padStart(2, '0')}:00`, 
    sales: 0, 
    orders: 0, 
    avgTicket: 0 
  }));
  
  return {
    salesByItem: [],
    salesByOrderType: [],
    peakHours: emptyPeakHours,
    orders: [],
    kpis: { 
      netSales: 0, 
      orderCount: 0, 
      avgTicket: 0, 
      avgItemsPerOrder: 0, 
      totalProfit: 0, 
      grossProfitMargin: 0 
    },
    metadata: {
      restaurantId,
      startDate: startDate || 'all',
      endDate: endDate || 'all',
      totalOrdersProcessed: 0,
      completedOrders: 0
    }
  };
}

// Helper functions for processing order data
function calculateSalesByItem(orders: OrderDocument[]): SaleItem[] {
  const itemMap = new Map<string, SaleItem>();
  orders.forEach(order => {
    if (order.status !== 'completed') return;
    order.items.forEach(item => {
      const existingItem = itemMap.get(item.menuItemId);
      // Use real COGS if available, otherwise fallback to 40% estimate
      const itemCogs = item.cogs || (item.price * 0.4);
      const itemProfit = (item.price * item.quantity) - (itemCogs * item.quantity);
      
      if (existingItem) {
        existingItem.quantity += item.quantity;
        existingItem.sales += item.price * item.quantity;
        existingItem.profit += itemProfit;
      } else {
        itemMap.set(item.menuItemId, {
          id: item.menuItemId,
          name: item.name,
          category: item.category || 'N/A',
          quantity: item.quantity,
          sales: item.price * item.quantity,
          unitPrice: item.price,
          profit: itemProfit,
          profitMargin: item.price > 0 ? ((item.price - (itemCogs || 0)) / item.price) * 100 : 0,
        });
      }
    });
  });
  
  let allItems = Array.from(itemMap.values());
  const totalProfit = allItems.reduce((sum, item) => sum + item.profit, 0);
  
  allItems = allItems.map(item => ({
    ...item,
    profitabilityScore: item.quantity > 0 ? parseFloat((item.profit / item.quantity).toFixed(2)) : 0,
    contributionToTotalProfit: totalProfit > 0 ? parseFloat(((item.profit / totalProfit) * 100).toFixed(2)) : 0,
    profitMargin: item.sales > 0 ? parseFloat(((item.profit / item.sales) * 100).toFixed(2)) : 0,
  }));
  
  return allItems;
}

function calculateSalesByOrderType(orders: OrderDocument[]): SalesByOrderType[] {
  const typeMap = new Map<string, { count: number; totalSales: number; totalItems: number }>();
  orders.forEach(order => {
    if (order.status !== 'completed') return;
    const orderType = order.orderType || 'unknown';
    const existingType = typeMap.get(orderType);
    const orderTotalItems = order.items.reduce((sum, item) => sum + item.quantity, 0);
    if (existingType) {
      existingType.count += 1;
      existingType.totalSales += order.total;
      existingType.totalItems += orderTotalItems;
    } else {
      typeMap.set(orderType, {
        count: 1,
        totalSales: order.total,
        totalItems: orderTotalItems,
      });
    }
  });
  const results: SalesByOrderType[] = [];
  const totalOrders = orders.filter(o => o.status === 'completed').length;
  typeMap.forEach((data, type) => {
    results.push({
      type: type,
      orderCount: data.count,
      totalSales: data.totalSales,
      avgTicket: data.count > 0 ? data.totalSales / data.count : 0,
      percentage: totalOrders > 0 ? (data.count / totalOrders) * 100 : 0,
    });
  });
  return results;
}

function calculatePeakHours(orders: OrderDocument[]): PeakHour[] {
  const hoursMap = new Map<number, { sales: number; orders: number }>();
  for (let i = 0; i < 24; i++) {
    hoursMap.set(i, { sales: 0, orders: 0 });
  }
  orders.forEach(order => {
    if (order.status !== 'completed') return;
    const hour = getHours(parseISO(order.createdAt));
    const currentHourData = hoursMap.get(hour);
    if (currentHourData) {
      currentHourData.sales += order.total;
      currentHourData.orders += 1;
    }
  });
  const results: PeakHour[] = [];
  hoursMap.forEach((data, hour) => {
    results.push({
      hour: `${String(hour).padStart(2, '0')}:00`,
      sales: data.sales,
      orders: data.orders,
      avgTicket: data.orders > 0 ? data.sales / data.orders : 0,
    });
  });
  return results.sort((a,b) => parseInt(a.hour) - parseInt(b.hour));
}

function calculateKPIs(orders: OrderDocument[], salesByItem: SaleItem[]): KPIs {
  const completedOrders = orders.filter(order => order.status === 'completed');
  const netSales = completedOrders.reduce((sum, order) => sum + order.total, 0);
  const orderCount = completedOrders.length;
  const totalItemsSold = completedOrders.reduce((sum, order) => 
    sum + order.items.reduce((itemSum, item) => itemSum + item.quantity, 0), 0);
  
  // Use real profit data from orders if available, otherwise calculate from salesByItem
  const totalProfit = completedOrders.some(order => order.grossProfit !== undefined)
    ? completedOrders.reduce((sum, order) => sum + (order.grossProfit || 0), 0)
    : salesByItem.reduce((acc, item) => acc + item.profit, 0);
    
  const grossProfitMargin = netSales > 0 ? (totalProfit / netSales) * 100 : 0;
  
  return {
    netSales,
    orderCount,
    avgTicket: orderCount > 0 ? netSales / orderCount : 0,
    avgItemsPerOrder: orderCount > 0 ? totalItemsSold / orderCount : 0,
    totalProfit,
    grossProfitMargin,
  };
} 