// knowledge: useCOGSV4 v4 COGS hook for COGS/recipe/production management
import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
// TODO: Replace with actual v4 db adapters/services for COGS/recipes when available
// import { cogsDbV4 } from '@/lib/db/v4-cogs-adapter';
import { SubRecipe, MenuItemRecipe, ProductionBatch } from '@/types/cogs';
// knowledge: use v4 settings for COGS
import { getSettings, updateSettings } from '@/lib/db/v4';
// knowledge: import v4 COGS/production CRUD
import {
  createSubRecipe as v4CreateSubRecipe,
  getSubRecipe as v4GetSubRecipe,
  getAllSubRecipes as v4GetAllSubRecipes,
  updateSubRecipe as v4UpdateSubRecipe,
  deleteSubRecipe as v4DeleteSubRecipe,
  createMenuItemRecipe as v4CreateMenuItemRecipe,
  getMenuItemRecipe as v4GetMenuItemRecipe,
  getAllMenuItemRecipes as v4GetAllMenuItemRecipes,
  updateMenuItemRecipe as v4UpdateMenuItemRecipe,
  deleteMenuItemRecipe as v4DeleteMenuItemRecipe,
  createProductionBatch as v4CreateProductionBatch,
  getProductionBatch as v4GetProductionBatch,
  getAllProductionBatches as v4GetAllProductionBatches,
  updateProductionBatch as v4UpdateProductionBatch,
  deleteProductionBatch as v4DeleteProductionBatch,
  // initializeV4Database,
  // getCurrentRestaurantId
} from '@/lib/db/v4';
import { useSettings } from '@/lib/context/settings-context';
// endknowledge

interface UseCOGSV4Result {
  isCogsEnabled: boolean;
  toggleCOGS: (enabled: boolean) => Promise<void>;
  updateCogsSettings: (settings: any) => Promise<void>;
  subRecipes: SubRecipe[];
  createSubRecipe: (subRecipe: Omit<SubRecipe, '_id' | 'costPerUnit' | 'lastProduced' | 'createdAt' | 'updatedAt'>) => Promise<SubRecipe>;
  updateSubRecipe: (id: string, updates: Partial<SubRecipe>) => Promise<SubRecipe>;
  deleteSubRecipe: (id: string) => Promise<void>;
  menuItemRecipes: MenuItemRecipe[];
  createMenuItemRecipe: (recipe: Omit<MenuItemRecipe, '_id' | 'costPerUnit' | 'createdAt' | 'updatedAt'>) => Promise<MenuItemRecipe>;
  updateMenuItemRecipe: (id: string, updates: Partial<MenuItemRecipe>) => Promise<MenuItemRecipe>;
  deleteMenuItemRecipe: (id: string) => Promise<void>;
  productionBatches: ProductionBatch[];
  createProductionBatch: (batch: Omit<ProductionBatch, '_id' | 'type' | 'createdAt' | 'updatedAt'>) => Promise<ProductionBatch>;
  getBatchesForSubRecipe: (subRecipeId: string) => ProductionBatch[];
  loading: boolean;
  error: Error | null;
  isReady: boolean;
  refreshData: () => Promise<void>;
  settings: any;
}

export function useCOGSV4(): UseCOGSV4Result {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const { isDbInitialized, isLoadingDb, dbInitializeError, currentDbRestaurantId } = useUnifiedDB();
  const {
    settings,
    loading: settingsLoading,
    error: settingsError,
    refreshSettings
  } = useSettings();
  const [subRecipes, setSubRecipes] = useState<SubRecipe[]>([]);
  const [menuItemRecipes, setMenuItemRecipes] = useState<MenuItemRecipe[]>([]);
  const [productionBatches, setProductionBatches] = useState<ProductionBatch[]>([]);
  const [isCogsDataLoading, setIsCogsDataLoading] = useState<boolean>(true);
  const [cogsError, setCogsError] = useState<Error | null>(null);

  // Enhanced refreshData function that explicitly fetches everything fresh
  const refreshAllCogsData = async () => {
    if (!isDbInitialized) {
      console.warn('[useCOGSV4] Cannot refresh COGS data: DB not initialized.');
      setIsCogsDataLoading(false);
      setSubRecipes([]);
      setMenuItemRecipes([]);
      setProductionBatches([]);
      return;
    }
    setIsCogsDataLoading(true);
    setCogsError(null);
    try {
      const [subs, menuRecipes, batches] = await Promise.all([
        v4GetAllSubRecipes(),
        v4GetAllMenuItemRecipes(),
        v4GetAllProductionBatches()
      ]);
      setSubRecipes(subs);
      setMenuItemRecipes(menuRecipes);
      setProductionBatches(batches);
    } catch (error: any) {
      console.error('[useCOGSV4] Error refreshing COGS data:', error);
      setCogsError(error);
    } finally {
      setIsCogsDataLoading(false);
    }
  };

  useEffect(() => {
    if (isDbInitialized && isAuthenticated) {
      refreshAllCogsData();
    } else {
      setSubRecipes([]);
      setMenuItemRecipes([]);
      setProductionBatches([]);
      setIsCogsDataLoading(true);
      if (dbInitializeError) {
        setCogsError(new Error(`Database initialization error: ${dbInitializeError.message}`));
      }
    }
  }, [isDbInitialized, isAuthenticated, dbInitializeError]);

  // knowledge: implement v4 CRUD for sub-recipes with explicit types
  const createSubRecipe = async (subRecipe: Omit<SubRecipe, '_id' | 'costPerUnit' | 'lastProduced' | 'createdAt' | 'updatedAt'>): Promise<SubRecipe> => {
    const created = await v4CreateSubRecipe(subRecipe);
    setSubRecipes((prev) => [...prev, created]);
    return created;
  };
  const updateSubRecipe = async (id: string, updates: Partial<SubRecipe>): Promise<SubRecipe> => {
    const updated = await v4UpdateSubRecipe(id, updates);
    if (!updated) throw new Error('SubRecipe not found');
    setSubRecipes((prev) => prev.map((s) => (s._id === id ? updated : s)));
    return updated;
  };
  const deleteSubRecipe = async (id: string): Promise<void> => {
    await v4DeleteSubRecipe(id);
    setSubRecipes((prev) => prev.filter((s) => s._id !== id));
  };
  // endknowledge

  // knowledge: implement v4 CRUD for menu item recipes with explicit types
  const createMenuItemRecipe = async (recipe: Omit<MenuItemRecipe, '_id' | 'costPerUnit' | 'createdAt' | 'updatedAt'>): Promise<MenuItemRecipe> => {
    const created = await v4CreateMenuItemRecipe(recipe);
    setMenuItemRecipes((prev) => [...prev, created]);
    return created;
  };
  const updateMenuItemRecipe = async (id: string, updates: Partial<MenuItemRecipe>): Promise<MenuItemRecipe> => {
    const updated = await v4UpdateMenuItemRecipe(id, updates);
    if (!updated) throw new Error('MenuItemRecipe not found');
    setMenuItemRecipes((prev) => prev.map((r) => (r._id === id ? updated : r)));
    return updated;
  };
  const deleteMenuItemRecipe = async (id: string): Promise<void> => {
    await v4DeleteMenuItemRecipe(id);
    setMenuItemRecipes((prev) => prev.filter((r) => r._id !== id));
  };
  // endknowledge

  // knowledge: fix createProductionBatch type to match v4 CRUD
  const createProductionBatch = async (batch: Omit<ProductionBatch, '_id' | 'type' | 'createdAt' | 'updatedAt'>): Promise<ProductionBatch> => {
    const created = await v4CreateProductionBatch(batch);
    setProductionBatches((prev) => [...prev, created]);
    return created;
  };
  const getBatchesForSubRecipe = (subRecipeId: string): ProductionBatch[] => {
    return productionBatches.filter((b) => b.subRecipeId === subRecipeId);
  };
  // endknowledge

  return {
    settings,
    isCogsEnabled: true,
    updateCogsSettings: async (settings: any) => {},
    refreshData: async () => {
      await refreshAllCogsData();
    },
    subRecipes,
    createSubRecipe,
    updateSubRecipe,
    deleteSubRecipe,
    menuItemRecipes,
    createMenuItemRecipe,
    updateMenuItemRecipe,
    deleteMenuItemRecipe,
    productionBatches,
    createProductionBatch,
    getBatchesForSubRecipe,
    loading: authLoading || isLoadingDb || settingsLoading || isCogsDataLoading,
    error: dbInitializeError || (settingsError instanceof Error ? settingsError : null) || cogsError,
    isReady: isAuthenticated && isDbInitialized && !settingsLoading && !isCogsDataLoading && !cogsError && !dbInitializeError,
  };
}
// end knowledge 