import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import {
  InventoryDocument,
  StockItem,
  StockAdjustment,
  PurchaseLog,
  StockCount,
  StockCountItem,
  WasteLog,
  ConsumptionLog
} from '@/lib/db/v4/schemas/inventory-schema';
import {
  getInventory as getInventoryOp,
  updateInventory as updateInventoryOp,
  addStockItem as addStockItemOp,
  updateStockItem as updateStockItemOp,
  deleteStockItem as deleteStockItemOp,
  addStockAdjustment as addStockAdjustmentOp,
  updateStockAdjustment as updateStockAdjustmentOp,
  deleteStockAdjustment as deleteStockAdjustmentOp,
  addPurchaseLog as addPurchaseLogOp,
  updatePurchaseLog as updatePurchaseLogOp,
  deletePurchaseLog as deletePurchaseLogOp,
  addStockCount as addStockCountOp,
  updateStockCount as updateStockCountOp,
  deleteStockCount as deleteStockCountOp,
  addStockCountItem as addStockCountItemOp,
  updateStockCountItem as updateStockCountItemOp,
  deleteStockCountItem as deleteStockCountItemOp,
  addWasteLog as addWasteLogOp,
  updateWasteLog as updateWasteLogOp,
  deleteWasteLog as deleteWasteLogOp,
  applyStockCountV4 as applyStockCountOp,
  getConsumptionLogsForOrder,
  getConsumptionLogsInDateRange,
  calculateOrderCOGS
} from '@/lib/db/v4/operations/inventory-ops';

interface UseStockV4Result {
  stockItems: StockItem[];
  refreshStock: () => Promise<void>;
  createStockItem: (item: Omit<StockItem, 'id' | 'createdAt' | 'updatedAt' | 'value'>) => Promise<StockItem>;
  updateStockItem: (id: string, updates: Partial<StockItem>) => Promise<StockItem>;
  deleteStockItem: (id: string) => Promise<void>;

  purchases: PurchaseLog[];
  refreshPurchases: () => Promise<void>;
  createPurchaseLog: (purchase: Omit<PurchaseLog, 'id' | 'createdAt' | 'updatedAt' | 'performedBy'>) => Promise<PurchaseLog>;
  getPurchasesForItem: (stockItemId: string) => Promise<PurchaseLog[]>;
  getPurchasesInDateRange: (startDate: Date, endDate: Date) => Promise<PurchaseLog[]>;

  createStockAdjustment: (adjustment: Omit<StockAdjustment, 'id' | 'createdAt' | 'updatedAt'>) => Promise<StockAdjustment>;
  getStockAdjustments: (stockItemId: string) => Promise<StockAdjustment[]>;

  stockCounts: StockCount[];
  refreshStockCounts: () => Promise<void>;
  createStockCount: (count: Omit<StockCount, 'id' | 'createdAt' | 'updatedAt' | 'performedBy'>) => Promise<StockCount>;
  updateStockCount: (id: string, updates: Partial<StockCount>) => Promise<StockCount>;
  deleteStockCount: (id: string) => Promise<void>;
  getStockCount: (id: string) => Promise<StockCount>;

  createStockCountItem: (item: Omit<StockCountItem, 'id' | 'createdAt' | 'updatedAt' | 'variance' | 'varianceValue'>) => Promise<StockCountItem>;
  getStockCountItems: (stockCountId: string) => Promise<StockCountItem[]>;
  updateStockCountItem: (id: string, updates: Partial<StockCountItem>) => Promise<StockCountItem>;
  applyStockCount: (stockCountId: string) => Promise<void>;

  wasteLogs: WasteLog[];
  refreshWasteLogs: () => Promise<void>;
  createWasteLog: (wasteLog: Omit<WasteLog, 'id' | 'createdAt' | 'updatedAt' | 'performedBy'>) => Promise<WasteLog>;
  getWasteLogsForItem: (stockItemId: string) => Promise<WasteLog[]>;
  getWasteLogsInDateRange: (startDate: Date, endDate: Date) => Promise<WasteLog[]>;

  // New consumption log functions
  getConsumptionLogsForOrder: (orderId: string) => Promise<ConsumptionLog[]>;
  getConsumptionLogsInDateRange: (startDate: Date, endDate: Date) => Promise<ConsumptionLog[]>;
  calculateOrderCOGS: (orderId: string) => Promise<number>;

  isLoading: boolean;
  error: Error | null;
  isReady: boolean;
}

export function useStockV4(): UseStockV4Result {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const { isDbInitialized, currentDbRestaurantId, dbInitializeError, isLoadingDb } = useUnifiedDB();

  const [stockItems, setStockItems] = useState<StockItem[]>([]);
  const [purchases, setPurchases] = useState<PurchaseLog[]>([]);
  const [stockCounts, setStockCounts] = useState<StockCount[]>([]);
  const [wasteLogs, setWasteLogs] = useState<WasteLog[]>([]);
  
  const [isDataLoading, setIsDataLoading] = useState<boolean>(true);
  const [hookError, setHookError] = useState<Error | null>(null);
  const [isHookReady, setIsHookReady] = useState<boolean>(false);

  const loadInitialData = useCallback(async () => {
    if (!isDbInitialized || !isAuthenticated) {
      console.warn('[useStockV4.loadInitialData] Cannot run: DB not initialized or user not authenticated.');
      setIsHookReady(false);
      return;
    }

    if (isHookReady) {
      console.log('[useStockV4.loadInitialData] Hook already ready for this state, skipping.');
      return;
    }

    console.log('[useStockV4.loadInitialData] STARTING initial inventory data load...');
    setIsDataLoading(true);
    setHookError(null);
    try {
      console.log('[useStockV4.loadInitialData] Calling getInventoryOp()...');
      const inventoryDoc = await getInventoryOp();
      console.log('[useStockV4.loadInitialData] getInventoryOp() SUCCEEDED. Setting state...');
      setStockItems(inventoryDoc.items || []);
      setPurchases(inventoryDoc.purchases || []);
      setStockCounts(inventoryDoc.stockCounts || []);
      setWasteLogs(inventoryDoc.wasteLogs || []);
      console.log('[useStockV4.loadInitialData] Setting isHookReady = true.');
      setIsHookReady(true);
    } catch (err) {
      console.error('[useStockV4.loadInitialData] ERROR loading initial inventory data:', err);
      setHookError(err instanceof Error ? err : new Error('Failed to load initial inventory data'));
      console.log('[useStockV4.loadInitialData] Setting isHookReady = false due to error.');
      setIsHookReady(false);
    } finally {
      console.log('[useStockV4.loadInitialData] Setting isDataLoading = false.');
      setIsDataLoading(false);
    }
  }, [isDbInitialized, isAuthenticated]);

  useEffect(() => {
    console.log(`[useStockV4.useEffect] Triggered. AuthLoading: ${authLoading}, IsAuthenticated: ${isAuthenticated}, IsDbInitialized: ${isDbInitialized}`);
    
    if (dbInitializeError) {
      console.warn('[useStockV4.useEffect] DB initialization error detected, but allowing potential recovery:', dbInitializeError.message);
      // Don't immediately fail - the database might recover or be in a transient state
      // Just reset the hook state and wait for potential recovery
      setIsHookReady(false);
      setIsDataLoading(false);
      setStockItems([]);
      setPurchases([]);
      setStockCounts([]);
      setWasteLogs([]);
      // Only set hook error if the database is actually marked as initialized but still has errors
      if (isDbInitialized) {
        setHookError(new Error(`Database initialization completed with errors: ${dbInitializeError.message}`));
      } else {
        setHookError(null); // Clear previous errors to allow recovery
      }
      return;
    }

    if (isDbInitialized && isAuthenticated) {
      console.log('[useStockV4.useEffect] Conditions met. Calling loadInitialData.');
      loadInitialData();
    } else {
      console.log('[useStockV4.useEffect] Conditions NOT MET (DB not init or not Auth). Resetting hook state.');
      setStockItems([]);
      setPurchases([]);
      setStockCounts([]);
      setWasteLogs([]);
      setIsDataLoading(true); 
      setIsHookReady(false); 
      setHookError(null);
    }
  }, [isDbInitialized, isAuthenticated, dbInitializeError]);

  const refreshStock = async () => {
    if (!isHookReady) return;
    setIsDataLoading(true);
    try {
      const doc = await getInventoryOp();
      setStockItems(doc.items || []);
      setHookError(null);
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to load stock items'));
    } finally {
      setIsDataLoading(false);
    }
  };

  const refreshPurchases = async () => {
    if (!isHookReady) return;
    setIsDataLoading(true);
    try {
      const doc = await getInventoryOp();
      setPurchases(doc.purchases || []);
      setHookError(null);
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to load purchases'));
    } finally {
      setIsDataLoading(false);
    }
  };

  const createStockItem = async (item: Omit<StockItem, 'id' | 'createdAt' | 'updatedAt' | 'value'>): Promise<StockItem> => {
    console.log(`[useStockV4.createStockItem] Called. isHookReady: ${isHookReady} isAuthenticated: ${isAuthenticated}`);
    if (!isHookReady || !isAuthenticated) {
      console.error('[useStockV4.createStockItem] Aborted: Hook not ready or user not authenticated.');
      throw new Error('Inventory system is not ready.');
    }
    try {
      const newItem = await addStockItemOp(item);
      await refreshStock();
      return newItem;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to create stock item'));
      throw err;
    }
  };

  const updateStockItem = async (id: string, updates: Partial<StockItem>): Promise<StockItem> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const updatedItem = await updateStockItemOp(id, updates);
      await refreshStock();
      return updatedItem;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to update stock item'));
      throw err;
    }
  };

  const deleteStockItem = async (id: string): Promise<void> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      await deleteStockItemOp(id);
      await refreshStock();
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to delete stock item'));
      throw err;
    }
  };

  const createPurchaseLog = async (purchase: Omit<PurchaseLog, 'id' | 'createdAt' | 'updatedAt' | 'performedBy'>): Promise<PurchaseLog> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const purchaseWithPerformer = {
        ...purchase,
        performedBy: user?.name || (user as any)?.username || 'unknown'
      };
      const newPurchase = await addPurchaseLogOp(purchaseWithPerformer);
      await refreshPurchases();
      return newPurchase;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to create purchase log'));
      throw err;
    }
  };

  const getPurchasesForItem = async (stockItemId: string): Promise<PurchaseLog[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const doc = await getInventoryOp();
      return (doc.purchases || []).filter(p => p.stockItemId === stockItemId);
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to get purchases for item'));
      throw err;
    }
  };

  const getPurchasesInDateRange = async (startDate: Date, endDate: Date): Promise<PurchaseLog[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const doc = await getInventoryOp();
      return (doc.purchases || []).filter(p => {
        const d = new Date(p.date);
        return d >= startDate && d <= endDate;
      });
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to get purchases in date range'));
      throw err;
    }
  };

  const createStockAdjustment = async (adjustment: Omit<StockAdjustment, 'id' | 'createdAt' | 'updatedAt'>): Promise<StockAdjustment> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const adjWithPerformer = {
        ...adjustment,
        performedBy: user?.name || (user as any)?.username || 'unknown'
      };
      const newAdj = await addStockAdjustmentOp(adjWithPerformer);
      await refreshStock();
      return newAdj;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to create stock adjustment'));
      throw err;
    }
  };

  const getStockAdjustments = async (stockItemId: string): Promise<StockAdjustment[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const doc = await getInventoryOp();
      return (doc.adjustments || []).filter(a => a.stockItemId === stockItemId);
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to get stock adjustments'));
      throw err;
    }
  };

  const refreshStockCounts = async () => {
    if (!isHookReady) return;
    setIsDataLoading(true);
    try {
      const doc = await getInventoryOp();
      setStockCounts(doc.stockCounts || []);
      setHookError(null);
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to load stock counts'));
    } finally {
      setIsDataLoading(false);
    }
  };

  const createStockCount = async (count: Omit<StockCount, 'id' | 'createdAt' | 'updatedAt' | 'performedBy'>): Promise<StockCount> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const countWithPerformer = {
        ...count,
        performedBy: user?.name || (user as any)?.username || 'unknown'
      };
      const newCount = await addStockCountOp(countWithPerformer);
      await refreshStockCounts();
      return newCount;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to create stock count'));
      throw err;
    }
  };

  const updateStockCount = async (id: string, updates: Partial<StockCount>): Promise<StockCount> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const updatedCount = await updateStockCountOp(id, updates);
      await refreshStockCounts();
      return updatedCount;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to update stock count'));
      throw err;
    }
  };

  const deleteStockCount = async (id: string): Promise<void> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      await deleteStockCountOp(id);
      await refreshStockCounts();
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to delete stock count'));
      throw err;
    }
  };

  const getStockCount = async (id: string): Promise<StockCount> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const doc = await getInventoryOp();
      const found = (doc.stockCounts || []).find(c => c.id === id);
      if (!found) throw new Error('Stock count not found');
      return found;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to get stock count'));
      throw err;
    }
  };

  const createStockCountItem = async (item: Omit<StockCountItem, 'id' | 'createdAt' | 'updatedAt' | 'variance' | 'varianceValue'>): Promise<StockCountItem> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const newItem = await addStockCountItemOp(item);
      await refreshStockCounts();
      return newItem;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to create stock count item'));
      throw err;
    }
  };

  const getStockCountItems = async (stockCountId: string): Promise<StockCountItem[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const doc = await getInventoryOp();
      return (doc.stockCountItems || []).filter(i => i.stockCountId === stockCountId);
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to get stock count items'));
      throw err;
    }
  };

  const updateStockCountItem = async (id: string, updates: Partial<StockCountItem>): Promise<StockCountItem> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const updatedItem = await updateStockCountItemOp(id, updates);
      await refreshStockCounts();
      return updatedItem;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to update stock count item'));
      throw err;
    }
  };

  const applyStockCount = async (stockCountId: string): Promise<void> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      await applyStockCountOp(stockCountId);
      await refreshStockCounts();
      await refreshStock();
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to apply stock count'));
      throw err;
    }
  };

  const refreshWasteLogs = async () => {
    if (!isHookReady) return;
    setIsDataLoading(true);
    try {
      const doc = await getInventoryOp();
      setWasteLogs(doc.wasteLogs || []);
      setHookError(null);
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to load waste logs'));
    } finally {
      setIsDataLoading(false);
    }
  };

  const createWasteLog = async (wasteLog: Omit<WasteLog, 'id' | 'createdAt' | 'updatedAt' | 'performedBy'>): Promise<WasteLog> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const logWithPerformer = {
        ...wasteLog,
        performedBy: user?.name || (user as any)?.username || 'unknown'
      };
      const newLog = await addWasteLogOp(logWithPerformer);
      await refreshWasteLogs();
      await refreshStock();
      return newLog;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to create waste log'));
      throw err;
    }
  };

  const getWasteLogsForItem = async (stockItemId: string): Promise<WasteLog[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const doc = await getInventoryOp();
      return (doc.wasteLogs || []).filter(w => w.stockItemId === stockItemId);
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to get waste logs for item'));
      throw err;
    }
  };

  const getWasteLogsInDateRange = async (startDate: Date, endDate: Date): Promise<WasteLog[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const doc = await getInventoryOp();
      return (doc.wasteLogs || []).filter(w => {
        const d = new Date(w.date);
        return d >= startDate && d <= endDate;
      });
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to get waste logs in date range'));
      throw err;
    }
  };

  const getConsumptionLogsForOrderHook = async (orderId: string): Promise<ConsumptionLog[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const logs = await getConsumptionLogsForOrder(orderId);
      setHookError(null);
      return logs;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to get consumption logs for order'));
      throw err;
    }
  };

  const getConsumptionLogsInDateRangeHook = async (startDate: Date, endDate: Date): Promise<ConsumptionLog[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const logs = await getConsumptionLogsInDateRange(startDate, endDate);
      setHookError(null);
      return logs;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to get consumption logs in date range'));
      throw err;
    }
  };

  const calculateOrderCOGSHook = async (orderId: string): Promise<number> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const cogs = await calculateOrderCOGS(orderId);
      setHookError(null);
      return cogs;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to calculate order COGS'));
      throw err;
    }
  };

  return {
    stockItems,
    refreshStock,
    createStockItem,
    updateStockItem,
    deleteStockItem,
    purchases,
    refreshPurchases,
    createPurchaseLog,
    getPurchasesForItem,
    getPurchasesInDateRange,
    createStockAdjustment,
    getStockAdjustments,
    stockCounts,
    refreshStockCounts,
    createStockCount,
    updateStockCount,
    deleteStockCount,
    getStockCount,
    createStockCountItem,
    getStockCountItems,
    updateStockCountItem,
    applyStockCount,
    wasteLogs,
    refreshWasteLogs,
    createWasteLog,
    getWasteLogsForItem,
    getWasteLogsInDateRange,
    getConsumptionLogsForOrder: getConsumptionLogsForOrderHook,
    getConsumptionLogsInDateRange: getConsumptionLogsInDateRangeHook,
    calculateOrderCOGS: calculateOrderCOGSHook,
    isLoading: isLoadingDb || isDataLoading,
    error: dbInitializeError || hookError,
    isReady: isDbInitialized && isHookReady && !isDataLoading
  };
}