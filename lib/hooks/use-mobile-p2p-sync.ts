import { useState, useEffect, useCallback } from 'react';
import { MobileP2PSync } from '../p2p/mobile-p2p-sync';
import { PeerInfo, onZeroconfLog, logZeroconfMessage } from '../p2p/zeroconf-discovery';
import { SyncStatus } from '../../types/p2p-sync';
import { isMobileApp } from '../utils/environment';

// Global instance for singleton pattern
let mobilePTPSyncInstance: MobileP2PSync | null = null;

/**
 * Hook for using mobile P2P sync in React components with Multi-Master Support
 * 
 * Mobile devices only act as clients that discover and connect to desktop hubs.
 * They do not publish their own services since they don't host HTTP servers.
 * Mobile-to-mobile sync is not supported.
 * 
 * NEW: Multi-master sync allows connecting to ALL discovered CouchDB servers simultaneously.
 */
export function useMobileP2PSync(pouchDb: any, port: number = 8000) {
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [desktopHubs, setDesktopHubs] = useState<PeerInfo[]>([]);
  const [syncStatuses, setSyncStatuses] = useState<SyncStatus[]>([]);
  const [error, setError] = useState<Error | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [mdnsStatus, setMdnsStatus] = useState<'not_running' | 'running' | 'error'>('not_running');
  
  // Multi-master sync state
  const [multiMasterStatus, setMultiMasterStatus] = useState<{
    config: any;
    databases: string[];
    activeSyncs: number;
    connectedPeers: number;
    syncsByDatabase: Record<string, number>;
  }>({
    config: { enabled: false, databases: [], autoSync: true, direction: 'both' },
    databases: [],
    activeSyncs: 0,
    connectedPeers: 0,
    syncsByDatabase: {}
  });
  
  const [initSteps, setInitSteps] = useState<{step: string, status: 'pending'|'success'|'error', error?: string}[]>([
    {step: 'Creating MobileP2PSync', status: 'pending'},
    {step: 'Initializing ZeroConf Discovery', status: 'pending'},
    {step: 'Setting up desktop hub listeners', status: 'pending'},
    {step: 'Getting desktop hubs and sync status', status: 'pending'}
  ]);

  // Update multi-master status periodically
  useEffect(() => {
    if (!isInitialized || !mobilePTPSyncInstance) return;

    const updateMultiMasterStatus = () => {
      try {
        const status = mobilePTPSyncInstance!.getMultiMasterStatus();
        setMultiMasterStatus(status);
      } catch (error) {
        console.warn('Error getting multi-master status:', error);
      }
    };

    // Update immediately
    updateMultiMasterStatus();

    // Update every 3 seconds
    const interval = setInterval(updateMultiMasterStatus, 3000);

    return () => clearInterval(interval);
  }, [isInitialized]);

  // Initialize mobile P2P sync on mount
  useEffect(() => {
    if (!isMobileApp()) return;

    // Add log listener for debugging
    const logListener = (log: string) => {
      setLogs((prevLogs) => {
        const newLogs = [...prevLogs, log];
        // Keep only the last 100 log entries
        return newLogs.slice(-100);
      });
    };
    onZeroconfLog(logListener);

    async function initializeMobileSync() {
      try {
        // Create MobileP2PSync instance
        mobilePTPSyncInstance = new MobileP2PSync();
        setInitSteps(steps => {
          const newSteps = [...steps];
          newSteps[0] = {...newSteps[0], status: 'success'};
          return newSteps;
        });

        logZeroconfMessage('🚀 Mobile P2P: Starting initialization...');

        // Initialize the sync system
        const initialized = await mobilePTPSyncInstance.initialize(pouchDb, port);
        if (!initialized) {
          throw new Error('Failed to initialize mobile P2P sync');
        }
        setInitSteps(steps => {
          const newSteps = [...steps];
          newSteps[1] = {...newSteps[1], status: 'success'};
          return newSteps;
        });

        setIsInitialized(true);
        setError(null);
        logZeroconfMessage('✅ Mobile P2P: Initialization complete');
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : String(err);
        console.error('Mobile P2P sync initialization error:', err);
        setError(err instanceof Error ? err : new Error(String(err)));
        setInitSteps(steps => {
          const newSteps = [...steps];
          if (newSteps[0].status === 'success') {
            newSteps[1] = {...newSteps[1], status: 'error', error: errorMsg};
          } else {
            newSteps[0] = {...newSteps[0], status: 'error', error: errorMsg};
          }
          return newSteps;
        });
      }
    }

    initializeMobileSync();

    // Set up event listeners for desktop hub discovery
    try {
      mobilePTPSyncInstance!.onPeerDiscovered((peer) => {
        // Only desktop hubs are returned from mobile sync, but double-check
        if (peer.platform === 'desktop') {
          logZeroconfMessage(`📱 Desktop hub discovered: ${peer.hostname}`);
          setDesktopHubs((currentHubs) => {
            if (currentHubs.some((h) => h.id === peer.id)) {
              return currentHubs;
            }
            return [...currentHubs, peer];
          });
        }
      });
      
      mobilePTPSyncInstance!.onPeerLost((peerId) => {
        logZeroconfMessage(`📱 Desktop hub lost: ${peerId}`);
        setDesktopHubs((currentHubs) => currentHubs.filter((h) => h.id !== peerId));
      });
      
      mobilePTPSyncInstance!.onSyncStatusUpdated((status) => {
        setSyncStatuses((currentStatuses) => {
          const index = currentStatuses.findIndex(
            (s) => s.peerId === status.peerId && s.dbName === status.dbName
          );
          if (index >= 0) {
            const newStatuses = [...currentStatuses];
            newStatuses[index] = status;
            return newStatuses;
          } else {
            return [...currentStatuses, status];
          }
        });
      });
      
      setInitSteps(steps => {
        const newSteps = [...steps];
        newSteps[2] = {...newSteps[2], status: 'success'};
        return newSteps;
      });
    } catch (err) {
      setInitSteps(steps => {
        const newSteps = [...steps];
        newSteps[2] = {...newSteps[2], status: 'error', error: String(err)};
        return newSteps;
      });
      setError(err instanceof Error ? err : new Error(String(err)));
    }

    // Poll mDNS status every 2 seconds for live updates
    const interval = setInterval(() => {
      if (mobilePTPSyncInstance) {
        setMdnsStatus(mobilePTPSyncInstance.getMdnsStatus());
      }
    }, 2000);

    return () => {
      clearInterval(interval);
    };
  }, [pouchDb, port]);

  // Get current desktop hubs and sync statuses
  useEffect(() => {
    if (isInitialized && mobilePTPSyncInstance) {
      try {
        const currentHubs = mobilePTPSyncInstance.getPeers();
        const currentStatuses = mobilePTPSyncInstance.getSyncStatus();
        setDesktopHubs(currentHubs);
        setSyncStatuses(currentStatuses);
        setInitSteps(steps => {
          const newSteps = [...steps];
          newSteps[3] = {...newSteps[3], status: 'success'};
          return newSteps;
        });
      } catch (err) {
        console.error('Error getting desktop hubs and sync status:', err);
        setInitSteps(steps => {
          const newSteps = [...steps];
          newSteps[3] = {...newSteps[3], status: 'error', error: String(err)};
          return newSteps;
        });
      }
    }
  }, [isInitialized]);

  // Start sync with a desktop hub
  const startSync = useCallback(async (
    desktopHubId: string, 
    dbName: string, 
    direction: 'push' | 'pull' | 'both' = 'pull'
  ) => {
    if (!mobilePTPSyncInstance) {
      throw new Error('Mobile P2P sync not initialized');
    }
    
    return await mobilePTPSyncInstance.startSync(desktopHubId, dbName, direction);
  }, []);

  // Stop sync with a desktop hub
  const stopSync = useCallback(async (desktopHubId: string, dbName: string) => {
    if (!mobilePTPSyncInstance) {
      throw new Error('Mobile P2P sync not initialized');
    }
    
    await mobilePTPSyncInstance.stopSync(desktopHubId, dbName);
  }, []);

  // Stop all syncs with a desktop hub
  const stopAllSyncsWithHub = useCallback(async (desktopHubId: string) => {
    if (!mobilePTPSyncInstance) {
      throw new Error('Mobile P2P sync not initialized');
    }

    await mobilePTPSyncInstance.stopAllSyncsWithPeer(desktopHubId);
  }, []);

  // 🌟 NEW: Multi-Master Sync Methods

  // Enable multi-master sync for specific databases
  const enableMultiMasterSync = useCallback((databases: string[], direction: 'push' | 'pull' | 'both' = 'both') => {
    if (!mobilePTPSyncInstance) {
      throw new Error('Mobile P2P sync not initialized');
    }
    
    logZeroconfMessage(`📱 🌟 Enabling multi-master sync for databases: ${databases.join(', ')}`);
    mobilePTPSyncInstance.enableMultiMasterSync(databases, direction);
  }, []);

  // Disable multi-master sync
  const disableMultiMasterSync = useCallback(() => {
    if (!mobilePTPSyncInstance) {
      throw new Error('Mobile P2P sync not initialized');
    }
    
    logZeroconfMessage(`📱 🛑 Disabling multi-master sync`);
    mobilePTPSyncInstance.disableMultiMasterSync();
  }, []);

  // Start multi-master sync for a specific database
  const startMultiMasterSync = useCallback(async (
    dbName: string, 
    direction: 'push' | 'pull' | 'both' = 'both'
  ) => {
    if (!mobilePTPSyncInstance) {
      throw new Error('Mobile P2P sync not initialized');
    }
    
    logZeroconfMessage(`📱 🚀 Starting multi-master sync for ${dbName}`);
    return await mobilePTPSyncInstance.startMultiMasterSync(dbName, direction);
  }, []);

  // Stop multi-master sync for a specific database
  const stopMultiMasterSync = useCallback(async (dbName: string) => {
    if (!mobilePTPSyncInstance) {
      throw new Error('Mobile P2P sync not initialized');
    }
    
    logZeroconfMessage(`📱 🛑 Stopping multi-master sync for ${dbName}`);
    await mobilePTPSyncInstance.stopMultiMasterSync(dbName);
  }, []);

  // Configure multi-master sync
  const configureMultiMaster = useCallback((config: {
    enabled?: boolean;
    databases?: string[];
    autoSync?: boolean;
    direction?: 'push' | 'pull' | 'both';
  }) => {
    if (!mobilePTPSyncInstance) {
      throw new Error('Mobile P2P sync not initialized');
    }
    
    logZeroconfMessage(`📱 ⚙️ Configuring multi-master sync: ${JSON.stringify(config)}`);
    mobilePTPSyncInstance.configureMultiMaster(config);
  }, []);

  return {
    // State
    isInitialized,
    desktopHubs, // Only desktop hubs, not "peers"
    syncStatuses,
    error,
    logs,
    mdnsStatus,
    initSteps,

    // Actions
    startSync,
    stopSync,
    stopAllSyncsWithHub,

    // For backward compatibility
    peers: desktopHubs, // Alias for backward compatibility
    stopAllSyncsWithPeer: stopAllSyncsWithHub, // Alias for backward compatibility

    // Multi-Master Sync
    multiMasterStatus,
    enableMultiMasterSync,
    disableMultiMasterSync,
    startMultiMasterSync,
    stopMultiMasterSync,
    configureMultiMaster,
  };
} 