'use client';

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
// Import the simplified order finance service instead of the old one
import { useSimplifiedOrderFinance } from '@/lib/services/simplified-order-finance';
import { OrderDocument } from '@/lib/db/v4/schemas/order-schema';
// TODO: Use v4 order logic here. No v3 allowed.

export interface OrderPaymentResult {
  orderId: string;
  success: boolean;
  error?: string;
  registeredInCaisse: boolean;
}

export interface UseOrderFinanceReturn {
  processOrderPayment: (order: OrderDocument, paymentMethod: 'cash' | 'card' | 'online', receivedAmount?: number) => Promise<OrderPaymentResult>;
  isProcessing: boolean;
  lastResult: OrderPaymentResult | null;
}

/**
 * Hook to integrate order payments with the finance system
 * This hook provides a function to process an order payment and register it in the finance system
 *
 * This is now a wrapper around the simplified order finance hook
 * It maintains the same interface for backward compatibility
 */
export function useOrderFinance(): UseOrderFinanceReturn {
  // Use the simplified order finance hook
  const simplifiedFinance = useSimplifiedOrderFinance();

  // Return the same interface
  return simplifiedFinance;
}