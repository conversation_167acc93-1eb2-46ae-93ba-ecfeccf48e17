'use client';

import { useState, useEffect, useCallback } from 'react';
import { jwtDecode } from 'jwt-decode';
import { JwtPayload } from '@/lib/auth/new-auth-service';
import { useRouter } from 'next/navigation';
import { isAdmin, isOwner, canManageStaff } from '@/lib/auth/role-utils';
import { cleanRestaurantId } from '@/lib/db/db-utils';
import { getApiUrl, checkRemoteConnectivity } from '@/lib/build-config';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

export interface User {
  id: string;
  name: string;
  email?: string;
  role: string;
  restaurantId: string;
  permissions?: any;
  metadata?: any;
}

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

export interface UseAuthReturn extends AuthState {
  login: (credentials: { identifier: string; password: string; restaurantId?: string; isStaffLogin?: boolean }) => Promise<boolean>;
  logout: () => void;
  register: (userData: { name: string; email: string; password: string; phoneNumber?: string }) => Promise<boolean>;
  isAdmin: boolean;
  isOwner: boolean;
  canManageStaff: boolean;
  offlineLogin: (role?: string) => Promise<boolean>;
  isOfflineMode: boolean;
}

const TOKEN_KEY = 'auth_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const OFFLINE_MODE_KEY = 'offline_mode';

// knowledge:start electron environment check simplification
function isElectron() {
  return typeof window !== 'undefined' && window.IS_DESKTOP_APP === true;
}
// knowledge:end electron environment check simplification

async function isServerReachable() {
  try {
    // Use the proper connectivity check that handles remote servers
    return await checkRemoteConnectivity();
  } catch {
    return false;
  }
}

export function useAuth(): UseAuthReturn {
  // Initialize with a stable loading state to prevent flickering
  const [state, setState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    loading: true, // Always start with loading true
    error: null,
  });
  const [isClient, setIsClient] = useState(false);
  const [isOfflineMode, setIsOfflineMode] = useState(false);
  const router = useRouter();

  // Track if auth check has completed to prevent multiple redirects
  const [authCheckComplete, setAuthCheckComplete] = useState(false);

  // Check if we're on the client side
  useEffect(() => {
    if (!isBrowser) {
      // If we're not in a browser, mark auth check as complete with loading false
      setState(prev => ({ ...prev, loading: false }));
      setAuthCheckComplete(true);
      return;
    }

    setIsClient(true);
    
    // Check if offline mode is enabled
    const offlineMode = localStorage.getItem(OFFLINE_MODE_KEY) === 'true';
    setIsOfflineMode(offlineMode);
  }, []);

  // On mount, if Electron and server is unreachable, force offline mode
  useEffect(() => {
    (async () => {
      if (isElectron()) {
        const reachable = await isServerReachable();
        if (!reachable) {
          localStorage.setItem(OFFLINE_MODE_KEY, 'true');
          setIsOfflineMode(true);
        }
      }
    })();
  }, []);

  // Check for token on mount and validate it
  useEffect(() => {
    const checkAuth = async () => {
      if (!isClient) return;

      // 🚨 SIMPLIFIED: For Electron, check for existing auth data only
      if (isElectron()) {
        console.log('🖥️ [Auth] Electron detected - checking existing auth data');
        
        // Debug: Check what's in localStorage
        console.log('🔍 [Auth] Checking localStorage contents:');
        console.log('  - auth_token:', !!localStorage.getItem('auth_token'));
        console.log('  - auth_data:', !!localStorage.getItem('auth_data'));
        
        // Check for existing valid token
        const token = localStorage.getItem('auth_token');
        const authData = localStorage.getItem('auth_data');
        
        if (token && authData) {
          try {
            const decoded = jwtDecode<JwtPayload>(token);
            const userData = JSON.parse(authData);
            
            // Check if token is not expired
            const currentTime = Math.floor(Date.now() / 1000);
            if (decoded.exp && decoded.exp > currentTime) {
              console.log('✅ [Auth] Valid existing token found - user authenticated');
              const user: User = {
                id: userData.userId || decoded.sub,
                name: userData.name || decoded.name,
                email: decoded.email || '',
                role: userData.role || decoded.role || 'staff',
                restaurantId: userData.restaurantId || decoded.restaurantId || '',
                permissions: decoded.permissions,
                metadata: decoded.metadata
              };
              
              setState({
                isAuthenticated: true,
                user,
                loading: false,
                error: null
              });
              setAuthCheckComplete(true);
              return;
            } else {
              console.log('⏰ [Auth] Token expired - clearing auth data');
              localStorage.removeItem('auth_token');
              localStorage.removeItem('auth_data');
            }
          } catch (error) {
            console.error('❌ [Auth] Error parsing existing auth data:', error);
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_data');
          }
        }
        
        // No valid existing auth - set to unauthenticated (show auth page)
        console.log('🔐 [Auth] No valid existing auth - user needs to login');
        setState({
          isAuthenticated: false,
          user: null,
          loading: false,
          error: null
        });
        setAuthCheckComplete(true);
        return;
      }

      // For web builds, use standard token validation
      const timeoutMs = 5000;
      const timeoutId = setTimeout(() => {
        if (!authCheckComplete) {
          console.log(`🕐 [Auth] Timeout after ${timeoutMs}ms`);
          setState({
            isAuthenticated: false,
            user: null,
            loading: false,
            error: null
          });
          setAuthCheckComplete(true);
        }
      }, timeoutMs);

      try {
        const token = localStorage.getItem(TOKEN_KEY);
        if (!token) {
          setState({ isAuthenticated: false, user: null, loading: false, error: null });
          setAuthCheckComplete(true);
          clearTimeout(timeoutId);
          return;
        }

        // Verify and decode token
        try {
          const decoded = jwtDecode<JwtPayload>(token);

          // Check if token is expired
          const currentTime = Math.floor(Date.now() / 1000);
          if (decoded.exp && decoded.exp < currentTime) {
            console.log('AUTH - Token expired');
            setState({ isAuthenticated: false, user: null, loading: false, error: 'Session expired' });
            setAuthCheckComplete(true);
            clearTimeout(timeoutId);
            return;
          }

          // Extract user info from token
          const user: User = {
            id: decoded.sub,
            name: decoded.name,
            email: decoded.email || '',
            role: decoded.role || 'staff',
            restaurantId: decoded.restaurantId || '',
            permissions: decoded.permissions,
            metadata: decoded.metadata
          };

          setState({
            isAuthenticated: true,
            user,
            loading: false,
            error: null,
          });
          
          // Set auth_data for v4 compatibility
          if (isClient && user && user.restaurantId) {
            localStorage.setItem('auth_data', JSON.stringify({
              restaurantId: user.restaurantId,
              userId: user.id,
              role: user.role,
              name: user.name
            }));
          }
          setAuthCheckComplete(true);
          clearTimeout(timeoutId);
        } catch (tokenError) {
          console.error('AUTH - Token decode error:', tokenError);
          setState({ isAuthenticated: false, user: null, loading: false, error: 'Invalid token' });
          setAuthCheckComplete(true);
          clearTimeout(timeoutId);
        }
      } catch (error) {
        console.error('AUTH - General error:', error);
        setState({ isAuthenticated: false, user: null, loading: false, error: 'Authentication error' });
        setAuthCheckComplete(true);
        clearTimeout(timeoutId);
      }
    };

    checkAuth();
  }, [isClient, authCheckComplete]);

  // Login function
  const login = async (credentials: { identifier: string; password: string; restaurantId?: string; isStaffLogin?: boolean }): Promise<boolean> => {
    if (isOfflineMode) {
      // Use local data, skip API
      return false;
    }

    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      
      const endpoint = credentials.isStaffLogin ? 'auth/staff/login' : 'auth/login';
      const apiUrl = getApiUrl(endpoint);
      
      if (!apiUrl) {
        console.log('❌ No API URL available - server not reachable');
        setState((prev) => ({ 
          ...prev, 
          loading: false, 
          error: 'Server not available. Try offline mode if you have previous login data.' 
        }));
        return false;
      }
      
      const payload: any = { password: credentials.password };
      if (credentials.isStaffLogin) {
        payload.username = credentials.identifier;
      } else {
        payload.identifier = credentials.identifier;
        if (credentials.restaurantId) {
          payload.restaurantId = credentials.restaurantId;
        }
      }
      let didTimeout = false;
      // Set a timeout for the fetch (2s)
      const timeoutPromise = new Promise((_, reject) => setTimeout(() => {
        didTimeout = true;
        reject(new Error('Server timeout. Try Offline Mode! 😎'));
      }, 2000));
      try {
        const response = await Promise.race([
          fetch(apiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload),
          }),
          timeoutPromise
        ]) as Response;
        if (didTimeout) throw new Error('Server timeout. Try Offline Mode! 😎');
        const data = await response.json();
        if (!response.ok) {
          // If server login fails, try offline login if we have cached credentials
          const offlineUser = localStorage.getItem('offline_user');
          if (offlineUser) {
            const user = JSON.parse(offlineUser);
            if ((user.email && user.email === credentials.identifier) || (user.name && user.name === credentials.identifier)) {
              setIsOfflineMode(true);
              localStorage.setItem(OFFLINE_MODE_KEY, 'true');
              setState({
                isAuthenticated: true,
                user,
                loading: false,
                error: null,
              });
              if (isClient && user && user.restaurantId) {
                localStorage.setItem('auth_data', JSON.stringify({
                  restaurantId: user.restaurantId,
                  userId: user.id,
                  role: user.role,
                  name: user.name
                }));
              }
              return true;
            }
          }
          setState((prev) => ({ ...prev, loading: false, error: data.error || 'Login failed. Try Offline Mode! 😎' }));
          return false;
        }
        // Store tokens
        if (isClient) {
          localStorage.setItem(TOKEN_KEY, data.token);
          localStorage.setItem(REFRESH_TOKEN_KEY, data.refreshToken);
          localStorage.setItem(OFFLINE_MODE_KEY, 'false'); // We're online now

          // Also store in cookies for server-side access
          document.cookie = `auth_token=${data.token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Strict`;
        }

        setIsOfflineMode(false);

        // Decode user from token
        const decoded = jwtDecode<JwtPayload>(data.token);

        // Debug log: print decoded email and role
        console.log('[DEBUG] Decoded JWT after login:', decoded);
        // Check if super admin
        const superAdminEmail = process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAIL;
        if (superAdminEmail) {
          console.log('[DEBUG] Super admin email from env:', superAdminEmail);
          console.log('[DEBUG] Is super admin?', decoded.email && decoded.email.toLowerCase() === superAdminEmail.toLowerCase());
        }

        // Format restaurant ID consistently by removing any prefix
        const restaurantId = decoded.restaurantId ?
          cleanRestaurantId(decoded.restaurantId) :
          '';

        const user: User = {
          id: decoded.sub,
          name: decoded.name,
          email: decoded.email,
          role: decoded.role,
          restaurantId: restaurantId,
          permissions: decoded.permissions,
          metadata: decoded.metadata
        };

        // Save user data for offline use
        if (isClient) {
          localStorage.setItem('offline_user', JSON.stringify(user));
        }

        console.log('Login successful:', {
          role: decoded.role,
          restaurantId,
          permissions: decoded.permissions,
          user
        });

        setState({
          isAuthenticated: true,
          user,
          loading: false,
          error: null,
        });
        // Debug log: print user state after login
        console.log('[DEBUG] User state after login:', user);
        // --- v4 compatibility: set auth_data in localStorage ---
        if (isClient && user && user.restaurantId) {
          localStorage.setItem('auth_data', JSON.stringify({
            restaurantId: user.restaurantId,
            userId: user.id,
            role: user.role,
            name: user.name
          }));
        }

        return true;
      } catch (error) {
        // Server unreachable or timeout, try offline mode
        const offlineUser = localStorage.getItem('offline_user');
        if (offlineUser) {
          const user = JSON.parse(offlineUser);
          if ((user.email && user.email === credentials.identifier) || (user.name && user.name === credentials.identifier)) {
            setIsOfflineMode(true);
            localStorage.setItem(OFFLINE_MODE_KEY, 'true');
            setState({
              isAuthenticated: true,
              user,
              loading: false,
              error: null,
            });
            if (isClient && user && user.restaurantId) {
              localStorage.setItem('auth_data', JSON.stringify({
                restaurantId: user.restaurantId,
                userId: user.id,
                role: user.role,
                name: user.name
              }));
            }
            return true;
          }
        }
        setState((prev) => ({ ...prev, loading: false, error: 'Cannot connect to server. Try Offline Mode! 😎' }));
        return false;
      }
    } catch (error) {
      setState((prev) => ({ ...prev, loading: false, error: error instanceof Error ? error.message : 'Login failed. Try Offline Mode! 😎' }));
      return false;
    }
  };

  // Offline login function - for demo or emergency access
  const offlineLogin = async (role: string = 'owner'): Promise<boolean> => {
    if (isOfflineMode) {
      // Use local data, skip API
      return true;
    }

    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      
      // Create a demo user
      const demoUser: User = {
        id: 'offline-' + Math.random().toString(36).substring(2, 9),
        name: 'Offline User',
        email: '<EMAIL>',
        role: role, // 'owner', 'staff', etc.
        restaurantId: 'demo-restaurant',
        permissions: {
          pages: { all: true } // Full permissions in offline mode
        },
        metadata: {
          offline: true,
          demoMode: true
        }
      };
      
      // Save offline user
      if (isClient) {
        localStorage.setItem('offline_user', JSON.stringify(demoUser));
        localStorage.setItem(OFFLINE_MODE_KEY, 'true');
        
        // --- v4 compatibility: set auth_data in localStorage ---
        localStorage.setItem('auth_data', JSON.stringify({
          restaurantId: demoUser.restaurantId,
          userId: demoUser.id,
          role: demoUser.role,
          name: demoUser.name
        }));
      }
      
      setIsOfflineMode(true);
      
      setState({
        isAuthenticated: true,
        user: demoUser,
        loading: false,
        error: null,
      });
      
      return true;
    } catch (error) {
      console.error('Offline login error:', error);
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Offline login failed'
      }));
      return false;
    }
  };

  // Logout function
  const logout = () => {
    if (isClient) {
      localStorage.removeItem(TOKEN_KEY);
      localStorage.removeItem(REFRESH_TOKEN_KEY);
      localStorage.removeItem(OFFLINE_MODE_KEY);
      // Don't remove offline_user to allow re-login

      // Also clear cookies
      document.cookie = `auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Strict`;
    }

    setState({
      isAuthenticated: false,
      user: null,
      loading: false,
      error: null,
    });
    
    setIsOfflineMode(false);

    router.push('/auth');
  };

  // Register function
  const register = async (userData: { name: string; email: string; password: string; phoneNumber?: string }): Promise<boolean> => {
    if (isOfflineMode) {
      setState((prev) => ({ ...prev, error: 'Registration requires server connection' }));
      return false;
    }

    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      
      const apiUrl = getApiUrl('auth/register');
      
      if (!apiUrl) {
        console.log('❌ No API URL available for registration - server not reachable');
        setState((prev) => ({ 
          ...prev, 
          loading: false, 
          error: 'Server not available. Registration requires server connection.' 
        }));
        return false;
      }

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
      });

      const data = await response.json();

      if (!response.ok) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error: data.error || 'Registration failed'
        }));
        return false;
      }

      // Auto login after registration
      return await login({
        identifier: userData.email,
        password: userData.password
      });
    } catch (error) {
      console.error('Registration error:', error);
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Registration failed'
      }));
      return false;
    }
  };

  // Token refresh function
  const refreshToken = async (): Promise<boolean> => {
    const token = localStorage.getItem(REFRESH_TOKEN_KEY);
    if (!token) return false;

    try {
      const apiUrl = getApiUrl('auth/refresh');
      
      if (!apiUrl) {
        console.log('❌ No API URL available for token refresh - server not reachable');
        return false;
      }

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: token }),
      });

      const data = await response.json();

      if (!response.ok) {
        logout();
        return false;
      }

      // Store new token
      localStorage.setItem(TOKEN_KEY, data.token);
      localStorage.setItem(OFFLINE_MODE_KEY, 'false'); // We're online now

      // Also store in cookies for server-side access
      document.cookie = `auth_token=${data.token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Strict`;

      // Decode user from token
      const decoded = jwtDecode<JwtPayload>(data.token);
      const user: User = {
        id: decoded.sub,
        name: decoded.name,
        email: decoded.email,
        role: decoded.role,
        restaurantId: decoded.restaurantId,
        permissions: decoded.permissions,
        metadata: decoded.metadata
      };

      // Save user data for offline use
      if (isClient) {
        localStorage.setItem('offline_user', JSON.stringify(user));
      }

      setState({
        isAuthenticated: true,
        user,
        loading: false,
        error: null,
      });
      // --- v4 compatibility: set auth_data in localStorage ---
      if (isClient && user && user.restaurantId) {
        localStorage.setItem('auth_data', JSON.stringify({
          restaurantId: user.restaurantId,
          userId: user.id,
          role: user.role,
          name: user.name
        }));
      }

      return true;
    } catch (error) {
      console.error('Token refresh error:', error);
      
      // If refresh fails due to network issues, try offline mode
      const offlineUser = localStorage.getItem('offline_user');
      if (offlineUser) {
        const user = JSON.parse(offlineUser);
        setIsOfflineMode(true);
        localStorage.setItem(OFFLINE_MODE_KEY, 'true');
        
        setState({
          isAuthenticated: true,
          user,
          loading: false,
          error: null,
        });
        
        // --- v4 compatibility: set auth_data in localStorage ---
        if (isClient && user && user.restaurantId) {
          localStorage.setItem('auth_data', JSON.stringify({
            restaurantId: user.restaurantId,
            userId: user.id,
            role: user.role,
            name: user.name
          }));
        }
        
        return true;
      }
      
      // If no offline user, log out
      logout();
      return false;
    }
  };

  return {
    ...state,
    login,
    logout,
    register,
    offlineLogin,
    isOfflineMode,
    isAdmin: isAdmin(state.user),
    isOwner: isOwner(state.user),
    canManageStaff: canManageStaff(state.user)
  };
}