import { hash, compare } from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { jwtDecode } from 'jwt-decode';

export interface User {
  id: string;
  name: string;
  email?: string;
  username?: string;
  role: string;
  restaurantId: string;
  permissions?: any;   // Added permissions field
  metadata?: any;      // Added metadata field
}

export interface JwtPayload {
  sub: string;         // User ID
  name: string;        // User name
  email?: string;      // User email (optional)
  role: string;        // User role
  restaurantId: string; // Restaurant ID
  permissions?: any;   // User permissions
  metadata?: any;      // User metadata
  iat: number;         // Issued at
  exp: number;         // Expiration time
}

/**
 * Generate a JWT token for a user
 */
export function generateToken(user: User): string {
  // Use JWT_SECRET or fall back to NEXTAUTH_SECRET
  const secret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET;

  if (!secret) {
    throw new Error('JWT_SECRET or NEXTAUTH_SECRET is not defined');
  }

  // Validate permissions structure before including in token
  let validatedPermissions = user.permissions;

  // Only validate if permissions exist
  if (validatedPermissions && validatedPermissions.pages) {
    // Ensure all values are boolean
    const requiredPages = ['menu', 'orders', 'finance', 'inventory', 'staff', 'settings', 'suppliers'];

    // Create a normalized permissions object with all required pages
    const normalizedPages = {};

    for (const page of requiredPages) {
      // Convert to boolean, defaulting to false if not present
      normalizedPages[page] = validatedPermissions.pages[page] === true;
    }

    // Replace the original pages with the normalized ones
    validatedPermissions.pages = normalizedPages;

    console.log('TOKEN: Normalized permissions:', JSON.stringify(validatedPermissions));
  } else if (validatedPermissions && (!validatedPermissions.pages || typeof validatedPermissions.pages !== 'object')) {
    console.log('TOKEN: Warning - Permissions exist but have invalid structure');

    // Create a valid permissions structure
    validatedPermissions = {
      pages: {
        menu: false,
        orders: false,
        finance: false,
        inventory: false,
        staff: false,
        settings: false,
        suppliers: false
      }
    };

    console.log('TOKEN: Created valid permissions structure:', JSON.stringify(validatedPermissions));
  } else if (!validatedPermissions && (user.role === 'waiter' || user.role === 'staff')) {
    console.log('TOKEN: Warning - Staff user has no permissions, creating empty structure');

    // Create an empty permissions structure for staff users
    validatedPermissions = {
      pages: {
        menu: false,
        orders: false,
        finance: false,
        inventory: false,
        staff: false,
        settings: false,
        suppliers: false
      }
    };

    console.log('TOKEN: Created empty permissions structure for staff user:', JSON.stringify(validatedPermissions));
  }

  // Include permissions and metadata in the payload if they exist
  const payload: JwtPayload = {
    sub: user.id,
    name: user.name,
    email: user.email,
    role: user.role,
    restaurantId: user.restaurantId,
    permissions: validatedPermissions, // Include validated permissions
    metadata: user.metadata,           // Include metadata
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 1 day
  };

  // Log the final permissions being included in the token
  console.log('TOKEN: Final permissions in token payload:', JSON.stringify(validatedPermissions));

  return jwt.sign(payload, secret);
}

/**
 * Generate a refresh token for a user (longer expiration)
 */
export function generateRefreshToken(user: User): string {
  // Use JWT_SECRET or fall back to NEXTAUTH_SECRET
  const secret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET;

  if (!secret) {
    throw new Error('JWT_SECRET or NEXTAUTH_SECRET is not defined');
  }

  // Validate permissions structure before including in token (same as in generateToken)
  let validatedPermissions = user.permissions;

  // Only validate if permissions exist
  if (validatedPermissions && validatedPermissions.pages) {
    // Ensure all values are boolean
    const requiredPages = ['menu', 'orders', 'finance', 'inventory', 'staff', 'settings', 'suppliers'];

    // Create a normalized permissions object with all required pages
    const normalizedPages = {};

    for (const page of requiredPages) {
      // Convert to boolean, defaulting to false if not present
      normalizedPages[page] = validatedPermissions.pages[page] === true;
    }

    // Replace the original pages with the normalized ones
    validatedPermissions.pages = normalizedPages;

    console.log('REFRESH TOKEN: Normalized permissions:', JSON.stringify(validatedPermissions));
  } else if (validatedPermissions && (!validatedPermissions.pages || typeof validatedPermissions.pages !== 'object')) {
    console.log('REFRESH TOKEN: Warning - Permissions exist but have invalid structure');

    // Create a valid permissions structure
    validatedPermissions = {
      pages: {
        menu: false,
        orders: false,
        finance: false,
        inventory: false,
        staff: false,
        settings: false,
        suppliers: false
      }
    };

    console.log('REFRESH TOKEN: Created valid permissions structure:', JSON.stringify(validatedPermissions));
  } else if (!validatedPermissions && (user.role === 'waiter' || user.role === 'staff')) {
    console.log('REFRESH TOKEN: Warning - Staff user has no permissions, creating empty structure');

    // Create an empty permissions structure for staff users
    validatedPermissions = {
      pages: {
        menu: false,
        orders: false,
        finance: false,
        inventory: false,
        staff: false,
        settings: false,
        suppliers: false
      }
    };

    console.log('REFRESH TOKEN: Created empty permissions structure for staff user:', JSON.stringify(validatedPermissions));
  }

  const payload: JwtPayload = {
    sub: `refresh_${user.id}`,
    name: user.name,
    email: user.email,
    role: user.role,
    restaurantId: user.restaurantId,
    permissions: validatedPermissions, // Include validated permissions
    metadata: user.metadata,           // Include metadata
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
  };

  return jwt.sign(payload, secret);
}

/**
 * Verify a JWT token
 */
export function verifyToken(token: string): JwtPayload | null {
  try {
    // Check if we're in Edge Runtime (Node.js crypto not available)
    if (typeof process === 'undefined' || process.env?.NEXT_RUNTIME === 'edge') {
      // Use jwtDecode for Edge Runtime
      const decoded = jwtDecode<JwtPayload>(token);

      // Basic validation (check expiry)
      const currentTime = Math.floor(Date.now() / 1000);
      if (decoded.exp && decoded.exp < currentTime) {
        console.log('Token expired in Edge runtime');
        return null;
      }

      return decoded;
    }

    // Use JWT_SECRET or fall back to NEXTAUTH_SECRET
    const secret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET;

    if (!secret) {
      throw new Error('JWT_SECRET or NEXTAUTH_SECRET is not defined');
    }

    return jwt.verify(token, secret) as JwtPayload;
  } catch (error) {
    console.error('Error verifying token:', error);
    return null;
  }
}

/**
 * Generate a password hash
 */
export async function hashPassword(password: string): Promise<string> {
  return hash(password, 12);
}

/**
 * Verify a password against a hash
 */
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return compare(password, hashedPassword);
}