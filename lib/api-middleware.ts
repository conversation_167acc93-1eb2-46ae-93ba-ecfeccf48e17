import { NextRequest } from 'next/server';
import { verifyToken } from './auth/new-auth-service';
import { jwtDecode } from 'jwt-decode';

export interface AuthResult {
  success: boolean;
  user?: {
    id: string;
    name: string;
    email?: string;
    role: string;
    restaurantId: string;
  };
  error?: string;
}

/**
 * Verify JWT authentication from request headers or cookies
 */
export async function verifyJwtAuth(req: NextRequest): Promise<AuthResult> {
  try {
    // Get token from Authorization header or cookies
    const authHeader = req.headers.get('Authorization');
    const token = authHeader ? 
      authHeader.replace('Bearer ', '') : 
      req.cookies.get('auth_token')?.value;
    
    if (!token) {
      return { 
        success: false, 
        error: 'Authentication required' 
      };
    }
    
    // Verify token
    const payload = verifyToken(token);
    
    if (!payload) {
      return { 
        success: false, 
        error: 'Invalid authentication token' 
      };
    }
    
    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (payload.exp < currentTime) {
      return { 
        success: false, 
        error: 'Authentication token expired' 
      };
    }
    
    // Return user info from token
    return {
      success: true,
      user: {
        id: payload.sub,
        name: payload.name,
        email: payload.email,
        role: payload.role,
        restaurantId: payload.restaurantId
      }
    };
  } catch (error) {
    console.error('Error verifying JWT:', error);
    return { 
      success: false, 
      error: 'Authentication error' 
    };
  }
} 