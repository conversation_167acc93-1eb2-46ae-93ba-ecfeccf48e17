/**
 * Database utility functions for working with PouchDB
 * 
 * This replaces the previous CouchDB utilities which are now deprecated.
 */

/**
 * Clean a restaurant ID by removing any prefix
 * 
 * @param id The restaurant ID to clean
 * @returns The cleaned restaurant ID
 */
export function cleanRestaurantId(id: string): string {
  if (!id) return '';
  
  // Handle multiple potential prefix patterns
  // 1. Remove 'restaurant-' or 'restaurant_' prefix (including multiple occurrences)
  // 2. Remove 'restaurant:' prefix that might be used in some IDs
  // 3. Remove 'resto-' or 'resto_' prefix if present
  // 4. Generally clean any non-alphanumeric except dashes and underscores
  
  // First handle the restaurant prefix pattern in various forms
  let cleanedId = id.replace(/^(restaurant[-_:])+/, '');
  
  // Also handle resto prefix
  cleanedId = cleanedId.replace(/^(resto[-_:])+/, '');
  
  // Also handle the case where "restaurant" or "resto" might appear multiple times
  while (cleanedId.match(/^(restaurant|resto)[-_:]/)) {
    cleanedId = cleanedId.replace(/^(restaurant|resto)[-_:]/, '');
  }
  
  // Ensure ID is valid for database use
  cleanedId = formatDocId(cleanedId);
  
  console.log(`[cleanRestaurantId] Cleaned '${id}' to '${cleanedId}'`);
  return cleanedId;
}

/**
 * Get the database name for a restaurant
 * 
 * @param restaurantId The restaurant ID
 * @returns The database name with 'resto-' prefix
 */
export function getRestaurantDbName(restaurantId: string): string {
  const cleanedId = cleanRestaurantId(restaurantId);
  return `resto-${cleanedId}`;
}

/**
 * Format a document ID to be safe for PouchDB
 * 
 * @param id The document ID to format
 * @returns The formatted document ID
 */
export function formatDocId(id: string): string {
  if (!id) return '';
  // Remove any special characters that could cause issues in PouchDB
  return id.replace(/[^a-zA-Z0-9_-]/g, '');
}

// Add other common database-related utility functions here if needed. 