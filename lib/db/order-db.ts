"use client";

import { v4 as uuidv4 } from 'uuid';
import { 
  createOrder,
  getOrder,
  getAllOrders,
  getOrdersByStatus,
  updateOrder,
  deleteOrder
} from './v4/operations/order-ops';

// Types
export interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  quantity: number;
  price: number;
  size?: string;
  notes?: string;
  addons: {
    id: string;
    name: string;
    price: number;
  }[];
}

export interface Customer {
  name: string;
  phone: string;
  address?: string;
}

export interface OrderDocument {
  _id: string;
  _rev?: string;
  tableId: string;
  status: 'pending' | 'completed' | 'cancelled' | 'preparing' | 'served';
  type?: 'dine-in' | 'takeaway' | 'delivery' | 'table' | 'takeout';
  createdAt: string;
  updatedAt: string;
  items: OrderItem[];
  total: number;
  notes?: string;
  customer?: Customer;
  deliveryPerson?: {
    name: string;
    phone: string;
  };
}

// --- TYPE MAPPERS BETWEEN LOCAL AND V4 ---
import type { OrderDocument as V4OrderDocument } from './v4/schemas/order-schema';

function toLocalOrderDocument(v4: V4OrderDocument): OrderDocument {
  return {
    _id: v4._id,
    _rev: v4._rev,
    tableId: v4.tableId,
    status: v4.status,
    type: v4.orderType, // map v4 orderType to local type
    createdAt: v4.createdAt,
    updatedAt: v4.updatedAt,
    items: v4.items.map(item => ({
      ...item,
      addons: item.addons ?? [], // always provide array for local type
    })),
    total: v4.total,
    notes: v4.notes,
    customer: v4.customer,
    deliveryPerson: v4.deliveryPerson,
  };
}

function toV4OrderDocument(local: Omit<OrderDocument, '_id' | '_rev'>): Omit<V4OrderDocument, '_id' | '_rev'> {
  return {
    tableId: local.tableId,
    status: local.status,
    orderType: local.type || 'dine-in', // default if missing
    createdAt: local.createdAt,
    updatedAt: local.updatedAt,
    items: local.items,
    total: local.total,
    notes: local.notes,
    customer: local.customer,
    deliveryPerson: local.deliveryPerson,
    type: 'order_document',
    schemaVersion: 'v4.0',
  };
}

// 🧠 v4 direct implementation: All logic below uses v4 order-ops directly, no v3/restaurantDb or adapters remain. All methods are exported as standalone functions, not as a class. All types are preserved.

// --- CREATE ORDER ---
export async function createOrderV4(order: Omit<OrderDocument, '_id' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt'>): Promise<OrderDocument> {
  // 🧠 v4 expects no _id, type, schemaVersion, createdAt, updatedAt in input
  const v4Order = await createOrder(order as any); // v4 expects its own type, but we map below
  return toLocalOrderDocument(v4Order);
}

// --- GET ALL ORDERS ---
export async function getAllOrdersV4(): Promise<OrderDocument[]> {
  const v4Orders = await getAllOrders();
  return v4Orders.map(toLocalOrderDocument);
}

// --- GET ORDER BY ID ---
export async function getOrderByIdV4(orderId: string): Promise<OrderDocument | null> {
  try {
    const v4Order = await getOrder(orderId);
    return toLocalOrderDocument(v4Order);
  } catch (err: any) {
    if (err?.message?.includes('not found')) return null;
    throw err;
  }
}

// --- GET ORDERS BY STATUS ---
export async function getOrdersByStatusV4(status: OrderDocument['status']): Promise<OrderDocument[]> {
  const v4Orders = await getOrdersByStatus(status);
  return v4Orders.map(toLocalOrderDocument);
}

// --- UPDATE ORDER ---
export async function updateOrderV4(orderId: string, updates: Partial<OrderDocument>): Promise<OrderDocument> {
  const v4Order = await updateOrder(orderId, updates as any);
  return toLocalOrderDocument(v4Order);
}

// --- UPDATE ORDER STATUS ---
export async function updateOrderStatusV4(orderId: string, status: OrderDocument['status']): Promise<OrderDocument> {
  const v4Order = await updateOrder(orderId, { status, updatedAt: new Date().toISOString() } as any);
  return toLocalOrderDocument(v4Order);
}

// --- DELETE ORDER ---
export async function deleteOrderV4(orderId: string): Promise<void> {
  await deleteOrder(orderId);
}

// --- CALCULATE TOTAL (utility, still useful) ---
export function calculateOrderTotalV4(items: OrderItem[]): number {
    return items.reduce((total, item) => {
      const itemTotal = item.price * item.quantity;
      const addonTotal = item.addons ? item.addons.reduce((sum, addon) => sum + addon.price, 0) * item.quantity : 0;
      return total + itemTotal + addonTotal;
    }, 0);
  }

// --- EXPORTS FOR COMPATIBILITY ---
export const orderDbV4 = {
  createOrder: createOrderV4,
  getAllOrders: getAllOrdersV4,
  getOrderById: getOrderByIdV4,
  getOrdersByStatus: getOrdersByStatusV4,
  updateOrder: updateOrderV4,
  updateOrderStatus: updateOrderStatusV4,
  deleteOrder: deleteOrderV4,
  calculateTotal: calculateOrderTotalV4,
};