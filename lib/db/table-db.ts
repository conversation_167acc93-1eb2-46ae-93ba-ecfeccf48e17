"use client";

import {
  getAllTables,
  getTable,
  createTable,
  updateTable,
  deleteTable,
  updateTableStatus
} from './v4';
import type { Table, TableDocument } from './v4';
import { v4 as uuidv4 } from 'uuid';

// Types
export interface TableLayout {
  id: string;
  name: string;
  seats: number;
  position: { x: number; y: number };
  status: 'free' | 'occupied';
}

// knowledge: removed local TableDocument interface, now using v4 type
// export interface TableDocument {
//   _id: string;
//   _rev?: string;
//   layout: TableLayout[];
// }
// knowledge: end TableDocument removal

/**
 * TableDB class - simplified wrapper around v4 table operations
 */
export class TableDB {
  constructor() {
    // No initialization needed - using v4 table operations
  }

  // Get all tables
  async getAllTables(): Promise<Table[]> {
    return getAllTables();
  }

  // Get table layout
  async getLayout(): Promise<Table[]> {
    const tables = await getAllTables();
    return tables;
  }

  // Add a table
  async addTable(table: Omit<Table, 'id'>): Promise<Table> {
    return createTable(table);
  }

  // Update a table
  async updateTable(tableId: string, updates: Partial<Table>): Promise<Table> {
    return updateTable(tableId, updates);
  }

  // Delete a table
  async deleteTable(tableId: string): Promise<void> {
    await deleteTable(tableId);
  }

  // Update table status
  async updateTableStatus(tableId: string, status: 'free' | 'occupied'): Promise<Table> {
    return updateTableStatus(tableId, status);
  }
}

// Singleton instance
export const tableDb = new TableDB(); 