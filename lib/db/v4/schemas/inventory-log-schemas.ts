/**
 * 🚀 Enhancement 3: Separated Inventory Log Schemas
 * 
 * Breaking out inventory logs into separate documents to:
 * - Reduce P2P sync conflicts (no more monolithic inventory doc)
 * - Enable efficient querying with proper indexes
 * - Improve replication performance (only new logs replicate)
 * - Better offline convergence
 */

// Base interface for all inventory log documents
export interface BaseInventoryLogDocument {
  _id: string;
  _rev?: string;
  type: string;
  schemaVersion: 'v4.0';
  createdAt: string;
  updatedAt: string;
  stockItemId: string;
  performedBy?: string;
  notes?: string;
}

// Purchase Log Document
export interface PurchaseLogDocument extends BaseInventoryLogDocument {
  type: 'purchase_log';
  supplierId?: string;
  supplierName?: string;
  quantity: number;
  costPerUnit: number;
  totalCost: number;
  invoiceNumber?: string;
  expiryDate?: string;
  batchNumber?: string;
  receiptImage?: string; // R2 file key for receipt image
  receiptImageLocal?: string; // Local file path for Electron offline access
}

// Waste Log Document  
export interface WasteLogDocument extends BaseInventoryLogDocument {
  type: 'waste_log';
  quantity: number;
  reason: 'expired' | 'damaged' | 'spoiled' | 'other';
  costPerUnit: number;
  totalCost: number;
}

// Consumption Log Document
export interface ConsumptionLogDocument extends BaseInventoryLogDocument {
  type: 'consumption_log';
  orderId: string;
  menuItemId: string;
  menuItemName: string;
  quantity: number;
  costPerUnit: number;
  totalCost: number;
  orderDate: string;
}

// Stock Adjustment Log Document
export interface StockAdjustmentLogDocument extends BaseInventoryLogDocument {
  type: 'stock_adjustment_log';
  adjustmentType: 'increase' | 'decrease' | 'correction';
  quantity: number;
  previousQuantity: number;
  newQuantity: number;
  reason: string;
  costPerUnit?: number;
}

// Stock Count Log Document
export interface StockCountLogDocument extends BaseInventoryLogDocument {
  type: 'stock_count_log';
  countedQuantity: number;
  systemQuantity: number;
  variance: number;
  varianceValue: number;
  countDate: string;
  countedBy: string;
}

// Default documents for creation
export const DEFAULT_PURCHASE_LOG: Omit<PurchaseLogDocument, '_id' | 'createdAt' | 'updatedAt'> = {
  type: 'purchase_log',
  schemaVersion: 'v4.0',
  stockItemId: '',
  quantity: 0,
  costPerUnit: 0,
  totalCost: 0,
};

export const DEFAULT_WASTE_LOG: Omit<WasteLogDocument, '_id' | 'createdAt' | 'updatedAt'> = {
  type: 'waste_log',
  schemaVersion: 'v4.0',
  stockItemId: '',
  quantity: 0,
  reason: 'other',
  costPerUnit: 0,
  totalCost: 0,
};

export const DEFAULT_CONSUMPTION_LOG: Omit<ConsumptionLogDocument, '_id' | 'createdAt' | 'updatedAt'> = {
  type: 'consumption_log',
  schemaVersion: 'v4.0',
  stockItemId: '',
  orderId: '',
  menuItemId: '',
  menuItemName: '',
  quantity: 0,
  costPerUnit: 0,
  totalCost: 0,
  orderDate: '',
};

export const DEFAULT_STOCK_ADJUSTMENT_LOG: Omit<StockAdjustmentLogDocument, '_id' | 'createdAt' | 'updatedAt'> = {
  type: 'stock_adjustment_log',
  schemaVersion: 'v4.0',
  stockItemId: '',
  adjustmentType: 'correction',
  quantity: 0,
  previousQuantity: 0,
  newQuantity: 0,
  reason: '',
};

export const DEFAULT_STOCK_COUNT_LOG: Omit<StockCountLogDocument, '_id' | 'createdAt' | 'updatedAt'> = {
  type: 'stock_count_log',
  schemaVersion: 'v4.0',
  stockItemId: '',
  countedQuantity: 0,
  systemQuantity: 0,
  variance: 0,
  varianceValue: 0,
  countDate: '',
  countedBy: '',
};

// Union type for all inventory log documents
export type InventoryLogDocument = 
  | PurchaseLogDocument 
  | WasteLogDocument 
  | ConsumptionLogDocument 
  | StockAdjustmentLogDocument 
  | StockCountLogDocument;

// Helper function to generate log IDs
export function generateLogId(logType: string): string {
  return `${logType}:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
} 