"use client";

// Purchase Unit Schema
export const purchaseUnitSchema = {
  type: 'object',
  required: ['id', 'name', 'conversionToBase', 'createdAt', 'updatedAt'],
  properties: {
    id: { type: 'string' },
    name: { type: 'string' },
    conversionToBase: { type: 'number' },
    isDefault: { type: 'boolean' },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' }
  }
};

// Inventory Document Schema
export const inventoryDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'schemaVersion', 'createdAt', 'updatedAt', 'items'],
  properties: {
    _id: { type: 'string', enum: ['inventory'] },
    _rev: { 
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: { type: 'string', enum: ['inventory_document'] },
    schemaVersion: { type: 'string', enum: ['v4.0'] },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
    items: {
      type: 'array',
      items: {
        type: 'object',
        required: ['id', 'name', 'category', 'unit', 'createdAt', 'updatedAt'],
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          category: { type: 'string' },
          unit: { type: 'string' },
          supplierId: { type: 'string' },
          quantity: { type: 'number' },
          minLevel: { type: 'number' },
          costPerUnit: { type: 'number' },
          expiryDate: { type: 'string' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' },
          purchaseUnits: {
            type: 'array',
            items: purchaseUnitSchema
          }
        }
      }
    }
  }
};

export interface PurchaseUnit {
  id: string;
  name: string; // e.g., "Tray", "Case", "Box"
  conversionToBase: number; // e.g., 1 tray = 30 pcs
  isDefault?: boolean; // Mark the default purchase unit
  createdAt: string;
  updatedAt: string;
}

export interface InventoryDocument {
  _id: string;
  _rev?: string; // PouchDB revision field - optional for new docs, required for updates
  type: 'inventory_document';
  schemaVersion: 'v4.0';
  createdAt: string;
  updatedAt: string;
  items: StockItem[];
  adjustments?: StockAdjustment[];
  purchases?: PurchaseLog[];
  stockCounts?: StockCount[];
  stockCountItems?: StockCountItem[];
  wasteLogs?: WasteLog[];
  consumptionLogs?: ConsumptionLog[];
}

export interface StockItem {
  id: string;
  name: string;
  category: string;
  unit: 'kg' | 'L' | 'pcs' | 'g' | 'ml'; // This becomes the base unit
  supplierId?: string;
  quantity?: number;
  minLevel?: number;
  costPerUnit?: number;
  expiryDate?: string;
  createdAt: string;
  updatedAt: string;
  // New UOM fields
  purchaseUnits?: PurchaseUnit[];
}

export interface StockAdjustment {
  id: string;
  stockItemId: string;
  adjustmentType: 'addition' | 'reduction';
  quantity: number;
  date: string;
  reason?: string;
  performedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface PurchaseLog {
  id: string;
  stockItemId: string;
  date: string;
  quantity: number;
  unit: 'kg' | 'L' | 'pcs' | 'g' | 'ml'; // This is now the purchase unit
  purchaseUnitId?: string; // Reference to the purchase unit used
  baseQuantity: number; // Converted quantity in base unit
  costPerUnit?: number; // Cost per purchase unit
  costPerBaseUnit?: number; // Cost per base unit (calculated)
  totalCost: number;
  supplierId?: string;
  notes?: string;
  receiptImage?: string; // R2 file key for receipt image
  receiptImageLocal?: string; // Local file path for Electron offline access
  performedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface StockCount {
  id: string;
  name: string;
  date: string;
  status: 'draft' | 'in_progress' | 'completed';
  countType: 'full' | 'partial' | 'cycle';
  countArea: 'all' | 'kitchen' | 'bar' | 'storage';
  notes?: string;
  performedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface StockCountItem {
  id: string;
  stockCountId: string;
  stockItemId: string;
  theoreticalQuantity: number;
  countedQuantity?: number;
  variance?: number;
  varianceValue?: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface WasteLog {
  id: string;
  stockItemId: string;
  quantity: number;
  reason: 'expired' | 'damaged' | 'spilled' | 'contaminated' | 'cooking_error' | 'returned' | 'other';
  notes?: string;
  date: string;
  performedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface ConsumptionLog {
  id: string;
  orderId: string;
  stockItemId: string;
  quantity: number;
  costPerUnit: number;
  totalCost: number;
  menuItemId: string;
  menuItemName: string;
  date: string;
  createdAt: string;
  updatedAt: string;
}

export const DEFAULT_INVENTORY_DOCUMENT: InventoryDocument = {
  _id: 'inventory',
  type: 'inventory_document',
  schemaVersion: 'v4.0',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  items: [],
  adjustments: [],
  purchases: [],
  stockCounts: [],
  stockCountItems: [],
  wasteLogs: [],
  consumptionLogs: []
}; 