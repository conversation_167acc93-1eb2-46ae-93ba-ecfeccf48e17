"use client";

/**
 * Cash Collection Schema for Staff Driver Collections
 * 
 * This file defines the schema for tracking cash collections from staff drivers
 * including expected amounts, actual collected amounts, and discrepancy tracking.
 */

// Cash Collection Document Schema
export const cashCollectionDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'staffId', 'staffName', 'expectedAmount', 'collectedAt', 'collectedBy'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^cash_collection:[0-9]+$'
    },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: {
      type: 'string',
      enum: ['cash_collection']
    },
    staffId: {
      type: 'string',
      description: 'ID of the staff driver'
    },
    staffName: {
      type: 'string',
      description: 'Name of the staff driver'
    },
    expectedAmount: {
      type: 'number',
      description: 'Expected cash amount based on deliveries'
    },
    actualAmount: {
      type: 'number',
      description: 'Actual cash amount collected'
    },
    discrepancy: {
      type: 'number',
      description: 'Difference between expected and actual (actualAmount - expectedAmount)'
    },
    discrepancyReason: {
      type: 'string',
      description: 'Reason for discrepancy if any'
    },
    orderIds: {
      type: 'array',
      items: {
        type: 'string'
      },
      description: 'List of order IDs included in this collection'
    },
    collectedAt: {
      type: 'string',
      format: 'date-time',
      description: 'When the cash was collected'
    },
    collectedBy: {
      type: 'string',
      description: 'Manager/staff who collected the cash'
    },
    notes: {
      type: 'string',
      description: 'Additional notes about the collection'
    },
    status: {
      type: 'string',
      enum: ['pending', 'collected', 'discrepancy_resolved'],
      description: 'Status of the cash collection'
    }
  }
};

// Cash Collection Interface
export interface CashCollectionDocument {
  _id: string;
  _rev?: string;
  type: 'cash_collection';
  staffId: string;
  staffName: string;
  expectedAmount: number;
  actualAmount?: number;
  discrepancy?: number;
  discrepancyReason?: string;
  orderIds: string[];
  collectedAt: string;
  collectedBy: string;
  notes?: string;
  status: 'pending' | 'collected' | 'discrepancy_resolved';
}

// Pending Collection Summary Interface
export interface PendingCollection {
  staffId: string;
  staffName: string;
  totalExpected: number;
  orderCount: number;
  orderIds: string[];
  oldestOrderDate: string;
} 