"use client";

/**
 * V4 Restaurant ID Utilities
 *
 * Provides utilities for managing restaurant IDs in the v4 database.
 */

/**
 * Get the current restaurant ID from the authenticated user's session
 * This is the ONLY source of truth for the restaurant ID
 */
export function getCurrentRestaurantId(): string | null {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    // Get restaurant ID from auth_data - this is the ONLY source of truth
    const authData = localStorage.getItem('auth_data');
    if (authData) {
      try {
        const parsedData = JSON.parse(authData);
        if (parsedData.restaurantId) {
          console.log(`V4 Utils - Found restaurant ID in auth session: ${parsedData.restaurantId}`);
          return parsedData.restaurantId;
        }
      } catch (parseError) {
        console.error('Error parsing auth_data:', parseError);
      }
    }

    console.warn('V4 Utils - No restaurant ID found in auth session');
    return null;
  } catch (error) {
    console.error('Error getting current restaurant ID from auth session:', error);
    return null;
  }
}

/**
 * Force a specific restaurant ID to be used
 * This should only be used in special circumstances when the ID needs to be set programmatically
 * 
 * @param restaurantId The restaurant ID to force
 */
export function forceRestaurantId(restaurantId: string): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Get existing auth data
    const authData = localStorage.getItem('auth_data');
    if (authData) {
      try {
        const parsedData = JSON.parse(authData);
        // Update the restaurant ID
        parsedData.restaurantId = restaurantId;
        // Save back to localStorage
        localStorage.setItem('auth_data', JSON.stringify(parsedData));
        console.log(`V4 Utils - Forced restaurant ID: ${restaurantId}`);
      } catch (parseError) {
        console.error('Error parsing auth_data for force update:', parseError);
      }
    } else {
      // Create minimal auth data with just the restaurant ID
      const minimalAuthData = { restaurantId };
      localStorage.setItem('auth_data', JSON.stringify(minimalAuthData));
      console.log(`V4 Utils - Created new auth data with forced restaurant ID: ${restaurantId}`);
    }
  } catch (error) {
    console.error('Error forcing restaurant ID:', error);
  }
}


