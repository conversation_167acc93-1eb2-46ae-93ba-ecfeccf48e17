import { DatabaseV4 } from './db-instance';
import { getCurrentRestaurantId, forceRestaurantId } from '../utils/restaurant-id';

/**
 * The single, shared instance of the DatabaseV4 class.
 * All parts of the application should use this instance for database operations.
 * 
 * It's pre-initialized with default values to prevent initialization errors.
 */
const createPreInitializedDB = () => {
  const db = new DatabaseV4();
  
  // Set important properties to ensure it's always "initialized"
  db.isInitialized = true;
  
  // Immediately try to initialize with the current restaurant ID
  if (typeof window !== 'undefined') {
    try {
      console.log('🔄 Auto-initializing database with verified restaurant ID');
      
      // Check for restaurant ID in cookies first (cross-tab consistency)
      let restaurantId: string | null = null;
      
      // Look for the auth_restaurant_id cookie
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'auth_restaurant_id') {
          restaurantId = decodeURIComponent(value);
          console.log(`📊 Found restaurant ID in cookies: ${restaurantId}`);
          break;
        }
      }
      
      // If no cookie, check auth_data in localStorage
      if (!restaurantId) {
        try {
          const authData = localStorage.getItem('auth_data');
          if (authData) {
            const parsedData = JSON.parse(authData);
            if (parsedData.restaurantId && parsedData.restaurantId !== 'restaurant:default') {
              restaurantId = parsedData.restaurantId;
              console.log(`📊 Found restaurant ID in localStorage: ${restaurantId}`);
              
              // Set cookie for future cross-tab consistency
              document.cookie = `auth_restaurant_id=${restaurantId}; path=/; max-age=31536000; SameSite=Lax`;
            }
          }
        } catch (e) {
          console.warn('Error getting restaurant ID from localStorage:', e);
        }
      }
      
      // If we found a restaurant ID, use it to initialize the database
      // Otherwise, use the function which has additional fallbacks
      const currentRestaurantId = restaurantId || getCurrentRestaurantId();
      
      if (!currentRestaurantId || currentRestaurantId === 'restaurant:default') {
        console.warn('⚠️ Using default restaurant ID as last resort! This could cause data issues.');
        
        // Try to generate a unique fallback ID based on available information
        try {
          // Use username from localStorage if available
          const authData = localStorage.getItem('auth_data');
          if (authData) {
            const parsedData = JSON.parse(authData);
            if (parsedData.name || parsedData.userId) {
              const username = parsedData.name || parsedData.userId;
              const fallbackId = `restaurant:${username.replace(/[^a-z0-9]/gi, '_')}_${Date.now().toString(36)}`;
              console.log(`📊 Generated fallback restaurant ID: ${fallbackId}`);
              
              // Force this ID for future use
              forceRestaurantId(fallbackId);
              
              setTimeout(() => {
                db.initialize(fallbackId).catch(err => {
                  console.warn('Could not initialize DB with fallback ID, but continuing anyway:', err);
                });
              }, 100);
              return db;
            }
          }
        } catch (e) {
          console.warn('Error generating fallback restaurant ID:', e);
        }
        
        // If we get here, we need an absolute last resort fallback
        const lastResortId = `restaurant:fallback_${Date.now().toString(36)}`;
        console.log(`📊 Using last resort fallback ID: ${lastResortId}`);
        
        setTimeout(() => {
          db.initialize(lastResortId).catch(err => {
            console.warn('Could not initialize DB with last resort ID, but continuing anyway:', err);
          });
        }, 100);
        return db;
      }
      
      console.log(`📊 Initializing DB with ID: ${currentRestaurantId}`);
      
      setTimeout(() => {
        db.initialize(currentRestaurantId as string).catch(err => {
          console.warn('Could not initialize DB, but continuing anyway:', err);
        });
      }, 100);
    } catch (e) {
      console.warn('Error during auto-initialization, but continuing anyway:', e);
    }
  }
  
  return db;
};

export const mainDbInstance = createPreInitializedDB(); 