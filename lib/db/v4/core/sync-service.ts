// 🔄 Simple Sync Service for V4 Database
import { databaseV4 } from './db-instance';

export type SyncStatus = 'disconnected' | 'connecting' | 'connected' | 'error' | 'synced';

export interface SyncState {
  status: SyncStatus;
  lastSync: Date | null;
  error: Error | null;
  isElectron: boolean;
}

class SyncServiceV4 {
  private status: SyncStatus = 'disconnected';
  private lastSync: Date | null = null;
  private error: Error | null = null;
  private isElectron: boolean = false;

  constructor() {
    this.isElectron = typeof window !== 'undefined' && 
      !!(window as any).IS_DESKTOP_APP;

    if (typeof window !== 'undefined') {
      window.addEventListener('v4-pouchdb-initialized', this.handleDbInitialized);
    }
  }

  private handleDbInitialized = async () => {
    if (databaseV4.isInitialized) {
      this.status = 'connected';
      this.lastSync = new Date();
      this.error = null;

      // In Electron, sync is automatically managed by the main process
      if (this.isElectron) {
        console.log('[SyncServiceV4] 🔄 Database initialized - sync automatically managed by Electron');
        
        // Try to start sync supervision
        try {
          const result = await databaseV4.startLocalSync();
          if (result.success) {
            this.status = 'synced';
            console.log('[SyncServiceV4] ✅ Local sync started successfully');
          } else {
            console.warn('[SyncServiceV4] ⚠️ Local sync failed to start:', result.message);
          }
        } catch (error: any) {
          console.error('[SyncServiceV4] ❌ Error starting local sync:', error);
          this.error = error;
          this.status = 'error';
        }
      }
    }
  };

  getSyncState(): SyncState {
    return {
      status: this.status,
      lastSync: this.lastSync,
      error: this.error,
      isElectron: this.isElectron
    };
  }

  async refreshSync(): Promise<void> {
    this.status = databaseV4.isInitialized ? 'connected' : 'disconnected';
    this.lastSync = new Date();
    
    if (this.isElectron) {
      try {
        const syncStatus = await databaseV4.getLocalSyncStatus();
        if (syncStatus.success && syncStatus.data?.length > 0) {
          this.status = 'synced';
        }
      } catch (error: any) {
        this.error = error;
        this.status = 'error';
      }
    }
  }

  async forceSyncOnce(): Promise<{ success: boolean; message: string }> {
    if (!this.isElectron) {
      return { 
        success: true, 
        message: 'Web environment - sync not applicable' 
      };
    }

    try {
      // In the new system, we just restart the sync supervision
      const result = await databaseV4.startLocalSync();
      
      if (result.success) {
        this.status = 'synced';
        this.lastSync = new Date();
        this.error = null;
      } else {
        this.error = new Error(result.message);
        this.status = 'error';
      }
      
      return result;
    } catch (error: any) {
      this.error = error;
      this.status = 'error';
      return { 
        success: false, 
        message: `Sync error: ${error.message}` 
      };
    }
  }

  async testAndRepairConnection(): Promise<{ success: boolean; message: string }> {
    if (!this.isElectron) {
      return { 
        success: true, 
        message: 'Web environment - no connection to repair' 
      };
    }

    return this.forceSyncOnce();
  }

  // Legacy compatibility methods (simplified)
  async forceFullPush(): Promise<{ success: boolean; message: string }> {
    return this.forceSyncOnce();
  }

  async checkPendingChanges(): Promise<number> {
    return 0; // Not implemented - main process handles this
  }

  async fixCorsIssues(): Promise<{ success: boolean; message: string }> {
    return { success: true, message: 'CORS not applicable for local sync' };
  }

  async getLocalSyncStatus(): Promise<{ active: boolean; details: any }> {
    if (!this.isElectron) {
      return { active: false, details: [] };
    }

    try {
      const result = await databaseV4.getLocalSyncStatus();
      return {
        active: result.success && result.data?.length > 0,
        details: result.data || []
      };
    } catch (error) {
      console.error('[SyncServiceV4] Error getting sync status:', error);
      return { active: false, details: [] };
    }
  }
}

export const syncServiceV4 = new SyncServiceV4();

// Export default for convenience
export default syncServiceV4; 