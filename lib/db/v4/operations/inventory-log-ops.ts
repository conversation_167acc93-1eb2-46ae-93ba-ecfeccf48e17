/**
 * 🚀 Enhancement 3: Separated Inventory Log Operations
 * 
 * Operations for the new separated inventory log documents.
 * This provides better P2P sync performance and reduces conflicts.
 */

import { v4 as uuidv4 } from 'uuid';
import { databaseV4 } from '../core/db-instance';
import {
  PurchaseLogDocument,
  WasteLogDocument,
  ConsumptionLogDocument,
  StockAdjustmentLogDocument,
  StockCountLogDocument,
  InventoryLogDocument,
  generateLogId,
  DEFAULT_PURCHASE_LOG,
  DEFAULT_WASTE_LOG,
  DEFAULT_CONSUMPTION_LOG,
  DEFAULT_STOCK_ADJUSTMENT_LOG,
  DEFAULT_STOCK_COUNT_LOG
} from '../schemas/inventory-log-schemas';
import { 
  retryWithConflictResolution, 
  safeUpdateDocument 
} from '../core/conflict-resolution';

/**
 * Create indexes for inventory logs
 */
export async function createInventoryLogIndexes(): Promise<void> {
  console.log('[createInventoryLogIndexes] Starting inventory log index creation');
  
  const results: {name: string, success: boolean, error?: any}[] = [];
  
  const createIndexWithRetry = async (fields: string[], name?: string, ddoc?: string) => {
    const indexName = name || `unnamed-${fields.join('-')}`;
    const maxRetries = 3;
    let lastError: any = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[createInventoryLogIndexes] Creating index '${indexName}' (attempt ${attempt}/${maxRetries})`);
        
        const indexOptions: PouchDB.Find.CreateIndexOptions = {
          index: { fields }
        };
        
        if (name && ddoc) {
          indexOptions.index.name = name;
          indexOptions.index.ddoc = ddoc;
        }
        
        await databaseV4.createIndex(indexOptions);
        console.log(`[createInventoryLogIndexes] ✅ Successfully created index '${indexName}'`);
        return { name: indexName, success: true };
      } catch (error: any) {
        lastError = error;
        console.warn(`[createInventoryLogIndexes] ⚠️ Failed to create index '${indexName}' (attempt ${attempt}/${maxRetries}): ${error.message}`);
        
        if (error.status === 409) {
          console.log(`[createInventoryLogIndexes] Index '${indexName}' already exists (409), continuing`);
          return { name: indexName, success: true };
        }
        
        if (attempt < maxRetries) {
          const delay = 500 * attempt;
          console.log(`[createInventoryLogIndexes] Retrying after ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    return { name: indexName, success: false, error: lastError };
  };
  
  try {
    // Inventory log indexes for efficient querying
    const indexes = [
      // Core log indexes
      { fields: ['type', 'stockItemId'], name: 'log-type-stockitem-idx', ddoc: 'log-type-stockitem-idx' },
      { fields: ['type', 'stockItemId', 'createdAt'], name: 'log-type-stockitem-date-idx', ddoc: 'log-type-stockitem-date-idx' },
      { fields: ['type', 'createdAt'], name: 'log-type-date-idx', ddoc: 'log-type-date-idx' },
      { fields: ['type', 'orderId'], name: 'log-type-order-idx', ddoc: 'log-type-order-idx' },
      { fields: ['stockItemId', 'createdAt'], name: 'log-stockitem-date-idx', ddoc: 'log-stockitem-date-idx' },
      
      // Specific log type indexes
      { fields: ['type', 'supplierId'], name: 'purchase-type-supplier-idx', ddoc: 'purchase-type-supplier-idx' },
      { fields: ['type', 'reason'], name: 'waste-type-reason-idx', ddoc: 'waste-type-reason-idx' },
      { fields: ['type', 'menuItemId'], name: 'consumption-type-menuitem-idx', ddoc: 'consumption-type-menuitem-idx' },
    ];
    
    for (const index of indexes) {
      results.push(await createIndexWithRetry(index.fields, index.name, index.ddoc));
    }
    
    const failedIndexes = results.filter(r => !r.success);
    if (failedIndexes.length > 0) {
      console.error(`[createInventoryLogIndexes] ❌ ${failedIndexes.length}/${results.length} inventory log indexes failed to create:`);
      failedIndexes.forEach(r => console.error(`  - ${r.name}: ${r.error?.message || 'Unknown error'}`));
      console.warn(`[createInventoryLogIndexes] Some inventory log indexes failed, but operations will continue.`);
    } else {
      console.log(`[createInventoryLogIndexes] ✅ All ${results.length} inventory log indexes created successfully`);
    }
  } catch (error) {
    console.error('[createInventoryLogIndexes] ❌ Unexpected error during inventory log index creation:', error);
    throw error;
  }
}

// ===== PURCHASE LOG OPERATIONS =====

export async function createPurchaseLog(logData: Omit<PurchaseLogDocument, '_id' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt'>): Promise<PurchaseLogDocument> {
  try {
    const now = new Date().toISOString();
    const logId = generateLogId('purchase_log');

    const log: PurchaseLogDocument = {
      _id: logId,
      type: 'purchase_log',
      schemaVersion: 'v4.0',
      createdAt: now,
      updatedAt: now,
      ...logData
    };

    // Use conflict resolution for log creation
    await retryWithConflictResolution(async () => {
      await databaseV4.putDoc(log);
    });
    
    return await databaseV4.getDoc<PurchaseLogDocument>(logId);
  } catch (error) {
    console.error('[createPurchaseLog] Error creating purchase log:', error);
    throw error;
  }
}

export async function getPurchaseLogsByStockItem(stockItemId: string, limit?: number): Promise<PurchaseLogDocument[]> {
  try {
    const result = await databaseV4.findDocs<PurchaseLogDocument>({
      selector: {
        type: 'purchase_log',
        stockItemId: stockItemId
      },
      sort: [{ type: 'asc' }, { stockItemId: 'asc' }, { createdAt: 'desc' }],
      use_index: ['log-type-stockitem-date-idx', 'log-type-stockitem-date-idx'],
      limit: limit || 100
    });
    return result.docs || [];
  } catch (error) {
    console.error('[getPurchaseLogsByStockItem] Error fetching purchase logs:', error);
    throw error;
  }
}

export async function getPurchaseLogsByDateRange(startDate: string, endDate: string): Promise<PurchaseLogDocument[]> {
  try {
    const result = await databaseV4.findDocs<PurchaseLogDocument>({
      selector: {
        type: 'purchase_log',
        createdAt: {
          $gte: startDate,
          $lte: endDate
        }
      },
      sort: [{ type: 'asc' }, { createdAt: 'desc' }],
      use_index: ['log-type-date-idx', 'log-type-date-idx']
    });
    return result.docs || [];
  } catch (error) {
    console.error('[getPurchaseLogsByDateRange] Error fetching purchase logs by date:', error);
    throw error;
  }
}

// ===== WASTE LOG OPERATIONS =====

export async function createWasteLog(logData: Omit<WasteLogDocument, '_id' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt'>): Promise<WasteLogDocument> {
  try {
    const now = new Date().toISOString();
    const logId = generateLogId('waste_log');

    const log: WasteLogDocument = {
      _id: logId,
      type: 'waste_log',
      schemaVersion: 'v4.0',
      createdAt: now,
      updatedAt: now,
      ...logData
    };

    await databaseV4.putDoc(log);
    return await databaseV4.getDoc<WasteLogDocument>(logId);
  } catch (error) {
    console.error('[createWasteLog] Error creating waste log:', error);
    throw error;
  }
}

export async function getWasteLogsByStockItem(stockItemId: string, limit?: number): Promise<WasteLogDocument[]> {
  try {
    const result = await databaseV4.findDocs<WasteLogDocument>({
      selector: {
        type: 'waste_log',
        stockItemId: stockItemId
      },
      sort: [{ type: 'asc' }, { stockItemId: 'asc' }, { createdAt: 'desc' }],
      use_index: ['log-type-stockitem-date-idx', 'log-type-stockitem-date-idx'],
      limit: limit || 100
    });
    return result.docs || [];
  } catch (error) {
    console.error('[getWasteLogsByStockItem] Error fetching waste logs:', error);
    throw error;
  }
}

export async function getWasteLogsByReason(reason: WasteLogDocument['reason']): Promise<WasteLogDocument[]> {
  try {
    const result = await databaseV4.findDocs<WasteLogDocument>({
      selector: {
        type: 'waste_log',
        reason: reason
      },
      sort: [{ type: 'asc' }, { reason: 'asc' }, { createdAt: 'desc' }],
      use_index: ['waste-type-reason-idx', 'waste-type-reason-idx']
    });
    return result.docs || [];
  } catch (error) {
    console.error('[getWasteLogsByReason] Error fetching waste logs by reason:', error);
    throw error;
  }
}

// ===== CONSUMPTION LOG OPERATIONS =====

export async function createConsumptionLog(logData: Omit<ConsumptionLogDocument, '_id' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt'>): Promise<ConsumptionLogDocument> {
  try {
    const now = new Date().toISOString();
    const logId = generateLogId('consumption_log');

    const log: ConsumptionLogDocument = {
      _id: logId,
      type: 'consumption_log',
      schemaVersion: 'v4.0',
      createdAt: now,
      updatedAt: now,
      ...logData
    };

    // knowledge: Use putDoc response to return document without extra get call
    const putResult = await databaseV4.putDoc(log);
    return { ...log, _rev: putResult.rev } as ConsumptionLogDocument;
  } catch (error) {
    console.error('[createConsumptionLog] Error creating consumption log:', error);
    throw error;
  }
}

export async function getConsumptionLogsByOrder(orderId: string): Promise<ConsumptionLogDocument[]> {
  try {
    const result = await databaseV4.findDocs<ConsumptionLogDocument>({
      selector: {
        type: 'consumption_log',
        orderId: orderId
      },
      sort: [{ type: 'asc' }, { orderId: 'asc' }],
      use_index: ['log-type-order-idx', 'log-type-order-idx']
    });
    return result.docs || [];
  } catch (error) {
    console.error('[getConsumptionLogsByOrder] Error fetching consumption logs by order:', error);
    throw error;
  }
}

export async function getConsumptionLogsByStockItem(stockItemId: string, limit?: number): Promise<ConsumptionLogDocument[]> {
  try {
    const result = await databaseV4.findDocs<ConsumptionLogDocument>({
      selector: {
        type: 'consumption_log',
        stockItemId: stockItemId
      },
      sort: [{ type: 'asc' }, { stockItemId: 'asc' }, { createdAt: 'desc' }],
      use_index: ['log-type-stockitem-date-idx', 'log-type-stockitem-date-idx'],
      limit: limit || 100
    });
    return result.docs || [];
  } catch (error) {
    console.error('[getConsumptionLogsByStockItem] Error fetching consumption logs:', error);
    throw error;
  }
}

export async function getConsumptionLogsByMenuItem(menuItemId: string): Promise<ConsumptionLogDocument[]> {
  try {
    const result = await databaseV4.findDocs<ConsumptionLogDocument>({
      selector: {
        type: 'consumption_log',
        menuItemId: menuItemId
      },
      sort: [{ type: 'asc' }, { menuItemId: 'asc' }, { createdAt: 'desc' }],
      use_index: ['consumption-type-menuitem-idx', 'consumption-type-menuitem-idx']
    });
    return result.docs || [];
  } catch (error) {
    console.error('[getConsumptionLogsByMenuItem] Error fetching consumption logs by menu item:', error);
    throw error;
  }
}

// ===== STOCK ADJUSTMENT LOG OPERATIONS =====

export async function createStockAdjustmentLog(logData: Omit<StockAdjustmentLogDocument, '_id' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt'>): Promise<StockAdjustmentLogDocument> {
  try {
    const now = new Date().toISOString();
    const logId = generateLogId('stock_adjustment_log');

    const log: StockAdjustmentLogDocument = {
      _id: logId,
      type: 'stock_adjustment_log',
      schemaVersion: 'v4.0',
      createdAt: now,
      updatedAt: now,
      ...logData
    };

    await databaseV4.putDoc(log);
    return await databaseV4.getDoc<StockAdjustmentLogDocument>(logId);
  } catch (error) {
    console.error('[createStockAdjustmentLog] Error creating stock adjustment log:', error);
    throw error;
  }
}

export async function getStockAdjustmentLogsByStockItem(stockItemId: string, limit?: number): Promise<StockAdjustmentLogDocument[]> {
  try {
    const result = await databaseV4.findDocs<StockAdjustmentLogDocument>({
      selector: {
        type: 'stock_adjustment_log',
        stockItemId: stockItemId
      },
      sort: [{ type: 'asc' }, { stockItemId: 'asc' }, { createdAt: 'desc' }],
      use_index: ['log-type-stockitem-date-idx', 'log-type-stockitem-date-idx'],
      limit: limit || 100
    });
    return result.docs || [];
  } catch (error) {
    console.error('[getStockAdjustmentLogsByStockItem] Error fetching stock adjustment logs:', error);
    throw error;
  }
}

// ===== STOCK COUNT LOG OPERATIONS =====

export async function createStockCountLog(logData: Omit<StockCountLogDocument, '_id' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt'>): Promise<StockCountLogDocument> {
  try {
    const now = new Date().toISOString();
    const logId = generateLogId('stock_count_log');

    const log: StockCountLogDocument = {
      _id: logId,
      type: 'stock_count_log',
      schemaVersion: 'v4.0',
      createdAt: now,
      updatedAt: now,
      ...logData
    };

    await databaseV4.putDoc(log);
    return await databaseV4.getDoc<StockCountLogDocument>(logId);
  } catch (error) {
    console.error('[createStockCountLog] Error creating stock count log:', error);
    throw error;
  }
}

export async function getStockCountLogsByStockItem(stockItemId: string, limit?: number): Promise<StockCountLogDocument[]> {
  try {
    const result = await databaseV4.findDocs<StockCountLogDocument>({
      selector: {
        type: 'stock_count_log',
        stockItemId: stockItemId
      },
      sort: [{ type: 'asc' }, { stockItemId: 'asc' }, { createdAt: 'desc' }],
      use_index: ['log-type-stockitem-date-idx', 'log-type-stockitem-date-idx'],
      limit: limit || 100
    });
    return result.docs || [];
  } catch (error) {
    console.error('[getStockCountLogsByStockItem] Error fetching stock count logs:', error);
    throw error;
  }
}

// ===== GENERAL LOG OPERATIONS =====

export async function getAllLogsByStockItem(stockItemId: string, limit?: number): Promise<InventoryLogDocument[]> {
  try {
    const result = await databaseV4.findDocs<InventoryLogDocument>({
      selector: {
        type: {
          $in: ['purchase_log', 'waste_log', 'consumption_log', 'stock_adjustment_log', 'stock_count_log']
        },
        stockItemId: stockItemId
      },
      sort: [{ stockItemId: 'asc' }, { createdAt: 'desc' }],
      use_index: ['log-stockitem-date-idx', 'log-stockitem-date-idx'],
      limit: limit || 200
    });
    return result.docs || [];
  } catch (error) {
    console.error('[getAllLogsByStockItem] Error fetching all logs by stock item:', error);
    throw error;
  }
}

export async function getLogsByDateRange(startDate: string, endDate: string, logTypes?: string[]): Promise<InventoryLogDocument[]> {
  try {
    const selector: any = {
      createdAt: {
        $gte: startDate,
        $lte: endDate
      }
    };

    if (logTypes && logTypes.length > 0) {
      selector.type = { $in: logTypes };
    } else {
      selector.type = {
        $in: ['purchase_log', 'waste_log', 'consumption_log', 'stock_adjustment_log', 'stock_count_log']
      };
    }

    const result = await databaseV4.findDocs<InventoryLogDocument>({
      selector,
      sort: [{ createdAt: 'desc' }],
      limit: 500
    });
    return result.docs || [];
  } catch (error) {
    console.error('[getLogsByDateRange] Error fetching logs by date range:', error);
    throw error;
  }
}

// ===== UTILITY FUNCTIONS =====

export async function deleteInventoryLog(logId: string): Promise<boolean> {
  try {
    const log = await databaseV4.getDoc<InventoryLogDocument>(logId);
    await databaseV4.deleteDoc(logId, log._rev!);
    return true;
  } catch (error) {
    console.error('[deleteInventoryLog] Error deleting inventory log:', error);
    throw error;
  }
}

export async function updateInventoryLog<T extends InventoryLogDocument>(logId: string, updates: Partial<T>): Promise<T> {
  try {
    const log = await databaseV4.getDoc<T>(logId);
    const updatedLog = {
      ...log,
      ...updates,
      updatedAt: new Date().toISOString()
    };
    await databaseV4.putDoc(updatedLog);
    return await databaseV4.getDoc<T>(logId);
  } catch (error) {
    console.error('[updateInventoryLog] Error updating inventory log:', error);
    throw error;
  }
} 