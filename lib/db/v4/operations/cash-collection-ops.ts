"use client";

import { databaseV4 } from '../core/db-instance';
import { CashCollectionDocument, PendingCollection } from '../schemas/cash-collection-schema';
import { getAllOrders } from './order-ops';
import { createCashTransaction } from './cash-ops';

/**
 * Get all cash collections
 */
export async function getAllCashCollections(): Promise<CashCollectionDocument[]> {
  try {
    const result = await databaseV4.findDocs<CashCollectionDocument>({
      selector: { 
        type: 'cash_collection'
      }
    });

    return (result.docs || [])
      .sort((a: CashCollectionDocument, b: CashCollectionDocument) => 
        new Date(b.collectedAt).getTime() - new Date(a.collectedAt).getTime()
      );
  } catch (error) {
    console.error('Error getting cash collections:', error);
    return [];
  }
}

/**
 * Get pending cash collections for staff drivers
 */
export async function getPendingCollections(): Promise<PendingCollection[]> {
  try {
    // Get all completed delivery orders that haven't been collected yet
    const orders = await getAllOrders();
    const deliveryOrders = orders.filter(order => 
      order.orderType === 'delivery' && 
      order.status === 'completed' && 
      order.paymentStatus === 'paid' &&
      order.deliveryPerson?.type === 'staff' &&
      order.deliveryPerson?.staffId
    );

    // Get existing collections to exclude already collected orders
    const collections = await getAllCashCollections();
    const collectedOrderIds = new Set(
      collections.flatMap(collection => collection.orderIds)
    );

    // Filter out already collected orders
    const uncollectedOrders = deliveryOrders.filter(order => 
      !collectedOrderIds.has(order._id)
    );

    // Group by staff driver
    const pendingByStaff = new Map<string, {
      staffId: string;
      staffName: string;
      orders: typeof uncollectedOrders;
      totalExpected: number;
    }>();

    uncollectedOrders.forEach(order => {
      const staffId = order.deliveryPerson!.staffId!;
      const staffName = order.deliveryPerson!.name;
      
      if (!pendingByStaff.has(staffId)) {
        pendingByStaff.set(staffId, {
          staffId,
          staffName,
          orders: [],
          totalExpected: 0
        });
      }

      const pending = pendingByStaff.get(staffId)!;
      pending.orders.push(order);
      // For cash payments, the expected amount is the received amount or total
      const expectedFromOrder = order.paymentDetails?.receivedAmount || order.total;
      pending.totalExpected += expectedFromOrder;
    });

    // Convert to PendingCollection array
    return Array.from(pendingByStaff.values()).map(pending => ({
      staffId: pending.staffId,
      staffName: pending.staffName,
      totalExpected: pending.totalExpected,
      orderCount: pending.orders.length,
      orderIds: pending.orders.map(order => order._id),
      oldestOrderDate: pending.orders.reduce((oldest, order) => 
        order.createdAt < oldest ? order.createdAt : oldest, 
        pending.orders[0]?.createdAt || new Date().toISOString()
      )
    }));
  } catch (error) {
    console.error('Error getting pending collections:', error);
    return [];
  }
}

/**
 * Create a cash collection record with enhanced transaction details
 */
export async function createCashCollection(
  staffId: string,
  staffName: string,
  expectedAmount: number,
  actualAmount: number,
  orderIds: string[],
  collectedBy: string,
  discrepancyReason?: string,
  notes?: string
): Promise<CashCollectionDocument> {
  try {
    const discrepancy = actualAmount - expectedAmount;
    const id = `cash_collection:${Date.now()}`;
    
    // Get order details for enhanced transaction description
    const orders = await getAllOrders();
    const collectedOrders = orders.filter(order => orderIds.includes(order._id));
    
    // Create detailed order summary for transaction
    const orderSummary = collectedOrders.map(order => ({
      id: order._id,
      total: order.total,
      items: order.items.map(item => `${item.quantity}x ${item.name}`).join(', '),
      orderType: order.orderType,
      customer: order.customer?.name || 'Client',
      createdAt: order.createdAt
    }));
    
    const collection: CashCollectionDocument = {
      _id: id,
      type: 'cash_collection',
      staffId,
      staffName,
      expectedAmount,
      actualAmount,
      discrepancy,
      discrepancyReason,
      orderIds,
      collectedAt: new Date().toISOString(),
      collectedBy,
      notes,
      status: discrepancy === 0 ? 'collected' : 'discrepancy_resolved'
    };

    await databaseV4.putDoc(collection);

    // 🚀 ENHANCED: Register the cash collection with detailed order information
    await createCashTransaction({
      type: 'manual_in',
      amount: actualAmount,
      description: `Collecte livreur: ${staffName} (${orderIds.length} livraison${orderIds.length > 1 ? 's' : ''})${discrepancy !== 0 ? ` - Écart: ${discrepancy}` : ''}`,
      time: new Date().toISOString(),
      performedBy: collectedBy,
      relatedDocId: id,
      // 🚀 NEW: Enhanced metadata for transaction history
      metadata: {
        transactionCategory: 'delivery_collection',
        staffId,
        staffName,
        orderCount: orderIds.length,
        orderIds,
        orderSummary,
        expectedAmount,
        actualAmount,
        discrepancy,
        discrepancyReason
      }
    });

    return collection;
  } catch (error) {
    console.error('Error creating cash collection:', error);
    throw error;
  }
}

/**
 * Get cash collections for a specific staff member
 */
export async function getStaffCashCollections(staffId: string): Promise<CashCollectionDocument[]> {
  try {
    const collections = await getAllCashCollections();
    return collections.filter(collection => collection.staffId === staffId);
  } catch (error) {
    console.error('Error getting staff cash collections:', error);
    return [];
  }
}

/**
 * Update cash collection status
 */
export async function updateCashCollectionStatus(
  collectionId: string, 
  status: 'pending' | 'collected' | 'discrepancy_resolved',
  notes?: string
): Promise<CashCollectionDocument> {
  try {
    const collection = await databaseV4.getDoc<CashCollectionDocument>(collectionId);
    
    const updatedCollection = {
      ...collection,
      status,
      notes: notes || collection.notes
    };

    await databaseV4.putDoc(updatedCollection);
    return updatedCollection;
  } catch (error) {
    console.error('Error updating cash collection status:', error);
    throw error;
  }
} 