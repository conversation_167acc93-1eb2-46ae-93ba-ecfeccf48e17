import { getPouchDB } from '@/lib/db/pouchdb-instance';
import { PrinterConfig } from '@/lib/services/kitchen-print-service';

const PRINTER_SETTINGS_DOC_ID = 'printer_settings';

interface PrinterSettingsDoc {
  _id: string;
  _rev?: string;
  printers: PrinterConfig[];
}

export const savePrinterSettings = async (printers: PrinterConfig[]): Promise<void> => {
  const db = getPouchDB();

  // Retry logic to handle document conflicts
  let retries = 3;
  while (retries > 0) {
    try {
      const doc = await db.get<PrinterSettingsDoc>(PRINTER_SETTINGS_DOC_ID).catch(err => {
        if (err.name === 'not_found') {
          return {
            _id: PRINTER_SETTINGS_DOC_ID,
            printers: [],
          };
        }
        throw err;
      });

      await db.put({
        ...doc,
        printers,
      });

      console.log('✅ Printer settings saved successfully');
      return; // Success, exit retry loop

    } catch (error: any) {
      if (error.name === 'conflict' && retries > 1) {
        console.warn(`⚠️ Document conflict, retrying... (${retries - 1} attempts left)`);
        retries--;
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 100));
        continue;
      }

      console.error('Failed to save printer settings:', error);
      throw error;
    }
  }
};

export const loadPrinterSettings = async (): Promise<PrinterConfig[]> => {
  const db = getPouchDB();
  try {
    const doc = await db.get<PrinterSettingsDoc>(PRINTER_SETTINGS_DOC_ID);
    return doc.printers || [];
  } catch (err) {
    if (err.name === 'not_found') {
      return []; // No settings saved yet
    }
    console.error('Failed to load printer settings:', err);
    throw err;
  }
};
