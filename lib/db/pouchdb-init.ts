"use client";

import { isElectronEnvironment } from './electron-db';

/**
 * Initialize PouchDB in the client environment
 */
export const initPouchDB = async () => {
  // Only run in the browser
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    // Enhanced environment logging for debugging electron detection issues
    const electronFlags = {
      isDesktopApp: isElectronEnvironment(),
      IS_DESKTOP_APP: typeof window !== 'undefined' ? Boolean((window as any).IS_DESKTOP_APP) : false,
      electronAPI: typeof window !== 'undefined' ? Boolean((window as any).electronAPI) : false,
      process: typeof window !== 'undefined' && (window as any).process ? Boolean((window as any).process.versions?.electron) : false
    };
    
    console.log('🔍 PouchDB Init - Environment Detection:', electronFlags);
    
    // Check if PouchDB is already initialized
    if ((window as any).PouchDB) {
      console.log('✅ PouchDB already loaded');
      return (window as any).PouchDB;
    }
    
    // Check if we're in Electron environment
    const isDesktopApp = electronFlags.isDesktopApp;
    
    // Dynamically import PouchDB only on the client side
    let PouchDBModule;
    try {
      // Import core PouchDB - works in both environments
      // For the renderer, 'pouchdb-browser' might be more lightweight if only browser features or IPC is used.
      // However, 'pouchdb' package itself is fine and will use appropriate browser adapters if not in Node.
      PouchDBModule = await import('pouchdb'); 
      // Handle ESM or CommonJS format
      const PouchDB = PouchDBModule.default || PouchDBModule;
      
      // Import and apply pouchdb-find
      const PouchDBFindModule = await import('pouchdb-find');
      const PouchDBFind = PouchDBFindModule.default || PouchDBFindModule;
      PouchDB.plugin(PouchDBFind);
      
      // Store PouchDB on window for reuse
      (window as any).PouchDB = PouchDB;
      
      if (isDesktopApp) {
        console.log('🔄 PouchDB library loaded in Electron renderer. DB operations will use IPC to CouchDB.');
      } else {
        console.log('🌐 PouchDB library loaded in browser mode. Default adapter (IndexedDB) will be used.');
        // For browser, PouchDB will automatically use IndexedDB or WebSQL if available.
      }
      
      return PouchDB;
    } catch (error) {
      console.error('❌ Failed to load PouchDB:', error);
      return null;
    }
  } catch (error) {
    console.error('❌ Error initializing PouchDB:', error);
    return null;
  }
}; 