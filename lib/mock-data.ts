/**
 * Type definitions and mock data for the application
 */

export interface Table {
  id: string
  name: string
  seats: number
  position: { x: number; y: number }
}

export interface MenuItem {
  id: string
  name: string
  prices: Record<string, number>
  image?: string
}

export interface Addon {
  id: string
  name: string
  price: number
}

export interface Category {
  _id: string;
  _rev?: string;
  name: string;
  emoji: string;
  items: MenuItem[];
  sizes?: string[];
  addons?: Addon[];
  type: 'category';
  userId: string;
  userEmail: string;
}

export const mockTables: Table[] = [
  {
    id: "T1",
    name: "Table 1",
    seats: 4,
    position: { x: 0, y: 0 },
  },
  {
    id: "T2",
    name: "Table 2",
    seats: 2,
    position: { x: 1, y: 0 },
  },
  {
    id: "T3",
    name: "Table 3",
    seats: 6,
    position: { x: 0, y: 1 },
  },
  {
    id: "T4",
    name: "Table 4",
    seats: 8,
    position: { x: 1, y: 1 },
  },
]

export const mockCategories: Category[] = [
  {
    _id: 'cat_1',
    name: 'Beverages',
    emoji: '🥤',
    type: 'category',
    userId: 'user_1',
    userEmail: '<EMAIL>',
    items: [
      {
        id: 'item_1',
        name: 'Coffee',
        prices: { small: 2.5, medium: 3.0, large: 3.5 }
      },
      {
        id: 'item_2',
        name: 'Tea',
        prices: { small: 2.0, medium: 2.5, large: 3.0 }
      }
    ],
    sizes: ['small', 'medium', 'large'],
    addons: [
      { id: 'addon_1', name: 'Sugar', price: 0 },
      { id: 'addon_2', name: 'Milk', price: 0.5 }
    ]
  },
  {
    _id: 'cat_2',
    name: 'Food',
    emoji: '🍔',
    type: 'category',
    userId: 'user_1',
    userEmail: '<EMAIL>',
    items: [
      {
        id: 'item_3',
        name: 'Burger',
        prices: { default: 8.99 }
      },
      {
        id: 'item_4',
        name: 'Sandwich',
        prices: { default: 6.99 }
      }
    ]
  }
]; 