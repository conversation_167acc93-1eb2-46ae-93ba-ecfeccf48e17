/**
 * P2P Sync Utilities
 */

/**
 * Generate connection info data for QR code scanning
 * This function creates the data needed to be encoded in a QR code
 * for mobile devices to scan and connect to this device
 */
export function generateConnectionQRData(
  ip: string,
  port: number,
  deviceId: string,
  hostname?: string,
  restaurantId?: string
): string {
  const connectionInfo: { ip: string; port: number; deviceId: string; hostname: string; restaurantId?: string; timestamp: number } = {
    ip,
    port,
    deviceId,
    hostname: hostname || 'P2P Device',
    restaurantId,
    timestamp: Date.now()
  };
  
  return JSON.stringify(connectionInfo);
}

/**
 * Format bytes to human-readable size
 */
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
} 