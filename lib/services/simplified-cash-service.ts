'use client';

import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import {
  getAllCashTransactions,
  getAllCashCounts,
  createCashCount,
  createCashTransaction
} from '@/lib/db/v4';

// Type definitions for the simplified cash service
export interface CashCount {
  id: string;
  countedAmount: number;
  expectedAmount: number;
  variance: number;
  countedAt: string;
  countedBy: string;
  notes?: string;
}

export interface CashTransaction {
  id: string;
  type: 'sales' | 'expense' | 'manual_in' | 'order' | 'count';
  amount: number;
  relatedDocId?: string;
  description: string;
  time: string;
  performedBy: string;
}

// Hook for simplified cash management
export function useSimplifiedCash() {
  const [transactions, setTransactions] = useState<CashTransaction[]>([]);
  const [cashCounts, setCashCounts] = useState<CashCount[]>([]);
  const [lastCashCount, setLastCashCount] = useState<CashCount | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, user } = useAuth();
  const { isReady: dbReady } = useUnifiedDB();

  // Load all cash transactions
  const loadTransactions = useCallback(async () => {
    if (!dbReady || !isAuthenticated) return;
    
    setLoading(true);
    try {
      // Get all transactions from the database
      const allTransactions = await getAllCashTransactions();
      
      // Map transactions to our simplified format
      const mappedTransactions = allTransactions.map((tx: any) => ({
        id: tx._id,
        type: tx.type,
        amount: tx.amount,
        relatedDocId: tx.relatedDocId,
        description: tx.description,
        time: tx.time,
        performedBy: tx.performedBy
      }));
      
      setTransactions(mappedTransactions);
    } catch (err) {
      console.error('Error loading cash transactions:', err);
      setError('Failed to load transactions');
    } finally {
      setLoading(false);
    }
  }, [dbReady, isAuthenticated]);

  // Load all cash counts
  const loadCashCounts = useCallback(async () => {
    if (!dbReady || !isAuthenticated) return;
    
    setLoading(true);
    try {
      // Get all cash counts from the database
      const allCounts = await getAllCashCounts();
      
      // Map counts to our simplified format
      const mappedCounts = allCounts.map((count: any) => ({
        id: count._id,
        countedAmount: count.countedAmount,
        expectedAmount: count.expectedAmount,
        variance: count.variance,
        countedAt: count.countedAt,
        countedBy: count.countedBy,
        notes: count.notes
      }));
      
      // Sort by date, newest first
      mappedCounts.sort((a: any, b: any) => new Date(b.countedAt).getTime() - new Date(a.countedAt).getTime());
      
      setCashCounts(mappedCounts);
      
      // Set the last cash count
      if (mappedCounts.length > 0) {
        setLastCashCount(mappedCounts[0]);
      }
    } catch (err) {
      console.error('Error loading cash counts:', err);
      setError('Failed to load cash counts');
    } finally {
      setLoading(false);
    }
  }, [dbReady, isAuthenticated]);

  // Refresh all data
  const refreshData = useCallback(async () => {
    if (dbReady && isAuthenticated) {
      await loadTransactions();
      await loadCashCounts();
    }
  }, [dbReady, isAuthenticated, loadTransactions, loadCashCounts]);

  // Load data on component mount
  useEffect(() => {
    refreshData();
  }, [refreshData]);

  // Calculate current cash balance
  const calculateCashBalance = useCallback(() => {
    return transactions.reduce((total, tx) => total + tx.amount, 0);
  }, [transactions]);

  // Calculate cash in/out totals
  const calculateCashTotals = useCallback(() => {
    const cashIn = transactions
      .filter(tx => tx.amount > 0)
      .reduce((sum, tx) => sum + tx.amount, 0);

    const cashOut = transactions
      .filter(tx => tx.amount < 0)
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);

    return { cashIn, cashOut };
  }, [transactions]);

  // Record a cash count
  const recordCashCount = async (countedAmount: number, notes?: string) => {
    if (!dbReady || !isAuthenticated || !user) {
      setError('Not authenticated or database not ready');
      return false;
    }

    setLoading(true);
    try {
      // Calculate the expected amount
      const expectedAmount = calculateCashBalance();
      const variance = countedAmount - expectedAmount;

      // Create the cash count record
      const countRecord = await createCashCount({
        countedAmount,
        expectedAmount,
        variance,
        countedAt: new Date().toISOString(),
        countedBy: user.name || 'Unknown User',
        notes
      });

      // We no longer create a cash transaction for the count variance.
      // The variance is recorded in the cash count document itself.
      // The cash balance should only be affected by actual cash movements (sales, expenses, manual in/out).
      // if (variance !== 0) {
      //   await createCashTransaction({
      //     type: 'count',
      //     amount: variance, // Positive if counted more than expected, negative if less
      //     description: `Ajustement de caisse (Comptage: ${countedAmount}, Attendu: ${expectedAmount})`,
      //     time: new Date().toISOString(),
      //     performedBy: user.name || 'Unknown User'
      //   });
      // }

      // Refresh data
      await refreshData();
      return true;
    } catch (err) {
      console.error('Error recording cash count:', err);
      setError('Failed to record cash count');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Add a cash transaction (manual in/out)
  const addCashTransaction = async (
    type: 'manual_in' | 'manual_out',
    amount: number,
    description: string
  ) => {
    if (!dbReady || !isAuthenticated || !user) {
      setError('Not authenticated or database not ready');
      return false;
    }

    setLoading(true);
    try {
      // Only allow negative amounts for manual_out
      const adjustedAmount = type === 'manual_out'
        ? -Math.abs(amount)
        : Math.abs(amount);

      await createCashTransaction({
        type,
        amount: adjustedAmount,
        description,
        time: new Date().toISOString(),
        performedBy: user.name || 'Unknown User'
      });

      // Refresh data
      await refreshData();
      return true;
    } catch (err) {
      console.error('Error adding cash transaction:', err);
      setError('Failed to add transaction');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    transactions,
    cashCounts,
    lastCashCount,
    loading,
    error,
    calculateCashBalance,
    calculateCashTotals,
    recordCashCount,
    addCashTransaction,
    refreshData
  };
}
