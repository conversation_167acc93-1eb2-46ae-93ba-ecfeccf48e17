// Empty MongoDB client for web builds
// This file replaces mongodb when building for web target

const clientPromise = Promise.reject(new Error('MongoDB not available in web build'));

export const isMongoDBReachable = async () => {
  return false; // Always return false for web builds
};

// Export ObjectId for compatibility
export const ObjectId = class {
  constructor(id) {
    this.id = id;
  }
};

// Export MongoClient for compatibility
export const MongoClient = class {
  static connect() {
    throw new Error('MongoDB not available in web build');
  }
};

export default clientPromise;