// Empty auth operations for web builds
// This file replaces mongo-auth-ops when building for web target

export const createMongoUser = async () => {
  throw new Error('Auth operations not available in web build');
};

export const updateMongoUserCredentials = async () => {
  throw new Error('Auth operations not available in web build');
};

export const getMongoUserById = async () => {
  throw new Error('Auth operations not available in web build');
};

export const getMongoUserByUsername = async () => {
  throw new Error('Auth operations not available in web build');
};

export const getMongoUserByEmail = async () => {
  throw new Error('Auth operations not available in web build');
};

// Export any other functions that might be imported
export default {
  createMongoUser,
  updateMongoUserCredentials,
  getMongoUserById,
  getMongoUserByUsername,
  getMongoUserByEmail
};