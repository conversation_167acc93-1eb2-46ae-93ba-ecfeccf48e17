// Empty service for web builds (landing page only)
// This file replaces restaurant functionality that web builds don't need

// Empty service class for kitchen printing
export class KitchenPrintService {
  constructor() {
    console.warn('Kitchen print service not available in web builds');
  }
  
  // Stub methods that return empty/default values
  setSystem() { return; }
  getSystem() { return 'single'; }
  setPrinters() { return; }
  getPrinters() { return []; }
  async printKitchenOrder() { return { success: false, error: 'Not available in web build' }; }
  async printReceipt() { return { success: false, error: 'Not available in web build' }; }
  resetPrinters() { return; }
  async forceRefreshPrinters() { return; }
  addOSPrinter() { return { success: false, message: 'Not available in web build' }; }
  removePrinter() { return { success: false, message: 'Not available in web build' }; }
  getPrinterStatus() { return { system: 'single', printers: [], totalPrinters: 0 }; }
}

// Empty barcode service
export class BarcodeService {
  constructor() {
    console.warn('Barcode service not available in web builds');
  }
  
  async generateBarcode() { 
    return { 
      dataUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==', 
      width: 100, 
      height: 50 
    }; 
  }
  async generateBarcodes() { return []; }
  async generateKitchenBarcode() { return this.generateBarcode(); }
  async generateKitchenBarcodeDataURL() { 
    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=='; 
  }
  async testBarcodeGeneration() { console.warn('Barcode test not available in web builds'); }
  async validateBarcodeValue() { return false; }
}

// Export singleton instances
export const kitchenPrintService = new KitchenPrintService();
export const barcodeService = new BarcodeService();

// Default export for compatibility
export default {
  KitchenPrintService,
  BarcodeService,
  kitchenPrintService,
  barcodeService,
}; 