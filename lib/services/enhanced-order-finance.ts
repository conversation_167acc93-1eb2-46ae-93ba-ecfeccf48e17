'use client';

import { updateOrder, processOrderPayment as v4ProcessOrderPayment } from '@/lib/db/v4/operations/order-ops';
import { createCashTransaction } from '@/lib/db/v4';
import { createExpense } from '@/lib/db/v4/operations/expense-ops';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useState } from 'react';
import { OrderDocument } from '@/lib/db/v4/schemas/order-schema';

export interface OrderPaymentResult {
  orderId: string;
  success: boolean;
  error?: string;
  registeredInCaisse: boolean;
  freelancePaymentProcessed?: boolean;
}

export interface UseEnhancedOrderFinanceReturn {
  processOrderPayment: (order: OrderDocument, paymentMethod: 'cash' | 'card' | 'online', receivedAmount?: number) => Promise<OrderPaymentResult>;
  isProcessing: boolean;
  lastResult: OrderPaymentResult | null;
}

/**
 * Enhanced hook to integrate order payments with delivery driver management
 * Handles both staff drivers (cash collection tracking) and freelance drivers (immediate payment)
 */
export function useEnhancedOrderFinance(): UseEnhancedOrderFinanceReturn {
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastResult, setLastResult] = useState<OrderPaymentResult | null>(null);
  const { user } = useAuth();

  /**
   * Process an order payment with enhanced delivery driver handling
   * @param order The order document to process
   * @param paymentMethod The payment method used (cash, card, online)
   * @param receivedAmount The amount received from the customer (for cash payments)
   * @returns A promise with the result of the payment processing
   */
  const processOrderPayment = async (
    order: OrderDocument,
    paymentMethod: 'cash' | 'card' | 'online',
    receivedAmount?: number
  ): Promise<OrderPaymentResult> => {
    setIsProcessing(true);
    try {
      // 1. Update order payment status and details using v4 logic
      await v4ProcessOrderPayment(
        order._id,
        paymentMethod,
        typeof receivedAmount === 'number' ? receivedAmount : order.total,
        receivedAmount
      );

      // 2. Register the payment in caisse ONLY for non-delivery orders
      // 🔧 FIXED: Delivery money never goes to drawer - it's collected separately
      let registeredInCaisse = false;
      
      if (order.orderType !== 'delivery') {
        // Only dine-in, takeaway, and table orders go to drawer
        try {
          console.log('🔍 Enhanced Finance: Creating cash transaction for non-delivery order:', {
            orderId: order._id,
            orderType: order.orderType,
            amount: typeof receivedAmount === 'number' ? receivedAmount : order.total,
            paymentMethod
          });
          
          // 🚀 ENHANCED: Create detailed order summary for transaction
          const orderSummary = {
            id: order._id,
            total: order.total,
            items: order.items.map(item => `${item.quantity}x ${item.name}`).join(', '),
            orderType: order.orderType,
            customer: order.customer?.name || 'Client',
            tableId: order.tableId,
            createdAt: order.createdAt
          };
          
          // Get order type label for description
          const orderTypeLabel = order.orderType === 'takeaway' ? 'Emporter' : 
                                order.orderType === 'dine-in' ? 'Sur place' : 
                                order.orderType === 'table' ? `Table ${order.tableId}` : 'Commande';
          
          await createCashTransaction({
            type: 'sales',
            amount: typeof receivedAmount === 'number' ? receivedAmount : order.total,
            description: `${orderTypeLabel}: ${order.customer?.name || 'Client'} - ${order.items.length} article${order.items.length > 1 ? 's' : ''}`,
            time: new Date().toISOString(),
            performedBy: user?.name || 'System',
            relatedDocId: order._id,
            // 🚀 NEW: Enhanced metadata for transaction history
            metadata: {
              transactionCategory: 'order_payment',
              orderType: order.orderType,
              orderSummary,
              paymentMethod,
              receivedAmount,
              change: receivedAmount ? Math.max(0, receivedAmount - order.total) : 0,
              itemCount: order.items.length,
              customer: order.customer?.name || 'Client',
              tableId: order.tableId
            }
          });
          
          console.log('🔍 Enhanced Finance: Cash transaction created successfully for non-delivery order');
          registeredInCaisse = true;
          
        } catch (caisseError) {
          console.error('🔍 Enhanced Finance: Error registering payment in caisse:', caisseError);
        }
      } else {
        // For delivery orders, just log that money is handled separately
        console.log(`🔍 Enhanced Finance: Delivery order ${order._id} payment NOT added to drawer - handled via collection system`);
      }

      // 3. Handle freelance driver payment based on payment model
      let freelancePaymentProcessed = false;
      if (order.orderType === 'delivery' && 
          order.deliveryPerson?.type === 'freelance' && 
          order.deliveryPerson?.paymentModel === 'prepaid' &&
          order.deliveryPerson?.freelancePayment && 
          !order.deliveryPerson?.isPaid) {
        
        try {
          const freelanceAmount = order.deliveryPerson.freelancePayment;
          
          // Create expense for freelance payment
          await createExpense({
            date: new Date().toISOString().split('T')[0],
            category: 'delivery_fees',
            description: `Freelance delivery payment - ${order.deliveryPerson.name} (Order ${order._id})`,
            amount: freelanceAmount,
            paymentMethod: 'cash',
            notes: `Freelance driver payment for order ${order._id}`,
            paymentSource: 'cash_register'
          });

          // Deduct from caisse (cash out)
          await createCashTransaction({
            type: 'expense',
            amount: -freelanceAmount, // Negative for cash out
            description: `Freelance delivery payment - ${order.deliveryPerson.name}`,
            time: new Date().toISOString(),
            performedBy: user?.name || 'System',
            relatedDocId: order._id
          });

          // Mark freelance driver as paid in the order
          await updateOrder(order._id, {
            deliveryPerson: {
              ...order.deliveryPerson,
              isPaid: true
            }
          });

          freelancePaymentProcessed = true;
          console.log(`✅ Freelance payment of ${freelanceAmount} processed for ${order.deliveryPerson.name}`);
        } catch (freelanceError) {
          console.error('Error processing freelance payment:', freelanceError);
          // Don't fail the entire payment process if freelance payment fails
        }
      }

      // 4. Handle collection-based drivers (staff + freelance collection model)
      // For collection-based drivers, initialize collection status if delivered
      if (order.orderType === 'delivery' && 
          ((order.deliveryPerson?.type === 'staff') || 
           (order.deliveryPerson?.type === 'freelance' && order.deliveryPerson?.paymentModel === 'collection'))) {
        
        try {
          // Import delivery operations
          const { updateDeliveryStatus } = await import('@/lib/db/v4/operations/order-ops');
          
          // Mark as delivered and initialize collection status
          await updateDeliveryStatus(order._id, 'delivered', {
            attemptedBy: order.deliveryPerson.name,
            notes: 'Order marked as delivered during payment processing'
          });
          
          console.log(`🔍 Enhanced Finance: Collection status initialized for ${order.deliveryPerson.type} driver ${order.deliveryPerson.name}`);
        } catch (collectionError) {
          console.error('Error initializing collection status:', collectionError);
          // Don't fail payment if collection status initialization fails
        }
      }

      const result: OrderPaymentResult = {
        orderId: order._id,
        success: true,
        registeredInCaisse,
        freelancePaymentProcessed
      };
      setLastResult(result);
      return result;
    } catch (error) {
      const result: OrderPaymentResult = {
        orderId: order._id,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        registeredInCaisse: false,
        freelancePaymentProcessed: false
      };
      setLastResult(result);
      return result;
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    processOrderPayment,
    isProcessing,
    lastResult
  };
} 