'use client';

import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import {
  getAllCashTransactions,
  getAllCashCounts,
  createCashCount,
  createCashTransaction,
  createExpense as v4CreateExpense,
  getAllExpenses as v4GetAllExpenses,
  updateExpense as v4UpdateExpense,
  deleteExpense as v4DeleteExpense,
  getAllOrders
} from '@/lib/db/v4';

// Type definitions for the finance service
export interface CashSession {
  id: string;
  status: 'open' | 'closed';
  openedAt: string;
  openedBy: string;
  closedAt?: string;
  closedBy?: string;
  openingAmount: number;
  closingAmount?: number;
  expectedAmount: number;
  discrepancy?: number;
  notes?: string;
}

export interface FinancialTransaction {
  id: string;
  sessionId?: string; // v4 cash-ops does not have session, keep for compatibility
  type: 'opening' | 'sales' | 'expense' | 'manual_in' | 'order' | 'count';
  amount: number;
  relatedDocId?: string;
  description: string;
  time: string;
  performedBy: string;
  metadata?: any;
}

export interface Expense {
  id: string;
  date: string;
  category: string;
  description: string;
  amount: number;
  paymentMethod: 'cash' | 'bank' | 'transfer' | 'credit';
  transactionId?: string;
  notes?: string;
  isRecurring?: boolean;
  frequency?: 'monthly' | 'quarterly' | 'yearly';
  nextDueDate?: string;
  isPaid?: boolean;
  paymentDate?: string;
  paymentSource?: 'cash_register' | 'capital' | 'bank_account';
}

export interface FinancialSummary {
  yearMonth: string;
  totalIncome: number;
  totalExpenses: number;
  netProfit: number;
  balanceInCaisse: number;
  pendingPayments: number;
  dailySummaries: {
    date: string;
    income: number;
    expenses: number;
    profit: number;
  }[];
}

// knowledge:start v4 direct implementation
// knowledge: v4 cash register logic (with UI compatibility stubs and safe signatures)
export function useCashRegister() {
  const [transactions, setTransactions] = useState<FinancialTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, user } = useAuth();

  const refreshData = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setTransactions([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      // Check if database is properly initialized
      const { databaseV4 } = await import('@/lib/db/v4/core/db-instance');
      
      if (!databaseV4.isInitialized || !databaseV4.getCurrentRestaurantId()) {
        console.warn('[FinanceService] Database not properly initialized, waiting...');
        
        try {
          const restaurantId = user.restaurantId;
          if (!restaurantId) {
            throw new Error('No restaurant ID available');
          }
          
          await databaseV4.waitForInitialization(restaurantId, 30000);
          console.log('[FinanceService] Database initialization completed');
        } catch (waitError) {
          console.error('[FinanceService] Database initialization failed:', waitError);
          setError('Database not ready - please refresh the page');
          setTransactions([]);
          return;
        }
      }

      const allTransactions = await getAllCashTransactions();
      console.log('🔍 Finance Service: Raw transactions from database:', allTransactions);
      
      setTransactions(
        Array.isArray(allTransactions)
          ? allTransactions.map((tx: any, i: number) => ({
              id: tx?._id || `tx_${i}_${Date.now()}`,
              type: tx?.type || 'manual_in',
              amount: typeof tx?.amount === 'number' ? tx.amount : 0,
              relatedDocId: tx?.relatedDocId || '',
              description: tx?.description || '',
              time: tx?.time || new Date().toISOString(),
              performedBy: tx?.performedBy || 'Unknown',
              sessionId: tx?.sessionId || undefined,
              metadata: tx?.metadata
            }))
          : []
      );
      
      console.log('🔍 Finance Service: Processed transactions:', transactions.length);
    } catch (err) {
      console.error('[FinanceService] Error loading cash transactions:', err);
      setError('Failed to load cash transactions');
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, user]);

  useEffect(() => {
      refreshData();
  }, [refreshData]);

  // Accepts (type, amount, description, performedBy?) for UI compatibility
  const addCashTransaction = async (
    type: 'manual_in' | 'manual_out',
    amount: number,
    description: string,
    performedBy?: string // optional, for UI compatibility
  ) => {
    if (!isAuthenticated || !user) {
      setError('Not authenticated or user not available');
      return false;
    }
    setLoading(true);
    try {
      // Only allow negative amounts for manual_out
      if (type === 'manual_out') {
        await createCashTransaction({
          type,
          amount: -Math.abs(amount),
          description,
          time: new Date().toISOString(),
          performedBy: performedBy || user.name || user.id || 'Unknown User',
        });
      } else {
        await createCashTransaction({
          type,
          amount: Math.abs(amount),
          description,
          time: new Date().toISOString(),
          performedBy: performedBy || user.name || user.id || 'Unknown User',
        });
      }
      await refreshData();
      return true;
    } catch (err) {
      setError('Failed to add transaction');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // No-op recordCashCount for UI compatibility
  const recordCashCount = async (..._args: any[]) => {
    // Not implemented in v4 cash register, only in simplified cash
    return false;
  };

  // Calculate the current cash balance (caisse)
  const calculateCashBalance = useCallback(() => {
    return transactions.reduce((total, tx) => total + (typeof tx.amount === 'number' ? tx.amount : 0), 0);
  }, [transactions]);

  const calculateSessionTotals = () => {
    if (!transactions.length) return { totalIn: 0, totalOut: 0 };
    const totalIn = transactions.filter(tx => tx.amount > 0).reduce((sum, tx) => sum + tx.amount, 0);
    const totalOut = transactions.filter(tx => tx.amount < 0).reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
    return { totalIn, totalOut };
  };

  // UI compatibility stubs
  const cashBalance = calculateCashBalance();
  const currentSession = {
    id: 'v4-session',
    status: 'open',
    openedAt: new Date().toISOString(),
    openedBy: user?.name || 'System',
    openingAmount: 0,
    expectedAmount: cashBalance,
    discrepancy: 0,
    notes: '',
  };
  const previousSessions: CashSession[] = [];

  return {
    transactions: Array.isArray(transactions) ? transactions : [],
    loading,
    error,
    addCashTransaction,
    refreshData,
    calculateSessionTotals,
    cashBalance,
    currentSession,
    previousSessions,
    recordCashCount, // no-op for UI compatibility
  };
}
// knowledge: v4 cash register logic (with UI compatibility stubs and safe signatures) end

// knowledge: v4 cash register logic end

// knowledge: v4 real expense CRUD in useExpenses
export function useExpenses() {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, user } = useAuth();
  const { isDbInitialized, isLoadingDb } = useUnifiedDB();

  const loadExpenses = useCallback(async () => {
    if (!isAuthenticated || !isDbInitialized || isLoadingDb) {
      setExpenses([]);
      if (!isLoadingDb) {
        setLoading(false);
      }
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const dbExpenses = await v4GetAllExpenses();
      setExpenses(Array.isArray(dbExpenses) ? dbExpenses : []);
    } catch (err) {
      console.error('[useExpenses] Failed to load expenses:', err);
      setError('Failed to load expenses');
      setExpenses([]);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, isDbInitialized, isLoadingDb]);

  useEffect(() => {
    if (!isLoadingDb && isDbInitialized) {
      loadExpenses();
    } else if (!isLoadingDb && !isDbInitialized && isAuthenticated) {
      setLoading(false);
    }
  }, [loadExpenses, isLoadingDb, isDbInitialized, isAuthenticated]);

  // Accept all possible arguments for UI compatibility
  const addExpense = async (
    date: string,
    category: string,
    description: string,
    amount: number,
    paymentMethod: 'cash' | 'bank' | 'transfer' | 'credit',
    notes?: string,
    isRecurring?: boolean,
    frequency?: 'monthly' | 'quarterly' | 'yearly',
    nextDueDate?: string,
    isPaid?: boolean,
    paymentSource?: 'cash_register' | 'capital' | 'bank_account',
    ..._rest: any[]
  ) => {
    if (!isAuthenticated || !user) {
      setError('Not authenticated or user not available');
      return false;
    }
    setLoading(true);
    try {
      await v4CreateExpense({
        date,
        category,
        description,
        amount,
        paymentMethod,
        notes,
        isRecurring,
        frequency,
        nextDueDate,
        isPaid,
        paymentSource,
        performedBy: user.name || user.id || 'Unknown User',
      });
      await loadExpenses();
      return true;
    } catch (err) {
      setError('Failed to add expense');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const updateExpense = async (id: string, updates: any) => {
    setLoading(true);
    try {
      await v4UpdateExpense(id, updates);
      await loadExpenses();
      return true;
    } catch (err) {
      setError('Failed to update expense');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const deleteExpense = async (id: string) => {
    setLoading(true);
    try {
      await v4DeleteExpense(id);
      await loadExpenses();
      return true;
    } catch (err) {
      setError('Failed to delete expense');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Recurring and advanced expense logic can be stubbed or left as TODO for now
  const addRecurringExpense = async (..._args: any[]) => {
    setError('Recurring expenses not implemented in v4 yet');
    return false;
  };
  const markExpenseAsPaid = async (..._args: any[]) => {
    setError('Marking expense as paid not implemented in v4 yet');
    return false;
  };
  const processRecurringExpenses = async (..._args: any[]) => {
    setError('Processing recurring expenses not implemented in v4 yet');
    return 0;
  };
  const getFrequencyLabel = (frequency?: 'monthly' | 'quarterly' | 'yearly') => {
    switch (frequency) {
      case 'monthly': return 'Mensuel';
      case 'quarterly': return 'Trimestriel';
      case 'yearly': return 'Annuel';
      default: return 'Périodique';
    }
  };

  return {
    expenses: Array.isArray(expenses) ? expenses : [],
    loading,
    error,
    refreshExpenses: loadExpenses,
    addExpense,
    updateExpense,
    deleteExpense,
    addRecurringExpense,
    markExpenseAsPaid,
    processRecurringExpenses,
    getFrequencyLabel
  };
}
// knowledge: v4 real expense CRUD in useExpenses end

// knowledge: v4 register order payment and add to caisse
export async function registerOrderPayment(
  orderId: string,
  amount: number, // This should be the total value of goods sold
  receivedAmount?: number, // This is the amount the customer paid
  cashier?: string
): Promise<boolean> {
  // Add a cash transaction of type 'sales' for the order
  try {
    // For 'sales' transactions, the amount should always be the actual value of the goods sold.
    // The 'receivedAmount' is for calculating change, not for recording sales value in the caisse.
    const salesAmount = amount; // Use the 'amount' parameter which represents the order total.

    // Use cashier if provided, otherwise 'System'
    const performedBy = cashier || 'System';
    await createCashTransaction({
      type: 'sales',
      amount: salesAmount,
      description: `Order payment for order ${orderId}`,
      time: new Date().toISOString(),
      performedBy,
      relatedDocId: orderId
    });
    return true;
  } catch (err) {
    // Optionally log error
    return false;
  }
}
// knowledge: v4 register order payment and add to caisse end

// knowledge: REMOVE useFinancialSummary (no summary, only real-time cash)
// (function removed)
// knowledge: REMOVE useFinancialSummary end
// knowledge: v4 migration end

// knowledge: v4 paid order income calculation start
/**
 * Get the total paid income from orders (v4 only)
 * Optionally accepts a date range (startDate, endDate in ISO format)
 */
export async function getTotalPaidIncomeV4(startDate?: string, endDate?: string): Promise<number> {
  // Fetch all orders
  const allOrders = await getAllOrders();
  // Filter for paid or partially paid orders
  const paidOrders = allOrders.filter(order =>
    (order.paymentStatus === 'paid' || order.paymentStatus === 'partially_paid') &&
    (!startDate || new Date(order.createdAt) >= new Date(startDate)) &&
    (!endDate || new Date(order.createdAt) <= new Date(endDate))
  );
  // Sum the paid amounts (prefer paymentDetails.amountPaid, fallback to order.total)
  const totalIncome = paidOrders.reduce((sum, order) => {
    const paid = order.paymentDetails?.amountPaid ?? order.total ?? 0;
    return sum + paid;
  }, 0);
  return totalIncome;
}
// knowledge: v4 paid order income calculation end
// knowledge:end v4 direct implementation