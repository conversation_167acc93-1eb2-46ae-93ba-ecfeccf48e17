'use client';

import { useState } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { createCashTransaction } from '@/lib/db/v4/operations/cash-ops';
import { addTransactionToSession } from '@/lib/db/v4/operations/cash-session-ops';
import { updateOrder } from '@/lib/db/v4/operations/order-ops';
import { OrderDocument } from '@/lib/db/v4/schemas/order-schema';
import { toast } from 'sonner';

export interface OrderPaymentResult {
  orderId: string;
  success: boolean;
  error?: string;
  registeredInCaisse: boolean;
  sessionActivated?: boolean;
  orderUpdated?: boolean;
}

export interface UseSimplifiedOrderFinanceReturn {
  processOrderPayment: (
    order: OrderDocument,
    paymentMethod: string,
    receivedAmount?: number
  ) => Promise<OrderPaymentResult>;
  isProcessing: boolean;
  lastResult: OrderPaymentResult | null;
}

/**
 * Simplified Order Finance Service
 * 
 * REFACTORED: Works with new session system
 * - Sessions always exist, become active with sales
 * - Transactions are automatically linked to current session
 * - Clean separation between delivery and drawer money
 * - FIXED: Now properly updates order status after payment
 */
export function useSimplifiedOrderFinance(): UseSimplifiedOrderFinanceReturn {
  const { user } = useAuth();
  const { isReady } = useUnifiedDB();
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastResult, setLastResult] = useState<OrderPaymentResult | null>(null);

  const processOrderPayment = async (
    order: OrderDocument,
    paymentMethod: string,
    receivedAmount?: number
  ): Promise<OrderPaymentResult> => {
    if (!isReady || !user) {
      const result: OrderPaymentResult = {
        orderId: order._id,
        success: false,
        error: 'Service not ready or user not authenticated',
        registeredInCaisse: false,
        orderUpdated: false
      };
      setLastResult(result);
      return result;
    }

    setIsProcessing(true);

    try {
      console.log(`[OrderFinance] Processing payment for order ${order._id}:`, {
        orderType: order.orderType,
        total: order.total,
        paymentMethod,
        receivedAmount
      });

      let registeredInCaisse = false;
      let sessionActivated = false;
      let orderUpdated = false;
      
      // 1. FIRST: Update the order status to completed and paid
      try {
        const actualAmount = typeof receivedAmount === 'number' ? receivedAmount : order.total;
        const change = receivedAmount ? Math.max(0, receivedAmount - order.total) : 0;
        
        await updateOrder(order._id, {
          status: 'completed',
          paymentStatus: 'paid',
          paymentMethod: paymentMethod as any,
          paymentDetails: {
            amountPaid: order.total,
            amountDue: 0,
            receivedAmount: actualAmount,
            change,
            paidAt: new Date().toISOString()
          }
        });
        
        console.log(`[OrderFinance] Order ${order._id} status updated to completed/paid`);
        orderUpdated = true;
      } catch (orderError) {
        console.error('[OrderFinance] Error updating order status:', orderError);
        throw new Error(`Failed to update order status: ${orderError instanceof Error ? orderError.message : 'Unknown error'}`);
      }
      
      // 2. SECOND: Register non-delivery orders in the cash drawer
      if (order.orderType !== 'delivery') {
        try {
          const orderTypeLabel = order.orderType === 'dine-in' ? 'Sur place' : 
                               order.orderType === 'takeaway' ? 'À emporter' : 
                               order.orderType === 'takeout' ? 'À emporter' :
                               order.orderType === 'table' ? 'Table' :
                               'Commande';

          const orderSummary = `${order.items.length} article${order.items.length > 1 ? 's' : ''}`;
          const actualAmount = typeof receivedAmount === 'number' ? receivedAmount : order.total;

          // Create the cash transaction
          const transaction = await createCashTransaction({
            type: 'sales',
            amount: actualAmount,
            description: `${orderTypeLabel}: ${order.customer?.name || 'Client'} - ${orderSummary}`,
            time: new Date().toISOString(),
            performedBy: user?.name || 'System',
            relatedDocId: order._id,
            metadata: {
              transactionCategory: 'order_payment',
              orderType: order.orderType,
              orderSummary,
              paymentMethod,
              receivedAmount,
              change: receivedAmount ? Math.max(0, receivedAmount - order.total) : 0,
              itemCount: order.items.length,
              customer: order.customer?.name || 'Client',
              tableId: order.tableId
            }
          });

          console.log(`[OrderFinance] Cash transaction created:`, transaction._id);

          // Link transaction to current session (session always exists now)
          try {
            await addTransactionToSession(transaction._id);
            console.log(`[OrderFinance] Transaction linked to session`);
            sessionActivated = true; // This sale might activate the session
          } catch (sessionError) {
            console.warn('[OrderFinance] Session linking failed (non-critical):', sessionError);
          }

          registeredInCaisse = true;
        } catch (caisseError) {
          // Log caisse registration error but continue since order is already updated
          console.error('[OrderFinance] Error registering payment in caisse (order still completed):', caisseError);
        }
      } else {
        // For delivery orders, just log that money is handled separately
        console.log(`[OrderFinance] Delivery order ${order._id} payment NOT added to drawer - handled via collection system`);
      }

      const result: OrderPaymentResult = {
        orderId: order._id,
        success: true,
        registeredInCaisse,
        sessionActivated,
        orderUpdated
      };
      setLastResult(result);
      return result;
    } catch (error) {
      console.error('[OrderFinance] Error processing payment:', error);
      const result: OrderPaymentResult = {
        orderId: order._id,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        registeredInCaisse: false,
        orderUpdated: false
      };
      setLastResult(result);
      return result;
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    processOrderPayment,
    isProcessing,
    lastResult
  };
}
