# DIY Auto-Update Implementation Guide 🚀

## Overview
This guide shows how to implement a DIY live updater for your Capacitor Android app using your VPS server.

## Architecture

```
Your VPS Server
├── Update API Endpoint
├── Version Check Endpoint  
├── ZIP Bundle Storage
└── Update Metadata

Your App
├── Update Check Logic
├── Download Manager
├── Bundle Extractor
└── WebView Reloader
```

## Implementation Steps

### 1. Server-Side Setup (VPS)

#### A. Create Update API Structure
```
/api/updates/
├── check-version          # GET - Check for updates
├── download/{version}      # GET - Download bundle
└── metadata/{version}     # GET - Get update info
```

#### B. Version Check Endpoint
```javascript
// /api/updates/check-version
{
  "currentVersion": "1.0.0",
  "latestVersion": "1.0.1", 
  "updateAvailable": true,
  "downloadUrl": "https://your-vps.com/api/updates/download/1.0.1",
  "mandatory": false,
  "checksum": "sha256-hash",
  "releaseNotes": "Bug fixes and improvements"
}
```

### 2. Client-Side Implementation

#### A. Install Required Plugins
```bash
npm install @capacitor/filesystem @capacitor/app
npx cap sync
```

#### B. Create Update Service
```typescript
// src/services/UpdateService.ts
import { Filesystem, Directory } from '@capacitor/filesystem';
import { App } from '@capacitor/app';

export class UpdateService {
  private readonly UPDATE_CHECK_URL = 'https://your-vps.com/api/updates/check-version';
  private readonly BUNDLE_DIR = 'updates';
  
  async checkForUpdates(): Promise<UpdateInfo | null> {
    try {
      const response = await fetch(this.UPDATE_CHECK_URL);
      const updateInfo = await response.json();
      
      if (updateInfo.updateAvailable) {
        return updateInfo;
      }
      return null;
    } catch (error) {
      console.error('Update check failed:', error);
      return null;
    }
  }
  
  async downloadUpdate(updateInfo: UpdateInfo): Promise<boolean> {
    try {
      // Download ZIP bundle
      const response = await fetch(updateInfo.downloadUrl);
      const arrayBuffer = await response.arrayBuffer();
      
      // Save to device
      const fileName = `update-${updateInfo.latestVersion}.zip`;
      await Filesystem.writeFile({
        path: `${this.BUNDLE_DIR}/${fileName}`,
        data: this.arrayBufferToBase64(arrayBuffer),
        directory: Directory.Data
      });
      
      // Verify checksum
      if (await this.verifyChecksum(fileName, updateInfo.checksum)) {
        await this.extractAndApply(fileName, updateInfo.latestVersion);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Download failed:', error);
      return false;
    }
  }
  
  private async extractAndApply(fileName: string, version: string): Promise<void> {
    // Extract ZIP and replace current bundle
    // Implementation depends on your ZIP extraction method
    
    // Store new version info
    await this.setCurrentVersion(version);
    
    // Schedule reload on next app start
    await this.scheduleReload();
  }
  
  async applyUpdate(): Promise<void> {
    // Reload the webview with new bundle
    window.location.reload();
  }
}
```

#### C. Integration in App
```typescript
// src/App.tsx or main component
import { UpdateService } from './services/UpdateService';

export function App() {
  const updateService = new UpdateService();
  
  useEffect(() => {
    // Check for updates on app start
    checkForUpdates();
    
    // Set up periodic checks
    const interval = setInterval(checkForUpdates, 30 * 60 * 1000); // 30 minutes
    
    return () => clearInterval(interval);
  }, []);
  
  const checkForUpdates = async () => {
    const updateInfo = await updateService.checkForUpdates();
    
    if (updateInfo) {
      if (updateInfo.mandatory) {
        // Force update
        await downloadAndApply(updateInfo);
      } else {
        // Show optional update dialog
        showUpdateDialog(updateInfo);
      }
    }
  };
  
  const downloadAndApply = async (updateInfo: UpdateInfo) => {
    const success = await updateService.downloadUpdate(updateInfo);
    if (success) {
      await updateService.applyUpdate();
    }
  };
}
```

### 3. Build & Deploy Process

#### A. Automated Build Script
```bash
#!/bin/bash
# build-and-deploy.sh

# Build the app
npm run build:static

# Create version info
VERSION=$(node -p "require('./package.json').version")
TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# Create ZIP bundle
cd out
zip -r "../update-${VERSION}.zip" .
cd ..

# Calculate checksum
CHECKSUM=$(shasum -a 256 "update-${VERSION}.zip" | cut -d' ' -f1)

# Upload to VPS
scp "update-${VERSION}.zip" your-vps:/path/to/updates/
ssh your-vps "echo '{\"version\":\"${VERSION}\",\"checksum\":\"${CHECKSUM}\",\"timestamp\":\"${TIMESTAMP}\"}' > /path/to/updates/${VERSION}.json"

# Update latest version pointer
ssh your-vps "echo '${VERSION}' > /path/to/updates/latest.txt"
```

### 4. Security Considerations

#### A. Checksum Verification
```typescript
private async verifyChecksum(fileName: string, expectedChecksum: string): Promise<boolean> {
  // Read file and calculate SHA-256
  const fileData = await Filesystem.readFile({
    path: `${this.BUNDLE_DIR}/${fileName}`,
    directory: Directory.Data
  });
  
  const calculatedChecksum = await this.calculateSHA256(fileData.data);
  return calculatedChecksum === expectedChecksum;
}
```

#### B. HTTPS Only
- Always use HTTPS for update endpoints
- Validate SSL certificates

#### C. Rollback Mechanism
```typescript
async rollbackToPrevious(): Promise<void> {
  const previousVersion = await this.getPreviousVersion();
  if (previousVersion) {
    await this.setCurrentVersion(previousVersion);
    await this.applyUpdate();
  }
}
```

## App Store Compliance ✅

### Google Play Store
- ✅ **Allowed**: JavaScript/HTML/CSS updates
- ❌ **Not Allowed**: Native code changes
- ✅ **Your case**: Static web content updates are fully compliant

### Implementation Notes
1. **Only update web assets** (HTML, CSS, JS)
2. **Don't change app functionality significantly**
3. **Maintain original app purpose**

## Estimated Development Time

- **Basic Implementation**: 2-3 days
- **Full Featured**: 1-2 weeks
- **Production Ready**: 2-3 weeks

## Pros vs Cons

### DIY Pros ✅
- Full control over process
- No monthly fees
- Custom features
- Uses your existing VPS
- Perfect for your offline-first architecture

### DIY Cons ❌
- Development time required
- Maintenance responsibility
- Security implementation needed
- Testing across devices

## Recommendation

For your use case, I'd suggest:

1. **Start with Capgo** ($12/month) - Get auto-updates working quickly
2. **Evaluate for 2-3 months** - See if it meets all needs
3. **Consider DIY later** - If you need more control or want to save costs

This gives you immediate auto-update capability while keeping the DIY option open for the future. 